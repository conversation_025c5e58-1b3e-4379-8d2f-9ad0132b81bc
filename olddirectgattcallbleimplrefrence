package com.bodymount.app.sensor

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import android.widget.Toast
import com.bodymount.app.common.CommonDataArea
import com.bodymount.app.common.CommonDataArea.Companion.bluetoothDevice
import com.bodymount.app.common.CommonDataArea.Companion.bluetoothGatt
import com.bodymount.app.common.CommonDataArea.Companion.bluetoothManager
import com.bodymount.app.common.CommonDataArea.Companion.isConnected
import com.bodymount.app.ui.dialogs.SensorListDialogManager
import com.bodymount.app.ui.dialogs.dialogHelper.SensorListItems
import com.bodymount.app.ui.dialogs.dialogHelper.SensorRecyclerViewAdapter
import com.bodymount.app.util.UUIDConfig
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.IOException
import java.nio.BufferUnderflowException
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.*

@SuppressLint("MissingPermission")
class BluetoothManager(
    private val context: Context,
    private val sensorItemsList: MutableList<SensorListItems>,
    private val adapter: SensorRecyclerViewAdapter,
    private val sensorListDialogManager: SensorListDialogManager,
) {
    private val bluetoothAdapter: BluetoothAdapter? by lazy { BluetoothAdapter.getDefaultAdapter() }
    private val reconnectionManager = BleReconnectionManager(context, this)
    private val discoveredDeviceAddresses = mutableSetOf<String>()
    var data: ByteArray? = null

    //  private var bluetoothGatt: BluetoothGatt? = null

    //reconnection
    private var isReconnecting = false
    private var retryCount = 0
    private val maxRetries = 20
    private val retryDelay = 5000L

    // CoroutineScope to launch coroutines
    private val reconnectionScope = CoroutineScope(Dispatchers.IO)
    private val TAG = "BMPMS_BLE"

    fun startBluetoothDiscovery(context: Context) {
        sensorListDialogManager.showLoadingIndicator()
        val discoveredDevices = mutableListOf<BluetoothDevice>()
        if (bluetoothAdapter?.isEnabled == true) {
            discoveredDevices.clear()
            val filter = IntentFilter(BluetoothDevice.ACTION_FOUND)
            context.registerReceiver(this.bluetoothReceiver, filter)
            // Start discovery
            bluetoothAdapter?.startDiscovery()
        } else {
            // Handle case when Bluetooth is not enabled
            showToast("Bluetooth is not enabled")
        }
    }

    private fun showToast(message: String) {

        Toast.makeText(CommonDataArea.curActivity, message, Toast.LENGTH_SHORT).show()

    }

    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {

            when (intent?.action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val device =
                        intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
                    val rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)
                    device?.let {
                        handleDiscoveredBluetoothDevice(device, rssi)
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission", "NotifyDataSetChanged")
    private fun handleDiscoveredBluetoothDevice(device: BluetoothDevice, rssi: Short) {
        val deviceName = device.name ?: "Unknown Device"
        val sharedPreferences = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
        val currentDevice = sharedPreferences.getString("device_name", CommonDataArea.BodyMountPMS)

        if (discoveredDeviceAddresses.contains(device.address)) {
            Log.d("BluetoothDiscovery", "Device already discovered: ${device.name}, MAC: ${device.address}")
            return // Skip if the device has already been processed
        }

        val isTargetDevice = when (currentDevice) {
            CommonDataArea.BodyMountPMS -> CommonDataArea.targetMacAddresses.contains(device.address)
            CommonDataArea.iEcgPatch -> deviceName.contains(CommonDataArea.iEcgPatch)
            else -> false
        }

        if (isTargetDevice) {
            processDiscoveredDevice(device, rssi)
        } else {
            Log.d(
                "BluetoothDiscovery",
                "Discovered unexpected device: $deviceName, MAC: ${device.address}, RSSI: $rssi dBm"
            )
        }
    }
    @SuppressLint("NotifyDataSetChanged")
    private fun processDiscoveredDevice(device: BluetoothDevice, rssi: Short) {
        sensorListDialogManager.hideLoadingIndicator()
        Log.d(
            "BluetoothDiscovery",
            "Discovered target device: ${device.name ?: "Unknown Device"}, MAC: ${device.address}, RSSI: $rssi dBm"
        )

        // Add device to discovered list
        discoveredDeviceAddresses.add(device.address)

        // Create and populate sensor list item
        val sensorListItem = SensorListItems(device).apply {
            setName(device.name ?: "Unknown Device")
            setAddress(device.address)
            setSignal(rssi.toDouble())
            // setConnectLoading(R.drawable.wifi_loading) // Uncomment if required
        }
        // Add to sensor list and notify adapter
        sensorItemsList.add(sensorListItem)
        adapter.notifyDataSetChanged()
    }


    @SuppressLint("MissingPermission")
    suspend fun connectToDevice(device: BluetoothDevice): Boolean {

        val connectionResult = CompletableDeferred<Boolean>()
        bluetoothGatt = device.connectGatt(context, false, gattCallBack(connectionResult))

        CommonDataArea.connectedDevices.add(device)

        return connectionResult.await()
    }

    private fun bondDevice(device: BluetoothDevice): Boolean {
        return try {
            val bondMethod = device.javaClass.getMethod("createBond")
            bondMethod.invoke(device) as Boolean
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    @SuppressLint("MissingPermission")
    fun isDeviceBonded(device: BluetoothDevice): Boolean {
        return device.bondState == BluetoothDevice.BOND_BONDED
    }
    private fun gattCallBack(connectionResult: CompletableDeferred<Boolean>) =
        object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
                if (newState == BluetoothProfile.STATE_CONNECTED) {
                    Log.i(TAG, "DeviceConnected")
                    gatt?.let { requestMtu(it, 517) }
                    connectionResult.complete(true)
                    CommonDataArea.deviceDisconnectionStatus = false
                    retryCount = 0

                    // Log successful connection for debugging
                    com.bodymount.app.common.LogWriter.writeLog("Device Connection", "Device successfully connected")

                    // Hide "No Device Connected" text when device connects
                    CoroutineScope(Dispatchers.Main).launch {
                        CommonDataArea.curActivity?.mainActivityUIs?.hideNoDeviceConnectedText()
                    }
                } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                    Log.i(TAG, "DeviceDisconnected - Status: $status")
                    connectionResult.complete(false)

                    // Set disconnection status immediately
                    CommonDataArea.deviceDisconnectionStatus = true

                    // Log disconnection event for debugging
                    com.bodymount.app.common.LogWriter.writeLog("Device Disconnection", "Device disconnected with status: $status")

                    CoroutineScope(Dispatchers.Main).launch {
                        // Clear UI components and reset values
                        CommonDataArea.curActivity?.mainActivityUIs?.clearOldChartComponents()
                        CommonDataArea.curActivity?.mainActivityUIs?.resetValues()
                        // Show no device connected text
                        CommonDataArea.curActivity?.mainActivityUIs?.showNoDeviceConnectedText()

                        com.bodymount.app.common.LogWriter.writeLog("UI Update", "UI cleared due to device disconnection")
                    }

                    //scheduleReconnection()
                    //retryReconnection(gatt?.device)
                    retryReconnection(CommonDataArea.bluetoothDevice)
                }
            }

            // Define service and characteristic UUIDs
            val serviceToCharacteristicsMap = mapOf(
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_1"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_DEVICE_INFO"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_2"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_ONE"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_TWO"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_THREE"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_3"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_PPG"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_SPO2"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_4"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_TEMP"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BATTERY"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_5"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_SOS"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_LEAD_OFF"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_BP"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_BP"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_BP_STATUS"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_ACTIVITY"])
                ),
                UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_ACTIVITY"]) to listOf(
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_1"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_2"]),
                    UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_3"])
                )
            )

            private var currentServiceIndex = 0
            private var currentCharacteristicIndex = 0
            private val servicesUUIDs = serviceToCharacteristicsMap.keys.toList()
            private lateinit var characteristicUUIDs: List<UUID>

            private fun setupNextCharacteristic(
                gatt: BluetoothGatt?,
                service: BluetoothGattService
            ) {
                if (currentCharacteristicIndex >= characteristicUUIDs.size) {
                    Log.i(TAG, "All characteristics set up for service: ${service.uuid}")
                    currentServiceIndex++
                    setupNextService(gatt)
                    return
                }

                val uuid = characteristicUUIDs[currentCharacteristicIndex]
                val characteristic = service.getCharacteristic(uuid)
                characteristic?.let { char ->
                    val canNotify = char.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY
                    if (canNotify != 0) {
                        val descriptor =
                            char.getDescriptor(UUID.fromString("00002902-0000-1000-8000-00805f9b34fb"))
                        descriptor?.let { desc ->
                            desc.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                            Log.i("BLE", "Writing descriptor for characteristic: $uuid")
                            gatt?.writeDescriptor(desc)
                        }
                    } else {
                        Log.i(TAG, "Characteristic $uuid does not support notification")
                        currentCharacteristicIndex++
                        setupNextCharacteristic(gatt, service)
                    }
                } ?: run {
                    Log.i(TAG, "Characteristic $uuid not found in service ${service.uuid}")
                    currentCharacteristicIndex++
                    setupNextCharacteristic(gatt, service)
                }
            }

            private fun setupNextService(gatt: BluetoothGatt?) {
                if (currentServiceIndex >= servicesUUIDs.size) {
                    Log.i(TAG, "All services and characteristics set up")
                    return
                }

                val serviceUUID = servicesUUIDs[currentServiceIndex]
                val service = gatt?.getService(serviceUUID)
                service?.let {
                    characteristicUUIDs = serviceToCharacteristicsMap[serviceUUID] ?: listOf()
                    currentCharacteristicIndex = 0
                    setupNextCharacteristic(gatt, service)
                } ?: run {
                    Log.i(TAG, "Service $serviceUUID not found")
                    currentServiceIndex++
                    setupNextService(gatt)
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.i(TAG, "ServiceDiscovered")
                    setupNextService(gatt)
                } else {
                    Log.i(TAG, "FailedToDiscoverServices: $status")
                }
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt?, descriptor: BluetoothGattDescriptor?, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    val characteristic = descriptor?.characteristic
                    gatt?.setCharacteristicNotification(characteristic, true)
                    Log.i(TAG, "Notification enabled for characteristic: ${characteristic?.uuid}")
                    currentCharacteristicIndex++
                    characteristic?.let { setupNextCharacteristic(gatt, it.service) }
                } else {
                    Log.e(
                        TAG,
                        "Failed to write descriptor for ${descriptor?.characteristic?.uuid}, status: $status"
                    )
                }
            }

            // Define a map to associate UUIDs with processing functions
            val characteristicHandlers = mapOf<UUID, (ByteArray) -> Unit>(
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_DEVICE_INFO"]) to { CommonDataArea.bluetoothDataParser.parseBmpmsDeviceInfo(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_ONE"]) to { CommonDataArea.bluetoothDataParser.processECGOneData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_TWO"]) to { CommonDataArea.bluetoothDataParser.processECGTwoData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ECG_THREE"]) to { CommonDataArea.bluetoothDataParser.processECGThreeData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_PPG"]) to { CommonDataArea.bluetoothDataParser.processReceivedPPGData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_SPO2"]) to { CommonDataArea.bluetoothDataParser.parseSpo2Data(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_TEMP"]) to { CommonDataArea.bluetoothDataParser.parseTempData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BATTERY"]) to { CommonDataArea.bluetoothDataParser.parseBatteryLevelData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_SOS"]) to { CommonDataArea.bluetoothDataParser.parseSosData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_LEAD_OFF"]) to { CommonDataArea.bluetoothDataParser.leadOffData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_1"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmEcgData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_2"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmRRData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_ACTIVITY_3"]) to { CommonDataArea.bluetoothDataParser.parseiRhythmHealthData(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_BP"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSBPParam(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_BP_STATUS"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSBPSTATUSParam(it) },
                UUID.fromString(UUIDConfig.characteristicMap["CHARACTERISTIC_BMPMS_ACTIVITY"]) to { CommonDataArea.bluetoothDataParser.parseBMPMSActivityParam(it) }
            )

            // Handle characteristic changes
            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, value: ByteArray) {
                characteristicHandlers[characteristic.uuid]?.invoke(value)
                    ?: Log.w(TAG, "No handler found for characteristic: ${characteristic.uuid}")
            }

            override fun onMtuChanged(gatt: BluetoothGatt, mtu: Int, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.i(TAG, "MTU changed to $mtu")
                    gatt?.discoverServices()
                } else {
                    Log.w(TAG, "MTU change unsuccessful: $status")
                }
            }
        }

    private fun requestMtu(gatt: BluetoothGatt, mtu: Int) {
        gatt.requestMtu(mtu)
    }
    fun receiveAndProcessData() {
        val receivedBuffer = ByteArray(268) // Adjust the buffer size as needed
        val receivedData: ByteArray = data ?: return

        val coroutineExceptionHandler = CoroutineExceptionHandler { _, throwable ->
            // Handle exceptions here
            Log.e("CoroutineExceptionHandler", "Exception: ${throwable.stackTraceToString()}")
            //   bluetoothDevice?.let { handleDisconnection(it) }
        }

        val coroutineScope = CoroutineScope(Dispatchers.IO + coroutineExceptionHandler)

        coroutineScope.launch {
            try {
                while (isConnected) {
                    // processInputStream(receivedData)
                }
            } catch (e: Exception) {
                Log.e("receiveAndProcessData", "Coroutine exception: ${e.message}")
                //  bluetoothDevice?.let { handleDisconnection(it) }
            }
        }
    }


    fun disconnect() {

        for (macAddress in CommonDataArea.targetMacAddresses) {
            if (discoveredDeviceAddresses.contains(macAddress)) {
                discoveredDeviceAddresses.remove(macAddress)
                // Handle disconnection-related tasks such as notifying the user or attempting reconnection.
                CommonDataArea.expectedDisconnect = true
                // context.unregisterReceiver(this.bluetoothReceiver)

            }
        }
        bluetoothDevice?.let { handleDisconnection(it) }

        CommonDataArea.manageSensorsDialog?.dismiss()
    }

//    private fun handleDisconnectionDueToError(e: IOException) {
//        Log.e("bt_socket_closed", "Bluetooth socket closed or error: ${e.message}")
//        bluetoothDevice?.let { handleDisconnection(it) }
//    }

    fun removeSensorName() {
        bluetoothDevice?.let {
            CommonDataArea.bluetoothConnectionHelper.enrollSensorNameByType(
                context,
                "",
                it
            )
        }
        CommonDataArea.deviceModelNumber = null
        isConnected = false

    }

    fun handleDisconnection(device: BluetoothDevice) {
        try {
            // Close the Bluetooth socket to release resources
//            CommonDataArea.bluetoothSocket.inputStream?.close()
//            CommonDataArea.bluetoothSocket.close()
            Log.d("managerObj", "postUiInitActions:$bluetoothManager ")

            bluetoothGatt?.let {
                it.disconnect()
                it.close()
                // bluetoothGatt = null
                isConnected = false

                Log.i(TAG, "DeviceDisconnected from")
            }
            CommonDataArea.connectedDevices.remove(bluetoothDevice)
            CommonDataArea.bluetoothDevice = null
            stopBluetoothDiscovery()

            //Reconnection
            if (!isConnected && !CommonDataArea.expectedDisconnect) {
                // scheduleReconnection()
            } else {
                Log.d(
                    TAG,
                    "Cant Reconnect |isConnected:$isConnected | expectedDisconnect:${CommonDataArea.expectedDisconnect}"
                )
            }

            removeSensorName()

        } catch (e: IOException) {
            // Handle any exceptions that occur while closing the socket
            e.printStackTrace()
            showToast("Disconnecting ${e.stackTraceToString()}")
        } finally {
            // Assuming isConnected is a Boolean flag indicating the connection status
            Log.d("handleDisconnection", "Bluetooth disconnected")

            // Use runOnUiThread to show the Toast message on the UI thread
            CoroutineScope(Dispatchers.Main).launch {
                CommonDataArea.curActivity?.mainActivityUIs?.clearChartComponents()
                CommonDataArea.curActivity?.mainActivityUIs?.showNoDeviceConnectedText()
                CommonDataArea.curActivity?.mainActivityUIs?.resetValues()
                CommonDataArea.curActivity?.mainActivityUIs?.clearOldChartComponents()
                CommonDataArea.curActivity?.dialog?.getPatchDisconnectionDialog(device.name)
                showToast("Device Disconnected")
            }
        }
    }

    @SuppressLint("MissingPermission")
    fun stopBluetoothDiscovery() {
        // Check if BluetoothAdapter is not null and if discovery is in progress
        if (bluetoothAdapter?.isDiscovering == true) {
            // Stop the discovery
            Log.d("bluetoothAdapter", "stopBluetoothDiscovery: ")
            bluetoothAdapter?.cancelDiscovery()
        }
        // Unregister the receiver
        //context.unregisterReceiver(bluetoothReceiver)
        // You can also hide the loading indicator if needed
        sensorListDialogManager.hideLoadingIndicator()
    }

    private fun retryReconnection(device: BluetoothDevice?) {
        if (!isReconnecting && retryCount < maxRetries && CommonDataArea.bluetoothDevice != null) {
            isReconnecting = true
            retryCount++

            // Delay before retrying
            reconnectionScope.launch {
                delay(retryDelay)
                Log.i(TAG, "Retrying connection, attempt $retryCount/$maxRetries")

                device?.let {
                    connectToDevice(it) // Retry connection
                }
                isReconnecting = false
            }
        } else {
            Log.e(TAG, "Max retries reached or already reconnecting")
        }
    }

    companion object {
        public fun writeCharacteristic(characteristicUuid: UUID, value: ByteArray) {
            val bluetoothGatt = CommonDataArea.bluetoothGatt
            bluetoothGatt?.let { gatt ->
                val characteristic = gatt.getService(UUID.fromString(UUIDConfig.serviceToCharacteristicsMap["SERVICE_BP"]))
                    ?.getCharacteristic(characteristicUuid)

                if (characteristic != null) {
                    characteristic.value = value
                    val success = gatt.writeCharacteristic(characteristic)
                    if (success) {
                        Log.d("BluetoothManager", "Data sent successfully to $characteristicUuid")
                    } else {
                        Log.e("BluetoothManager", "Failed to send data to $characteristicUuid")
                    }
                } else {
                    Log.e("BluetoothManager", "Characteristic $characteristicUuid not found")
                }
            } ?: Log.e("BluetoothManager", "BluetoothGatt is null")
        }
    }
}


