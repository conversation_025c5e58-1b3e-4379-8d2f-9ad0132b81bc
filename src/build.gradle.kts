/*
 * SIBEL INC ("SIBEL HEALTH") CONFIDENTIAL
 * Copyright 2018-2021 [Sibel Inc.], All Rights Reserved.
 * NOTICE: All information contained herein is, and remains the property of SIBEL
 * INC. The intellectual and technical concepts contained herein are proprietary
 * to SIBEL INC and may be covered by U.S. and Foreign Patents, patents in
 * process, and are protected by trade secret or copyright law. Dissemination of
 * this information or reproduction of this material is strictly forbidden unless
 * prior written permission is obtained from SIBEL INC. Access to the source code
 * contained herein is hereby forbidden to anyone except current SIBEL INC
 * employees, managers or contractors who have executed Confidentiality and
 * Non-disclosure agreements explicitly covering such access.
 * The copyright notice above does not evidence any actual or intended
 * publication or disclosure of this source code, which includes information that
 * is confidential and/or proprietary, and is a trade secret, of SIBEL INC.
 * ANY REPRODUCTION, MODIFICATION, DISTRIBUTION, PUBLIC PERFORMANCE, OR PUBLIC
 * DISPLAY OF OR THROUGH USE OF THIS SOURCE CODE WITHOUT THE EXPRESS WRITTEN
 * CONSENT OF COMPANY IS STRICTLY PROHIBITED, AND IN VIOLATION OF APPLICABLE
 * LAWS AND INTERNATIONAL TREATIES. THE RECEIPT OR POSSESSION OF THIS SOURCE
 * CODE AND/OR RELATED INFORMATION DOES NOT CONVEY OR IMPLY ANY RIGHTS TO
 * REPRODUCE, DISCLOSE OR DISTRIBUTE ITS CONTENTS, OR TO MANUFACTURE, USE, OR
 * SELL ANYTHING THAT IT MAY DESCRIBE, IN WHOLE OR IN PART.
 */

// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    kotlin("jvm") version "1.5.20"
    id("org.jetbrains.dokka") version "1.8.20"
    id("org.jetbrains.kotlin.android") version "1.7.10" apply false
    id("com.android.application") version "8.0.2" apply false
}

buildscript {
    val agp_version by extra("7.2.2")
    repositories {
        google()
        mavenCentral()
    }
    extra.apply {
        set("kotlin_version", "1.8.0")
        set("nav_version", "2.3.0")
    }

    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${rootProject.extra.get("kotlin_version")}")
        classpath("androidx.navigation:navigation-safe-args-gradle-plugin:2.5.0")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenLocal()
        mavenCentral()
        jcenter()
        maven(url = "https://repo1.maven.org/maven2/")
        maven(url = "https://www.myget.org/F/abtsoftware/maven")
    }
    apply(plugin = "org.jetbrains.dokka")
}
