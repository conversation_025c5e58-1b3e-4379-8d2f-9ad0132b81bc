#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 303038464 bytes for Failed to commit area from 0x00000000b5000000 to 0x00000000c7100000 of length 303038464.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3521), pid=71432, tid=59596
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 18:02:15 2022 India Standard Time elapsed time: 34.120453 seconds (0d 0h 0m 34s)

---------------  T H R E A D  ---------------

Current thread (0x0000029b7e6e7000):  VMThread "VM Thread" [stack: 0x000000cd6ee00000,0x000000cd6ef00000] [id=59596]

Stack: [0x000000cd6ee00000,0x000000cd6ef00000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x0000029b0000112d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x733535]
V  [jvm.dll+0x7334eb]
V  [jvm.dll+0x5fb080]
V  [jvm.dll+0x5fb818]
C  [ntdll.dll+0xa8fcf]
C  [ntdll.dll+0x35e9a]
C  [ntdll.dll+0xa7fde]
C  0x0000029b0000112d

VM_Operation (0x000000cd74ef9d40): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000029b1afb6000


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000029b18e4ebd0, length=92, elements={
0x0000029b74e1e000, 0x0000029b7e6e8800, 0x0000029b7e70f000, 0x0000029b7e731000,
0x0000029b7e732000, 0x0000029b7e735800, 0x0000029b7e737000, 0x0000029b7e73c800,
0x0000029b7e753000, 0x0000029b7f1e7000, 0x0000029b0fdf4000, 0x0000029b0fc28800,
0x0000029b0f913800, 0x0000029b0fee3000, 0x0000029b0fee4000, 0x0000029b0f461000,
0x0000029b0f464000, 0x0000029b0f465000, 0x0000029b0fdd2000, 0x0000029b103f9000,
0x0000029b103fa800, 0x0000029b103fc800, 0x0000029b103fd000, 0x0000029b103fe000,
0x0000029b103fb800, 0x0000029b103f8800, 0x0000029b103ff000, 0x0000029b103fa000,
0x0000029b0f3d9800, 0x0000029b0f3d8800, 0x0000029b0f3d8000, 0x0000029b0f3dc000,
0x0000029b0f3d6000, 0x0000029b0f3dc800, 0x0000029b0f3d7000, 0x0000029b0f3e2800,
0x0000029b0f3e3800, 0x0000029b0f3dd800, 0x0000029b0f3e4000, 0x0000029b0f3e1800,
0x0000029b0f3de800, 0x0000029b0f3e0000, 0x0000029b0f3e5000, 0x0000029b0f3df000,
0x0000029b0f3e1000, 0x0000029b10dd4000, 0x0000029b10dd5800, 0x0000029b10dd5000,
0x0000029b10dd6800, 0x0000029b10dd7800, 0x0000029b10dd1800, 0x0000029b10dd3000,
0x0000029b10dd8000, 0x0000029b10dd2800, 0x0000029b10ddb800, 0x0000029b10dda800,
0x0000029b10ddc000, 0x0000029b10ddd000, 0x0000029b10dde000, 0x0000029b10dde800,
0x0000029b10dd9000, 0x0000029b10dd9800, 0x0000029b10ddf800, 0x0000029b10de0800,
0x0000029b145ef000, 0x0000029b145ee000, 0x0000029b145ed800, 0x0000029b145eb000,
0x0000029b145ec800, 0x0000029b145e9800, 0x0000029b145ea000, 0x0000029b145f0800,
0x0000029b145f1800, 0x0000029b145eb800, 0x0000029b145f4000, 0x0000029b145f3000,
0x0000029b145f2000, 0x0000029b145f4800, 0x0000029b145f5800, 0x0000029b1afad000,
0x0000029b1afac000, 0x0000029b1afad800, 0x0000029b1afa9000, 0x0000029b1afa9800,
0x0000029b1afa8000, 0x0000029b1afb1000, 0x0000029b1afae800, 0x0000029b1afb2000,
0x0000029b1afb4000, 0x0000029b1afaf800, 0x0000029b1afb5000, 0x0000029b1afb6000
}

Java Threads: ( => current thread )
  0x0000029b74e1e000 JavaThread "main" [_thread_blocked, id=73144, stack(0x000000cd6e800000,0x000000cd6e900000)]
  0x0000029b7e6e8800 JavaThread "Reference Handler" daemon [_thread_blocked, id=73288, stack(0x000000cd6ef00000,0x000000cd6f000000)]
  0x0000029b7e70f000 JavaThread "Finalizer" daemon [_thread_blocked, id=72980, stack(0x000000cd6f000000,0x000000cd6f100000)]
  0x0000029b7e731000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=71772, stack(0x000000cd6f100000,0x000000cd6f200000)]
  0x0000029b7e732000 JavaThread "Attach Listener" daemon [_thread_blocked, id=62548, stack(0x000000cd6f200000,0x000000cd6f300000)]
  0x0000029b7e735800 JavaThread "Service Thread" daemon [_thread_blocked, id=71368, stack(0x000000cd6f300000,0x000000cd6f400000)]
  0x0000029b7e737000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=72268, stack(0x000000cd6f400000,0x000000cd6f500000)]
  0x0000029b7e73c800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=70060, stack(0x000000cd6f500000,0x000000cd6f600000)]
  0x0000029b7e753000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=56640, stack(0x000000cd6f600000,0x000000cd6f700000)]
  0x0000029b7f1e7000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=73520, stack(0x000000cd6f700000,0x000000cd6f800000)]
  0x0000029b0fdf4000 JavaThread "Daemon health stats" [_thread_blocked, id=71864, stack(0x000000cd6fb00000,0x000000cd6fc00000)]
  0x0000029b0fc28800 JavaThread "Incoming local TCP Connector on port 51842" [_thread_in_native, id=43284, stack(0x000000cd6fc00000,0x000000cd6fd00000)]
  0x0000029b0f913800 JavaThread "Daemon periodic checks" [_thread_blocked, id=69796, stack(0x000000cd6fd00000,0x000000cd6fe00000)]
  0x0000029b0fee3000 JavaThread "Daemon" [_thread_blocked, id=71616, stack(0x000000cd6fe00000,0x000000cd6ff00000)]
  0x0000029b0fee4000 JavaThread "Handler for socket connection from /127.0.0.1:51842 to /127.0.0.1:51843" [_thread_in_native, id=71424, stack(0x000000cd6ff00000,0x000000cd70000000)]
  0x0000029b0f461000 JavaThread "Cancel handler" [_thread_blocked, id=68724, stack(0x000000cd70000000,0x000000cd70100000)]
  0x0000029b0f464000 JavaThread "Daemon worker" [_thread_blocked, id=68992, stack(0x000000cd70100000,0x000000cd70200000)]
  0x0000029b0f465000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51842 to /127.0.0.1:51843" [_thread_blocked, id=72776, stack(0x000000cd70200000,0x000000cd70300000)]
  0x0000029b0fdd2000 JavaThread "Stdin handler" [_thread_blocked, id=66636, stack(0x000000cd70300000,0x000000cd70400000)]
  0x0000029b103f9000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=41160, stack(0x000000cd70400000,0x000000cd70500000)]
  0x0000029b103fa800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=71700, stack(0x000000cd70600000,0x000000cd70700000)]
  0x0000029b103fc800 JavaThread "File lock request listener" [_thread_in_native, id=70108, stack(0x000000cd70700000,0x000000cd70800000)]
  0x0000029b103fd000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=62448, stack(0x000000cd70800000,0x000000cd70900000)]
  0x0000029b103fe000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=72840, stack(0x000000cd70900000,0x000000cd70a00000)]
  0x0000029b103fb800 JavaThread "File watcher server" daemon [_thread_blocked, id=72820, stack(0x000000cd70a00000,0x000000cd70b00000)]
  0x0000029b103f8800 JavaThread "File watcher consumer" daemon [_thread_blocked, id=71556, stack(0x000000cd70b00000,0x000000cd70c00000)]
  0x0000029b103ff000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=72828, stack(0x000000cd70c00000,0x000000cd70d00000)]
  0x0000029b103fa000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=53712, stack(0x000000cd70d00000,0x000000cd70e00000)]
  0x0000029b0f3d9800 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=65692, stack(0x000000cd70e00000,0x000000cd70f00000)]
  0x0000029b0f3d8800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=71484, stack(0x000000cd70f00000,0x000000cd71000000)]
  0x0000029b0f3d8000 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=65316, stack(0x000000cd71000000,0x000000cd71100000)]
  0x0000029b0f3dc000 JavaThread "jar transforms" [_thread_blocked, id=73540, stack(0x000000cd71100000,0x000000cd71200000)]
  0x0000029b0f3d6000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=72792, stack(0x000000cd71200000,0x000000cd71300000)]
  0x0000029b0f3dc800 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=70536, stack(0x000000cd71300000,0x000000cd71400000)]
  0x0000029b0f3d7000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=70308, stack(0x000000cd71400000,0x000000cd71500000)]
  0x0000029b0f3e2800 JavaThread "jar transforms Thread 3" [_thread_blocked, id=58212, stack(0x000000cd71500000,0x000000cd71600000)]
  0x0000029b0f3e3800 JavaThread "Unconstrained build operations" [_thread_blocked, id=72696, stack(0x000000cd71600000,0x000000cd71700000)]
  0x0000029b0f3dd800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=73692, stack(0x000000cd71700000,0x000000cd71800000)]
  0x0000029b0f3e4000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=60780, stack(0x000000cd71800000,0x000000cd71900000)]
  0x0000029b0f3e1800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=73464, stack(0x000000cd71900000,0x000000cd71a00000)]
  0x0000029b0f3de800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=69288, stack(0x000000cd71a00000,0x000000cd71b00000)]
  0x0000029b0f3e0000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=72500, stack(0x000000cd71b00000,0x000000cd71c00000)]
  0x0000029b0f3e5000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=71504, stack(0x000000cd71c00000,0x000000cd71d00000)]
  0x0000029b0f3df000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=25480, stack(0x000000cd71d00000,0x000000cd71e00000)]
  0x0000029b0f3e1000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=73116, stack(0x000000cd71e00000,0x000000cd71f00000)]
  0x0000029b10dd4000 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=72308, stack(0x000000cd71f00000,0x000000cd72000000)]
  0x0000029b10dd5800 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=71224, stack(0x000000cd72000000,0x000000cd72100000)]
  0x0000029b10dd5000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=72536, stack(0x000000cd72100000,0x000000cd72200000)]
  0x0000029b10dd6800 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=34588, stack(0x000000cd72200000,0x000000cd72300000)]
  0x0000029b10dd7800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=58964, stack(0x000000cd72300000,0x000000cd72400000)]
  0x0000029b10dd1800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=59628, stack(0x000000cd72400000,0x000000cd72500000)]
  0x0000029b10dd3000 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=70300, stack(0x000000cd72500000,0x000000cd72600000)]
  0x0000029b10dd8000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=71124, stack(0x000000cd72600000,0x000000cd72700000)]
  0x0000029b10dd2800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=61364, stack(0x000000cd72700000,0x000000cd72800000)]
  0x0000029b10ddb800 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=72576, stack(0x000000cd72800000,0x000000cd72900000)]
  0x0000029b10dda800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=70164, stack(0x000000cd72900000,0x000000cd72a00000)]
  0x0000029b10ddc000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=61772, stack(0x000000cd72a00000,0x000000cd72b00000)]
  0x0000029b10ddd000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=50540, stack(0x000000cd72b00000,0x000000cd72c00000)]
  0x0000029b10dde000 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=73328, stack(0x000000cd72c00000,0x000000cd72d00000)]
  0x0000029b10dde800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=67488, stack(0x000000cd72d00000,0x000000cd72e00000)]
  0x0000029b10dd9000 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=58680, stack(0x000000cd72e00000,0x000000cd72f00000)]
  0x0000029b10dd9800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=70788, stack(0x000000cd72f00000,0x000000cd73000000)]
  0x0000029b10ddf800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=55352, stack(0x000000cd73000000,0x000000cd73100000)]
  0x0000029b10de0800 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=65744, stack(0x000000cd73100000,0x000000cd73200000)]
  0x0000029b145ef000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=68120, stack(0x000000cd73200000,0x000000cd73300000)]
  0x0000029b145ee000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=73148, stack(0x000000cd73300000,0x000000cd73400000)]
  0x0000029b145ed800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=6836, stack(0x000000cd73400000,0x000000cd73500000)]
  0x0000029b145eb000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=71380, stack(0x000000cd73500000,0x000000cd73600000)]
  0x0000029b145ec800 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=71196, stack(0x000000cd73600000,0x000000cd73700000)]
  0x0000029b145e9800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=64924, stack(0x000000cd73700000,0x000000cd73800000)]
  0x0000029b145ea000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=64628, stack(0x000000cd73800000,0x000000cd73900000)]
  0x0000029b145f0800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=54768, stack(0x000000cd73900000,0x000000cd73a00000)]
  0x0000029b145f1800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=73628, stack(0x000000cd73a00000,0x000000cd73b00000)]
  0x0000029b145eb800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=52028, stack(0x000000cd73b00000,0x000000cd73c00000)]
  0x0000029b145f4000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=73112, stack(0x000000cd73c00000,0x000000cd73d00000)]
  0x0000029b145f3000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=25604, stack(0x000000cd73d00000,0x000000cd73e00000)]
  0x0000029b145f2000 JavaThread "jar transforms Thread 4" [_thread_blocked, id=53568, stack(0x000000cd73e00000,0x000000cd73f00000)]
  0x0000029b145f4800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=44244, stack(0x000000cd73f00000,0x000000cd74000000)]
  0x0000029b145f5800 JavaThread "Memory manager" [_thread_blocked, id=73104, stack(0x000000cd74000000,0x000000cd74100000)]
  0x0000029b1afad000 JavaThread "build event listener" [_thread_blocked, id=16468, stack(0x000000cd74200000,0x000000cd74300000)]
  0x0000029b1afac000 JavaThread "Execution worker for ':'" [_thread_blocked, id=29968, stack(0x000000cd74300000,0x000000cd74400000)]
  0x0000029b1afad800 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=70256, stack(0x000000cd74400000,0x000000cd74500000)]
  0x0000029b1afa9000 JavaThread "Execution worker for ':' Thread 3" [_thread_blocked, id=73424, stack(0x000000cd74500000,0x000000cd74600000)]
  0x0000029b1afa9800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=50140, stack(0x000000cd74600000,0x000000cd74700000)]
  0x0000029b1afa8000 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=73472, stack(0x000000cd74700000,0x000000cd74800000)]
  0x0000029b1afb1000 JavaThread "pool-3-thread-1" [_thread_blocked, id=55140, stack(0x000000cd74800000,0x000000cd74900000)]
  0x0000029b1afae800 JavaThread "stderr" [_thread_in_native, id=72252, stack(0x000000cd74900000,0x000000cd74a00000)]
  0x0000029b1afb2000 JavaThread "stdout" [_thread_in_native, id=56932, stack(0x000000cd74a00000,0x000000cd74b00000)]
  0x0000029b1afb4000 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=72152, stack(0x000000cd74b00000,0x000000cd74c00000)]
  0x0000029b1afaf800 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=73668, stack(0x000000cd74c00000,0x000000cd74d00000)]
  0x0000029b1afb5000 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=71428, stack(0x000000cd74d00000,0x000000cd74e00000)]
  0x0000029b1afb6000 JavaThread "WorkerExecutor Queue Thread 5" [_thread_blocked, id=70080, stack(0x000000cd74e00000,0x000000cd74f00000)]

Other Threads:
=>0x0000029b7e6e7000 VMThread "VM Thread" [stack: 0x000000cd6ee00000,0x000000cd6ef00000] [id=59596]
  0x0000029b7f22c800 WatcherThread [stack: 0x000000cd6f800000,0x000000cd6f900000] [id=57036]
  0x0000029b74e36800 GCTaskThread "GC Thread#0" [stack: 0x000000cd6e900000,0x000000cd6ea00000] [id=53164]
  0x0000029b7f56b000 GCTaskThread "GC Thread#1" [stack: 0x000000cd6f900000,0x000000cd6fa00000] [id=69300]
  0x0000029b7f7c1800 GCTaskThread "GC Thread#2" [stack: 0x000000cd6fa00000,0x000000cd6fb00000] [id=55516]
  0x0000029b0fdcd800 GCTaskThread "GC Thread#3" [stack: 0x000000cd70500000,0x000000cd70600000] [id=71664]
  0x0000029b74e5c800 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000cd6ea00000,0x000000cd6eb00000] [id=43992]
  0x0000029b74e5f800 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000cd6eb00000,0x000000cd6ec00000] [id=72036]
  0x0000029b74ef0000 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000cd6ec00000,0x000000cd6ed00000] [id=72624]
  0x0000029b74ef4000 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x000000cd6ed00000,0x000000cd6ee00000] [id=72480]

Threads with active compile tasks:
C2 CompilerThread0  34146 13531   !   4       java.util.concurrent.ConcurrentHashMap::transfer (828 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000029b72d2b700] Threads_lock - owner thread: 0x0000029b7e6e7000
[0x0000029b72d2ab30] Heap_lock - owner thread: 0x0000029b1afb6000

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 640000K, used 245760K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 104503K, capacity 106913K, committed 107212K, reserved 1142784K
  class space    used 13614K, capacity 14524K, committed 14592K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Updating 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Updating 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Updating 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Updating 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Updating 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Updating 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Updating 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Updating 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Updating 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Updating 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Updating 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Updating 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Updating 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Updating 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Updating 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Updating 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Updating 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Updating 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Updating 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%|HS|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Complete 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%|HS|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Complete 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%|HS|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Complete 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%|HS|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Complete 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HC|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HC|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HS|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%|HC|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Complete 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Updating 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Updating 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Updating 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Updating 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Updating 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2300000| Updating 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Updating 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Updating 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Updating 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Updating 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Updating 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Updating 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Updating 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Updating 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Updating 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Updating 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Updating 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Updating 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3000000| Updating 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3100000| Updating 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3200000| Updating 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3300000| Updating 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3400000| Updating 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3500000| Updating 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3600000| Updating 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3700000| Updating 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3800000| Updating 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3900000| Updating 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3a00000| Updating 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3b00000| Updating 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3c00000| Updating 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3d00000| Updating 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3e00000| Updating 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a3f00000| Updating 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4000000| Updating 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4100000| Updating 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4200000| Updating 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4300000| Updating 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4400000| Updating 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4500000| Updating 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4600000| Updating 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4700000| Updating 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4800000| Updating 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4900000| Updating 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4a00000| Updating 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4b00000| Updating 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4c00000| Updating 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4d00000| Updating 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4e00000| Updating 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000, 0x00000000a4f00000| Updating 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000, 0x00000000a5000000| Updating 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5200000, 0x00000000a5100000| Updating 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5300000, 0x00000000a5200000| Updating 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5400000, 0x00000000a5300000| Updating 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5500000, 0x00000000a5400000| Updating 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5600000, 0x00000000a5500000| Updating 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5700000, 0x00000000a5600000| Updating 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5800000, 0x00000000a5700000| Updating 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5900000, 0x00000000a5800000| Updating 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5a00000, 0x00000000a5900000| Updating 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5b00000, 0x00000000a5a00000| Updating 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5c00000, 0x00000000a5b00000| Updating 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5d00000, 0x00000000a5c00000| Updating 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5e00000, 0x00000000a5d00000| Updating 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5f00000, 0x00000000a5e00000| Updating 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a6000000, 0x00000000a5f00000| Updating 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6100000, 0x00000000a6000000| Updating 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000, 0x00000000a6100000| Updating 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000, 0x00000000a6200000| Updating 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6300000| Updating 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6400000| Updating 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6500000| Updating 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6700000, 0x00000000a6600000| Updating 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|  |TAMS 0x00000000a6800000, 0x00000000a6700000| Updating 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000, 0x00000000a6800000| Updating 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000, 0x00000000a6900000| Updating 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%|HS|  |TAMS 0x00000000a6b00000, 0x00000000a6a00000| Complete 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%|HS|  |TAMS 0x00000000a6c00000, 0x00000000a6b00000| Complete 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%|HC|  |TAMS 0x00000000a6d00000, 0x00000000a6c00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%|HS|  |TAMS 0x00000000a6e00000, 0x00000000a6d00000| Complete 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%|HS|  |TAMS 0x00000000a6f00000, 0x00000000a6e00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%|HS|  |TAMS 0x00000000a7000000, 0x00000000a6f00000| Complete 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%|HC|  |TAMS 0x00000000a7100000, 0x00000000a7000000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%|HS|  |TAMS 0x00000000a7200000, 0x00000000a7100000| Complete 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%|HC|  |TAMS 0x00000000a7300000, 0x00000000a7200000| Complete 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%|HS|  |TAMS 0x00000000a7400000, 0x00000000a7300000| Complete 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%|HS|  |TAMS 0x00000000a7500000, 0x00000000a7400000| Complete 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%|HS|  |TAMS 0x00000000a7600000, 0x00000000a7500000| Complete 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%|HC|  |TAMS 0x00000000a7700000, 0x00000000a7600000| Complete 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%|HS|  |TAMS 0x00000000a7800000, 0x00000000a7700000| Complete 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%|HC|  |TAMS 0x00000000a7900000, 0x00000000a7800000| Complete 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%|HS|  |TAMS 0x00000000a7a00000, 0x00000000a7900000| Complete 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%|HC|  |TAMS 0x00000000a7b00000, 0x00000000a7a00000| Complete 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%|HC|  |TAMS 0x00000000a7c00000, 0x00000000a7b00000| Complete 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%|HC|  |TAMS 0x00000000a7d00000, 0x00000000a7c00000| Complete 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%|HS|  |TAMS 0x00000000a7e00000, 0x00000000a7d00000| Complete 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7f00000, 0x00000000a7e00000| Updating 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a8000000, 0x00000000a7f00000| Updating 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%|HS|  |TAMS 0x00000000a8100000, 0x00000000a8000000| Complete 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%|HC|  |TAMS 0x00000000a8200000, 0x00000000a8100000| Complete 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%|HS|  |TAMS 0x00000000a8300000, 0x00000000a8200000| Complete 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HC|  |TAMS 0x00000000a8400000, 0x00000000a8300000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%|HC|  |TAMS 0x00000000a8500000, 0x00000000a8400000| Complete 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%|HS|  |TAMS 0x00000000a8600000, 0x00000000a8500000| Complete 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%|HS|  |TAMS 0x00000000a8700000, 0x00000000a8600000| Complete 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%|HS|  |TAMS 0x00000000a8800000, 0x00000000a8700000| Complete 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%|HS|  |TAMS 0x00000000a8900000, 0x00000000a8800000| Complete 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8a00000, 0x00000000a8900000| Updating 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8b00000, 0x00000000a8a00000| Updating 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8c00000, 0x00000000a8b00000| Updating 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8c00000| Updating 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8d00000| Updating 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8f00000, 0x00000000a8e00000| Updating 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a9000000, 0x00000000a8f00000| Updating 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9100000, 0x00000000a9000000| Updating 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9200000, 0x00000000a9100000| Updating 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%|HS|  |TAMS 0x00000000a9300000, 0x00000000a9200000| Complete 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9400000, 0x00000000a9300000| Updating 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|  |TAMS 0x00000000a9500000, 0x00000000a9400000| Updating 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9600000, 0x00000000a9500000| Updating 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%|HS|  |TAMS 0x00000000a9700000, 0x00000000a9600000| Complete 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%|HC|  |TAMS 0x00000000a9800000, 0x00000000a9700000| Complete 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%|HS|  |TAMS 0x00000000a9900000, 0x00000000a9800000| Complete 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%|HS|  |TAMS 0x00000000a9a00000, 0x00000000a9900000| Complete 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000, 0x00000000a9a00000| Updating 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%|HS|  |TAMS 0x00000000a9c00000, 0x00000000a9b00000| Complete 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%|HS|  |TAMS 0x00000000a9d00000, 0x00000000a9c00000| Complete 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%|HC|  |TAMS 0x00000000a9e00000, 0x00000000a9d00000| Complete 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%|HS|  |TAMS 0x00000000a9f00000, 0x00000000a9e00000| Complete 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%|HS|  |TAMS 0x00000000aa000000, 0x00000000a9f00000| Complete 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%|HC|  |TAMS 0x00000000aa100000, 0x00000000aa000000| Complete 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa200000, 0x00000000aa100000| Updating 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa300000, 0x00000000aa200000| Updating 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%|HS|  |TAMS 0x00000000aa400000, 0x00000000aa300000| Complete 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%|HC|  |TAMS 0x00000000aa500000, 0x00000000aa400000| Complete 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa600000, 0x00000000aa500000| Updating 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%|HS|  |TAMS 0x00000000aa700000, 0x00000000aa600000| Complete 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%|HC|  |TAMS 0x00000000aa800000, 0x00000000aa700000| Complete 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000, 0x00000000aa800000| Updating 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aaa00000, 0x00000000aa900000| Updating 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aab00000, 0x00000000aaa00000| Updating 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%|HS|  |TAMS 0x00000000aac00000, 0x00000000aab00000| Complete 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%|HC|  |TAMS 0x00000000aad00000, 0x00000000aac00000| Complete 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%|HC|  |TAMS 0x00000000aae00000, 0x00000000aad00000| Complete 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%|HS|  |TAMS 0x00000000aaf00000, 0x00000000aae00000| Complete 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%|HC|  |TAMS 0x00000000ab000000, 0x00000000aaf00000| Complete 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%|HS|  |TAMS 0x00000000ab100000, 0x00000000ab000000| Complete 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab200000, 0x00000000ab100000| Updating 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%|HS|  |TAMS 0x00000000ab300000, 0x00000000ab200000| Complete 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab400000, 0x00000000ab300000| Updating 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab500000, 0x00000000ab400000| Updating 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab600000, 0x00000000ab500000| Updating 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%|HS|  |TAMS 0x00000000ab700000, 0x00000000ab600000| Complete 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%| O|  |TAMS 0x00000000ab800000, 0x00000000ab700000| Updating 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%|HS|  |TAMS 0x00000000ab900000, 0x00000000ab800000| Complete 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000aba00000, 0x00000000ab900000| Updating 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000abb00000, 0x00000000aba00000| Updating 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000, 0x00000000abb00000| Updating 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%|HS|  |TAMS 0x00000000abd00000, 0x00000000abc00000| Complete 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%|HC|  |TAMS 0x00000000abe00000, 0x00000000abd00000| Complete 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%|HC|  |TAMS 0x00000000abf00000, 0x00000000abe00000| Complete 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%|HC|  |TAMS 0x00000000ac000000, 0x00000000abf00000| Complete 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%|HS|  |TAMS 0x00000000ac100000, 0x00000000ac000000| Complete 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%| O|  |TAMS 0x00000000ac200000, 0x00000000ac100000| Updating 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%| O|  |TAMS 0x00000000ac300000, 0x00000000ac200000| Updating 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%| O|  |TAMS 0x00000000ac400000, 0x00000000ac300000| Updating 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac500000, 0x00000000ac400000| Updating 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%| O|  |TAMS 0x00000000ac600000, 0x00000000ac500000| Updating 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac600000, 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac800000, 0x00000000ac700000| Updating 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac900000, 0x00000000ac800000| Updating 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000aca00000, 0x00000000ac900000| Updating 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%|HS|  |TAMS 0x00000000acb00000, 0x00000000aca00000| Complete 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%|HS|  |TAMS 0x00000000acc00000, 0x00000000acb00000| Complete 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%|HC|  |TAMS 0x00000000acd00000, 0x00000000acc00000| Complete 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000ace00000, 0x00000000acd00000| Updating 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000ace00000| Updating 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000ad000000, 0x00000000acf00000| Updating 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000, 0x00000000ad000000| Updating 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000, 0x00000000ad100000| Updating 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000, 0x00000000ad200000| Updating 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000, 0x00000000ad300000| Updating 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000, 0x00000000ad400000| Updating 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000, 0x00000000ad500000| Updating 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad700000, 0x00000000ad600000| Updating 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad800000, 0x00000000ad700000| Updating 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad900000, 0x00000000ad800000| Updating 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ada00000, 0x00000000ad900000| Updating 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000adb00000, 0x00000000ada00000| Updating 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adc00000, 0x00000000adb00000| Updating 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%| O|  |TAMS 0x00000000add00000, 0x00000000adc00000| Updating 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000ade00000, 0x00000000add00000| Updating 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000ade69800, 0x00000000ade00000| Updating 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%|HS|  |TAMS 0x00000000adf00000, 0x00000000adf00000| Complete 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%|HC|  |TAMS 0x00000000ae000000, 0x00000000ae000000| Complete 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%|HS|  |TAMS 0x00000000ae100000, 0x00000000ae100000| Complete 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae200000, 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae300000, 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae400000, 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae500000, 0x00000000ae500000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae600000, 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae700000, 0x00000000ae700000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%| O|  |TAMS 0x00000000ae800000, 0x00000000ae800000| Untracked 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000ae900000, 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aea00000, 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aeb00000, 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aec00000, 0x00000000aed00000|  0%| F|  |TAMS 0x00000000aec00000, 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aed00000, 0x00000000aee00000|  0%| F|  |TAMS 0x00000000aed00000, 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aee00000, 0x00000000aef00000|  0%| F|  |TAMS 0x00000000aee00000, 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000aef00000, 0x00000000af000000|  0%| F|  |TAMS 0x00000000aef00000, 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af000000, 0x00000000af100000|  0%| F|  |TAMS 0x00000000af000000, 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af100000, 0x00000000af200000|  0%| F|  |TAMS 0x00000000af100000, 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af200000, 0x00000000af300000|  0%| F|  |TAMS 0x00000000af200000, 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af300000, 0x00000000af400000|  0%| F|  |TAMS 0x00000000af300000, 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af400000, 0x00000000af500000|  0%| F|  |TAMS 0x00000000af400000, 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af500000, 0x00000000af600000|  0%| F|  |TAMS 0x00000000af500000, 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af600000, 0x00000000af700000|  0%| F|  |TAMS 0x00000000af600000, 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af700000, 0x00000000af800000|  0%| F|  |TAMS 0x00000000af700000, 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af800000, 0x00000000af900000|  0%| F|  |TAMS 0x00000000af800000, 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000af900000, 0x00000000afa00000|  0%| F|  |TAMS 0x00000000af900000, 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afa00000, 0x00000000afb00000|  0%| F|  |TAMS 0x00000000afa00000, 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afb00000, 0x00000000afc00000|  0%| F|  |TAMS 0x00000000afb00000, 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afc00000, 0x00000000afd00000|  0%| F|  |TAMS 0x00000000afc00000, 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afd00000, 0x00000000afe00000|  0%| F|  |TAMS 0x00000000afd00000, 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000afe00000, 0x00000000aff00000|  0%| F|  |TAMS 0x00000000afe00000, 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000aff00000, 0x00000000b0000000|  0%| F|  |TAMS 0x00000000aff00000, 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0000000, 0x00000000b0100000|  0%| F|  |TAMS 0x00000000b0000000, 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0100000, 0x00000000b0200000|  0%| F|  |TAMS 0x00000000b0100000, 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0200000, 0x00000000b0300000|  0%| F|  |TAMS 0x00000000b0200000, 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0300000, 0x00000000b0400000|  0%| F|  |TAMS 0x00000000b0300000, 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0400000, 0x00000000b0500000|  0%| F|  |TAMS 0x00000000b0400000, 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0500000, 0x00000000b0600000|  0%| F|  |TAMS 0x00000000b0500000, 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0600000, 0x00000000b0700000|  0%| F|  |TAMS 0x00000000b0600000, 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0700000, 0x00000000b0800000|  0%| F|  |TAMS 0x00000000b0700000, 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0800000, 0x00000000b0900000|  0%| F|  |TAMS 0x00000000b0800000, 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0900000, 0x00000000b0a00000|  0%| F|  |TAMS 0x00000000b0900000, 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0a00000, 0x00000000b0b00000|  0%| F|  |TAMS 0x00000000b0a00000, 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0b00000, 0x00000000b0c00000|  0%| F|  |TAMS 0x00000000b0b00000, 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0c00000, 0x00000000b0d00000|  0%| F|  |TAMS 0x00000000b0c00000, 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0d00000, 0x00000000b0e00000|  0%| F|  |TAMS 0x00000000b0d00000, 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0e00000, 0x00000000b0f00000|  0%| F|  |TAMS 0x00000000b0e00000, 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b0f00000, 0x00000000b1000000|  0%| F|  |TAMS 0x00000000b0f00000, 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1000000, 0x00000000b1100000|  0%| F|  |TAMS 0x00000000b1000000, 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1100000, 0x00000000b1200000|  0%| F|  |TAMS 0x00000000b1100000, 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1200000, 0x00000000b1300000|  0%| F|  |TAMS 0x00000000b1200000, 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1300000, 0x00000000b1400000|  0%| F|  |TAMS 0x00000000b1300000, 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1400000, 0x00000000b1500000|  0%| F|  |TAMS 0x00000000b1400000, 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1500000, 0x00000000b1600000|  0%| F|  |TAMS 0x00000000b1500000, 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1600000, 0x00000000b1700000|  0%| F|  |TAMS 0x00000000b1600000, 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1700000, 0x00000000b1800000|  0%| F|  |TAMS 0x00000000b1700000, 0x00000000b1700000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1800000, 0x00000000b1900000|  0%| F|  |TAMS 0x00000000b1800000, 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000, 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1a00000, 0x00000000b1b00000|  0%| F|  |TAMS 0x00000000b1a00000, 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1b00000, 0x00000000b1c00000|  0%| F|  |TAMS 0x00000000b1b00000, 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1c00000, 0x00000000b1d00000|  0%| F|  |TAMS 0x00000000b1c00000, 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1d00000, 0x00000000b1e00000|  0%| F|  |TAMS 0x00000000b1d00000, 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1e00000, 0x00000000b1f00000|  0%| F|  |TAMS 0x00000000b1e00000, 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b1f00000, 0x00000000b2000000|  0%| F|  |TAMS 0x00000000b1f00000, 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2000000, 0x00000000b2100000|  0%| F|  |TAMS 0x00000000b2000000, 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2100000, 0x00000000b2200000|  0%| F|  |TAMS 0x00000000b2100000, 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2200000, 0x00000000b2300000|  0%| F|  |TAMS 0x00000000b2200000, 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2300000, 0x00000000b2400000|  0%| F|  |TAMS 0x00000000b2300000, 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2400000, 0x00000000b2500000|  0%| F|  |TAMS 0x00000000b2400000, 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2500000, 0x00000000b2600000|  0%| F|  |TAMS 0x00000000b2500000, 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2600000, 0x00000000b2700000|  0%| F|  |TAMS 0x00000000b2600000, 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2700000, 0x00000000b2800000|  0%| F|  |TAMS 0x00000000b2700000, 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2800000, 0x00000000b2900000|  0%| F|  |TAMS 0x00000000b2800000, 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2900000, 0x00000000b2a00000|  0%| F|  |TAMS 0x00000000b2900000, 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2a00000, 0x00000000b2b00000|  0%| F|  |TAMS 0x00000000b2a00000, 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2b00000, 0x00000000b2c00000|  0%| F|  |TAMS 0x00000000b2b00000, 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| S|CS|TAMS 0x00000000b2c00000, 0x00000000b2c00000| Complete 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| S|CS|TAMS 0x00000000b2d00000, 0x00000000b2d00000| Complete 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| S|CS|TAMS 0x00000000b2e00000, 0x00000000b2e00000| Complete 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| S|CS|TAMS 0x00000000b2f00000, 0x00000000b2f00000| Complete 
| 304|0x00000000b3000000, 0x00000000b3000000, 0x00000000b3100000|  0%| F|  |TAMS 0x00000000b3000000, 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3100000, 0x00000000b3200000|  0%| F|  |TAMS 0x00000000b3100000, 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3200000, 0x00000000b3300000|  0%| F|  |TAMS 0x00000000b3200000, 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3300000, 0x00000000b3400000|  0%| F|  |TAMS 0x00000000b3300000, 0x00000000b3300000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3400000, 0x00000000b3500000|  0%| F|  |TAMS 0x00000000b3400000, 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3500000, 0x00000000b3600000|  0%| F|  |TAMS 0x00000000b3500000, 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3600000, 0x00000000b3700000|  0%| F|  |TAMS 0x00000000b3600000, 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3700000, 0x00000000b3800000|  0%| F|  |TAMS 0x00000000b3700000, 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3800000, 0x00000000b3900000|  0%| F|  |TAMS 0x00000000b3800000, 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3900000, 0x00000000b3a00000|  0%| F|  |TAMS 0x00000000b3900000, 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3a00000, 0x00000000b3b00000|  0%| F|  |TAMS 0x00000000b3a00000, 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3b00000, 0x00000000b3c00000|  0%| F|  |TAMS 0x00000000b3b00000, 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3c00000, 0x00000000b3d00000|  0%| F|  |TAMS 0x00000000b3c00000, 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3d00000, 0x00000000b3e00000|  0%| F|  |TAMS 0x00000000b3d00000, 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3e00000, 0x00000000b3f00000|  0%| F|  |TAMS 0x00000000b3e00000, 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b3f00000, 0x00000000b4000000|  0%| F|  |TAMS 0x00000000b3f00000, 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4000000, 0x00000000b4100000|  0%| F|  |TAMS 0x00000000b4000000, 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4100000, 0x00000000b4200000|  0%| F|  |TAMS 0x00000000b4100000, 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4200000, 0x00000000b4300000|  0%| F|  |TAMS 0x00000000b4200000, 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4300000, 0x00000000b4400000|  0%| F|  |TAMS 0x00000000b4300000, 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4400000, 0x00000000b4500000|  0%| F|  |TAMS 0x00000000b4400000, 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4500000, 0x00000000b4600000|  0%| F|  |TAMS 0x00000000b4500000, 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4600000, 0x00000000b4700000|  0%| F|  |TAMS 0x00000000b4600000, 0x00000000b4600000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4700000, 0x00000000b4800000|  0%| F|  |TAMS 0x00000000b4700000, 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4800000, 0x00000000b4900000|  0%| F|  |TAMS 0x00000000b4800000, 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4900000, 0x00000000b4a00000|  0%| F|  |TAMS 0x00000000b4900000, 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4a00000, 0x00000000b4b00000|  0%| F|  |TAMS 0x00000000b4a00000, 0x00000000b4a00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4b00000, 0x00000000b4c00000|  0%| F|  |TAMS 0x00000000b4b00000, 0x00000000b4b00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4c00000, 0x00000000b4d00000|  0%| F|  |TAMS 0x00000000b4c00000, 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4d00000, 0x00000000b4e00000|  0%| F|  |TAMS 0x00000000b4d00000, 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4e00000, 0x00000000b4f00000|  0%| F|  |TAMS 0x00000000b4e00000, 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b4f00000, 0x00000000b5000000|  0%| F|  |TAMS 0x00000000b4f00000, 0x00000000b4f00000| Untracked 

Card table byte_map: [0x0000029b78c30000,0x0000029b78f30000] _byte_map_base: 0x0000029b78730000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000029b74e55c20, (CMBitMap*) 0x0000029b74e55be8
 Prev Bits: [0x0000029b7aa30000, 0x0000029b7c230000)
 Next Bits: [0x0000029b79230000, 0x0000029b7aa30000)

Polling page: 0x0000029b72d60000

Metaspace:

Usage:
  Non-class:     90.22 MB capacity,    88.76 MB ( 98%) used,     1.17 MB (  1%) free+waste,   300.63 KB ( <1%) overhead. 
      Class:     14.18 MB capacity,    13.30 MB ( 94%) used,   776.84 KB (  5%) free+waste,   132.75 KB ( <1%) overhead. 
       Both:    104.41 MB capacity,   102.05 MB ( 98%) used,     1.93 MB (  2%) free+waste,   433.38 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       92.00 MB reserved,      90.45 MB ( 98%) committed 
      Class space:        1.00 GB reserved,      14.25 MB (  1%) committed 
             Both:        1.09 GB reserved,     104.70 MB (  9%) committed 

Chunk freelists:
   Non-Class:  39.00 KB
       Class:  4.00 KB
        Both:  43.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 173.46 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=8906Kb max_used=8906Kb free=111157Kb
 bounds [0x0000029b07ac0000, 0x0000029b08380000, 0x0000029b0f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=30063Kb max_used=30063Kb free=89937Kb
 bounds [0x0000029b00590000, 0x0000029b022f0000, 0x0000029b07ac0000]
CodeHeap 'non-nmethods': size=5696Kb used=2414Kb max_used=2458Kb free=3281Kb
 bounds [0x0000029b00000000, 0x0000029b00280000, 0x0000029b00590000]
 total_blobs=14333 nmethods=13400 adapters=845
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 33.944 Thread 0x0000029b7e73c800 13522   !   3       com.android.tools.r8.internal.Y9::b (116 bytes)
Event: 33.945 Thread 0x0000029b7e73c800 nmethod 13522 0x0000029b022e7a10 code [0x0000029b022e7ca0, 0x0000029b022e8ba8]
Event: 33.945 Thread 0x0000029b7e73c800 13523       3       com.android.tools.r8.internal.q::next (33 bytes)
Event: 33.945 Thread 0x0000029b7e73c800 nmethod 13523 0x0000029b022e8f10 code [0x0000029b022e9100, 0x0000029b022e9478]
Event: 33.950 Thread 0x0000029b7e73c800 13526       3       com.android.tools.r8.graph.Z0::get (2 bytes)
Event: 33.951 Thread 0x0000029b7e73c800 nmethod 13526 0x0000029b022e9590 code [0x0000029b022e9740, 0x0000029b022e9838]
Event: 33.959 Thread 0x0000029b7e73c800 13528   !   3       com.android.tools.r8.internal.Va::a (104 bytes)
Event: 33.959 Thread 0x0000029b7e73c800 nmethod 13528 0x0000029b022e9910 code [0x0000029b022e9b00, 0x0000029b022ea058]
Event: 33.959 Thread 0x0000029b7e73c800 13529   !   3       com.android.tools.r8.internal.Va::b (79 bytes)
Event: 33.960 Thread 0x0000029b7e737000 nmethod 13520% 0x0000029b0836f690 code [0x0000029b0836f860, 0x0000029b083701f8]
Event: 33.961 Thread 0x0000029b7e73c800 nmethod 13529 0x0000029b022ea210 code [0x0000029b022ea500, 0x0000029b022eb488]
Event: 33.961 Thread 0x0000029b7e737000 13527       4       com.android.tools.r8.graph.S0::g (62 bytes)
Event: 33.969 Thread 0x0000029b7e737000 nmethod 13527 0x0000029b08370690 code [0x0000029b08370860, 0x0000029b08370fd8]
Event: 33.969 Thread 0x0000029b7e737000 13530       4       com.android.tools.r8.graph.a1::g (62 bytes)
Event: 33.977 Thread 0x0000029b7e737000 nmethod 13530 0x0000029b08371510 code [0x0000029b083716c0, 0x0000029b08371d18]
Event: 33.977 Thread 0x0000029b7e737000 13524       4       java.util.concurrent.ConcurrentHashMap$Traverser::advance (188 bytes)
Event: 33.981 Thread 0x0000029b7e737000 nmethod 13524 0x0000029b08372110 code [0x0000029b083722a0, 0x0000029b08372578]
Event: 33.981 Thread 0x0000029b7e737000 13525       4       java.util.concurrent.ConcurrentHashMap$BaseIterator::hasNext (13 bytes)
Event: 33.982 Thread 0x0000029b7e737000 nmethod 13525 0x0000029b08372710 code [0x0000029b083728a0, 0x0000029b08372918]
Event: 33.982 Thread 0x0000029b7e737000 13531   !   4       java.util.concurrent.ConcurrentHashMap::transfer (828 bytes)

GC Heap History (20 events):
Event: 30.941 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 201728K, used 174085K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 101470K, capacity 103844K, committed 104012K, reserved 1138688K
  class space    used 13064K, capacity 13952K, committed 13952K, reserved 1048576K
}
Event: 30.947 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 201728K, used 185349K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 2 survivors (2048K)
 Metaspace       used 101496K, capacity 103844K, committed 104012K, reserved 1138688K
  class space    used 13067K, capacity 13952K, committed 13952K, reserved 1048576K
}
Event: 30.949 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 242688K, used 178912K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 101496K, capacity 103844K, committed 104012K, reserved 1138688K
  class space    used 13067K, capacity 13952K, committed 13952K, reserved 1048576K
}
Event: 31.325 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 242688K, used 212704K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 2 survivors (2048K)
 Metaspace       used 101679K, capacity 104015K, committed 104140K, reserved 1138688K
  class space    used 13083K, capacity 13987K, committed 14080K, reserved 1048576K
}
Event: 31.335 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 242688K, used 192314K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 101679K, capacity 104015K, committed 104140K, reserved 1138688K
  class space    used 13083K, capacity 13987K, committed 14080K, reserved 1048576K
}
Event: 31.548 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 242688K, used 204602K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 3 survivors (3072K)
 Metaspace       used 102938K, capacity 105318K, committed 105420K, reserved 1140736K
  class space    used 13420K, capacity 14315K, committed 14336K, reserved 1048576K
}
Event: 31.560 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 242688K, used 196144K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 102938K, capacity 105318K, committed 105420K, reserved 1140736K
  class space    used 13420K, capacity 14315K, committed 14336K, reserved 1048576K
}
Event: 31.921 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 242688K, used 205360K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 2 survivors (2048K)
 Metaspace       used 103035K, capacity 105399K, committed 105548K, reserved 1140736K
  class space    used 13443K, capacity 14340K, committed 14464K, reserved 1048576K
}
Event: 31.941 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 242688K, used 200553K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 103035K, capacity 105399K, committed 105548K, reserved 1140736K
  class space    used 13443K, capacity 14340K, committed 14464K, reserved 1048576K
}
Event: 32.029 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 242688K, used 208745K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 103132K, capacity 105469K, committed 105804K, reserved 1140736K
  class space    used 13451K, capacity 14342K, committed 14464K, reserved 1048576K
}
Event: 32.046 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 242688K, used 204139K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 103132K, capacity 105469K, committed 105804K, reserved 1140736K
  class space    used 13451K, capacity 14342K, committed 14464K, reserved 1048576K
}
Event: 32.161 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 242688K, used 213355K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 103416K, capacity 105770K, committed 106060K, reserved 1140736K
  class space    used 13497K, capacity 14402K, committed 14464K, reserved 1048576K
}
Event: 32.183 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 344064K, used 208669K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 103416K, capacity 105770K, committed 106060K, reserved 1140736K
  class space    used 13497K, capacity 14402K, committed 14464K, reserved 1048576K
}
Event: 32.204 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 344064K, used 211741K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 2 survivors (2048K)
 Metaspace       used 103447K, capacity 105773K, committed 106060K, reserved 1140736K
  class space    used 13503K, capacity 14403K, committed 14464K, reserved 1048576K
}
Event: 32.218 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 344064K, used 210266K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 103447K, capacity 105773K, committed 106060K, reserved 1140736K
  class space    used 13503K, capacity 14403K, committed 14464K, reserved 1048576K
}
Event: 32.902 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 344064K, used 245082K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 4 survivors (4096K)
 Metaspace       used 103855K, capacity 106296K, committed 106316K, reserved 1140736K
  class space    used 13525K, capacity 14448K, committed 14464K, reserved 1048576K
}
Event: 32.974 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 344064K, used 226684K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 103855K, capacity 106296K, committed 106316K, reserved 1140736K
  class space    used 13525K, capacity 14448K, committed 14464K, reserved 1048576K
}
Event: 33.294 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 344064K, used 238972K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 5 survivors (5120K)
 Metaspace       used 103890K, capacity 106299K, committed 106316K, reserved 1140736K
  class space    used 13525K, capacity 14448K, committed 14464K, reserved 1048576K
}
Event: 33.339 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 344064K, used 232870K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 103890K, capacity 106299K, committed 106316K, reserved 1140736K
  class space    used 13525K, capacity 14448K, committed 14464K, reserved 1048576K
}
Event: 34.055 GC heap before
{Heap before GC invocations=53 (full 0):
 garbage-first heap   total 344064K, used 261542K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 5 survivors (5120K)
 Metaspace       used 104503K, capacity 106913K, committed 107212K, reserved 1142784K
  class space    used 13614K, capacity 14524K, committed 14592K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 33.260 Thread 0x0000029b1afb6000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000029b0835b6c0 relative=0x0000000000000100
Event: 33.260 Thread 0x0000029b1afb6000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029b0835b6c0 method=com.android.tools.r8.internal.Hq.get(I)Ljava/lang/Object; @ 8 c2
Event: 33.260 Thread 0x0000029b1afb6000 DEOPT PACKING pc=0x0000029b0835b6c0 sp=0x000000cd74efce50
Event: 33.260 Thread 0x0000029b1afb6000 DEOPT UNPACKING pc=0x0000029b0004a1af sp=0x000000cd74efcdd8 mode 2
Event: 33.470 Thread 0x0000029b1afb5000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000029b08345040 relative=0x0000000000000200
Event: 33.470 Thread 0x0000029b1afb5000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029b08345040 method=com.android.tools.r8.graph.f1.equals(Ljava/lang/Object;)Z @ 28 c2
Event: 33.470 Thread 0x0000029b1afb5000 DEOPT PACKING pc=0x0000029b08345040 sp=0x000000cd74dfdb50
Event: 33.470 Thread 0x0000029b1afb5000 DEOPT UNPACKING pc=0x0000029b0004a1af sp=0x000000cd74dfdaf0 mode 2
Event: 33.777 Thread 0x0000029b1afb5000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000029b082d5a18 relative=0x00000000000012b8
Event: 33.777 Thread 0x0000029b1afb5000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029b082d5a18 method=java.util.concurrent.ConcurrentHashMap.transfer([Ljava/util/concurrent/ConcurrentHashMap$Node;[Ljava/util/concurrent/ConcurrentHashMap$Node;)V @ 352 c2
Event: 33.777 Thread 0x0000029b1afb5000 DEOPT PACKING pc=0x0000029b082d5a18 sp=0x000000cd74dfdb20
Event: 33.777 Thread 0x0000029b1afb5000 DEOPT UNPACKING pc=0x0000029b0004a1af sp=0x000000cd74dfdac8 mode 2
Event: 33.781 Thread 0x0000029b1afb5000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000029b08292890 relative=0x0000000000000fb0
Event: 33.781 Thread 0x0000029b1afb5000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029b08292890 method=java.util.concurrent.ConcurrentHashMap.transfer([Ljava/util/concurrent/ConcurrentHashMap$Node;[Ljava/util/concurrent/ConcurrentHashMap$Node;)V @ 352 c2
Event: 33.781 Thread 0x0000029b1afb5000 DEOPT PACKING pc=0x0000029b08292890 sp=0x000000cd74dfdb50
Event: 33.781 Thread 0x0000029b1afb5000 DEOPT UNPACKING pc=0x0000029b0004a1af sp=0x000000cd74dfdac8 mode 2
Event: 33.981 Thread 0x0000029b1afb5000 DEOPT PACKING pc=0x0000029b022d5918 sp=0x000000cd74dfdad0
Event: 33.981 Thread 0x0000029b1afb5000 DEOPT UNPACKING pc=0x0000029b0004a95e sp=0x000000cd74dfcfd0 mode 0
Event: 33.983 Thread 0x0000029b1afb5000 DEOPT PACKING pc=0x0000029b022da940 sp=0x000000cd74dfd9f0
Event: 33.983 Thread 0x0000029b1afb5000 DEOPT UNPACKING pc=0x0000029b0004a95e sp=0x000000cd74dfcfd0 mode 0

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 26.912 Thread 0x0000029b0f464000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000aa9e9388}> (0x00000000aa9e9388) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 26.912 Thread 0x0000029b0f464000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000aa9eaa40}> (0x00000000aa9eaa40) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 26.959 Thread 0x0000029b0f464000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa78bda0}: <clinit>> (0x00000000aa78bda0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 26.964 Thread 0x0000029b0f464000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa7d6f88}: <clinit>> (0x00000000aa7d6f88) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 26.968 Thread 0x0000029b0f464000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa61b600}: <clinit>> (0x00000000aa61b600) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 26.970 Thread 0x0000029b0f464000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa6347b8}: <clinit>> (0x00000000aa6347b8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 27.568 Thread 0x0000029b1afac000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a8d14058}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a8d14058) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.069 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab422498}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000ab422498) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.070 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab4276b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000ab4276b0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.072 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aaaa9ce8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000aaaa9ce8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.073 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aaaafe80}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, long)'> (0x00000000aaaafe80) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.074 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aaab63f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object)'> (0x00000000aaab63f8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 29.174 Thread 0x0000029b1afa8000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a9ca5ab0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a9ca5ab0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 30.072 Thread 0x0000029b1afb6000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000aac34da8}> (0x00000000aac34da8) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.603 Thread 0x0000029b1afb5000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000aad20450}> (0x00000000aad20450) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.769 Thread 0x0000029b1afb6000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab034a60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0
Event: 32.015 Thread 0x0000029b1afb6000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ae537b20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int)'> (0x00000000ae537b20) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 32.139 Thread 0x0000029b1afb6000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000ae68fef0}: Found class java.lang.Object, but interface was expected> (0x00000000ae68fef0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 32.143 Thread 0x0000029b1afb6000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000ae6b66e0}: Found class java.lang.Object, but interface was expected> (0x00000000ae6b66e0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 32.144 Thread 0x0000029b1afb6000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000ae6bdb20}: Found class java.lang.Object, but interface was expected> (0x00000000ae6bdb20) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]

Events (20 events):
Event: 33.947 loading class com/android/tools/r8/internal/uc done
Event: 33.949 loading class com/android/tools/r8/synthesis/b
Event: 33.949 loading class com/android/tools/r8/synthesis/b done
Event: 33.950 loading class com/android/tools/r8/graph/F
Event: 33.950 loading class com/android/tools/r8/graph/F done
Event: 33.950 loading class com/android/tools/r8/graph/e1
Event: 33.950 loading class com/android/tools/r8/graph/e1 done
Event: 33.950 loading class com/android/tools/r8/graph/e1
Event: 33.950 loading class com/android/tools/r8/graph/e1 done
Event: 33.951 loading class com/android/tools/r8/synthesis/y$b
Event: 33.951 loading class com/android/tools/r8/synthesis/y$b done
Event: 33.952 loading class com/android/tools/r8/synthesis/y
Event: 33.952 loading class com/android/tools/r8/synthesis/y done
Event: 33.952 loading class com/android/tools/r8/internal/uk
Event: 33.952 loading class com/android/tools/r8/internal/uk done
Event: 34.053 loading class com/android/tools/r8/synthesis/d
Event: 34.053 loading class com/android/tools/r8/synthesis/d done
Event: 34.054 loading class com/android/tools/r8/graph/S2
Event: 34.054 loading class com/android/tools/r8/graph/S2 done
Event: 34.054 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff79da10000 - 0x00007ff79da1a000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa6c5b0000 - 0x00007ffa6c5c7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa42720000 - 0x00007ffa427bd000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa3c770000 - 0x00007ffa3c8b4000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:24 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (366M free)
TotalPageFile size 32602M (AvailPageFile size 179M)
current process WorkingSet (physical memory assigned to process): 563M, peak: 563M
current process commit charge ("private bytes"): 661M, peak: 950M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
