#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 338690048 bytes for Failed to commit area from 0x00000000c1700000 to 0x00000000d5a00000 of length 338690048.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3521), pid=71948, tid=69228
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 18:00:53 2022 India Standard Time elapsed time: 26.175129 seconds (0d 0h 0m 26s)

---------------  T H R E A D  ---------------

Current thread (0x00000280dafcc800):  VMThread "VM Thread" [stack: 0x000000a0ed600000,0x000000a0ed700000] [id=69228]

Stack: [0x000000a0ed600000,0x000000a0ed700000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00000280c621112d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x733535]
V  [jvm.dll+0x7334eb]
V  [jvm.dll+0x5fb080]
V  [jvm.dll+0x5fb818]
C  [ntdll.dll+0xa8fcf]
C  [ntdll.dll+0x35e9a]
C  [ntdll.dll+0xa7fde]
C  0x00000280c621112d

VM_Operation (0x000000a0f30fda00): G1CollectForAllocation, mode: safepoint, requested by thread 0x00000280e2a90000


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000280dfbfada0, length=90, elements={
0x00000280c26ff800, 0x00000280daff0800, 0x00000280daffb800, 0x00000280db985800,
0x00000280db986800, 0x00000280db987800, 0x00000280db988800, 0x00000280db98f000,
0x00000280db991800, 0x00000280dbacb000, 0x00000280dd993000, 0x00000280dc521800,
0x00000280dd244000, 0x00000280dca38800, 0x00000280dc273800, 0x00000280dcca1800,
0x00000280dcca2000, 0x00000280dda49000, 0x00000280dda2d800, 0x00000280dd23c000,
0x00000280dd23e800, 0x00000280dd23b000, 0x00000280dd23f000, 0x00000280dd23d800,
0x00000280dd241000, 0x00000280dd241800, 0x00000280dd23c800, 0x00000280dd240000,
0x00000280de115000, 0x00000280de11a800, 0x00000280de11a000, 0x00000280de11b800,
0x00000280de118000, 0x00000280de115800, 0x00000280de11d000, 0x00000280de11e000,
0x00000280de11c800, 0x00000280de11e800, 0x00000280de119000, 0x00000280de11f800,
0x00000280de120800, 0x00000280de121000, 0x00000280de122000, 0x00000280de123000,
0x00000280de123800, 0x00000280df9b1000, 0x00000280df9b3800, 0x00000280df9b2000,
0x00000280df9b3000, 0x00000280df9b0800, 0x00000280df9ae800, 0x00000280df9b4800,
0x00000280df9af800, 0x00000280df9b7800, 0x00000280df9b9800, 0x00000280df9b5000,
0x00000280df9ba000, 0x00000280df9b8800, 0x00000280df9b7000, 0x00000280df9bb000,
0x00000280df9bc000, 0x00000280df9b6000, 0x00000280df9bc800, 0x00000280df9bd800,
0x00000280dfdb3000, 0x00000280dfdb0800, 0x00000280dfdb1800, 0x00000280dfdb4000,
0x00000280dfdb2800, 0x00000280dfdae800, 0x00000280dfdb5000, 0x00000280dfdaf000,
0x00000280dfdb5800, 0x00000280dfdb0000, 0x00000280dfdb9800, 0x00000280dfdb9000,
0x00000280dfdb6800, 0x00000280dfdb8000, 0x00000280dfdba800, 0x00000280dfdbd000,
0x00000280dfdb7800, 0x00000280e2a8f000, 0x00000280e2a8e800, 0x00000280e2a92800,
0x00000280e2a90800, 0x00000280e2a93000, 0x00000280e2a90000, 0x00000280e2a97800,
0x00000280e2a91800, 0x00000280e2a94000
}

Java Threads: ( => current thread )
  0x00000280c26ff800 JavaThread "main" [_thread_blocked, id=47712, stack(0x000000a0ed000000,0x000000a0ed100000)]
  0x00000280daff0800 JavaThread "Reference Handler" daemon [_thread_blocked, id=57408, stack(0x000000a0ed700000,0x000000a0ed800000)]
  0x00000280daffb800 JavaThread "Finalizer" daemon [_thread_blocked, id=55012, stack(0x000000a0ed800000,0x000000a0ed900000)]
  0x00000280db985800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=71560, stack(0x000000a0ed900000,0x000000a0eda00000)]
  0x00000280db986800 JavaThread "Attach Listener" daemon [_thread_blocked, id=70948, stack(0x000000a0eda00000,0x000000a0edb00000)]
  0x00000280db987800 JavaThread "Service Thread" daemon [_thread_blocked, id=72164, stack(0x000000a0edb00000,0x000000a0edc00000)]
  0x00000280db988800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=16468, stack(0x000000a0edc00000,0x000000a0edd00000)]
  0x00000280db98f000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=29968, stack(0x000000a0edd00000,0x000000a0ede00000)]
  0x00000280db991800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=70256, stack(0x000000a0ede00000,0x000000a0edf00000)]
  0x00000280dbacb000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=73424, stack(0x000000a0edf00000,0x000000a0ee000000)]
  0x00000280dd993000 JavaThread "Daemon health stats" [_thread_blocked, id=71292, stack(0x000000a0ee300000,0x000000a0ee400000)]
  0x00000280dc521800 JavaThread "Incoming local TCP Connector on port 51832" [_thread_in_native, id=71740, stack(0x000000a0ee400000,0x000000a0ee500000)]
  0x00000280dd244000 JavaThread "Daemon periodic checks" [_thread_blocked, id=57340, stack(0x000000a0ee500000,0x000000a0ee600000)]
  0x00000280dca38800 JavaThread "Daemon" [_thread_blocked, id=73140, stack(0x000000a0ee600000,0x000000a0ee700000)]
  0x00000280dc273800 JavaThread "Handler for socket connection from /127.0.0.1:51832 to /127.0.0.1:51833" [_thread_in_native, id=73436, stack(0x000000a0ee700000,0x000000a0ee800000)]
  0x00000280dcca1800 JavaThread "Cancel handler" [_thread_blocked, id=30700, stack(0x000000a0ee800000,0x000000a0ee900000)]
  0x00000280dcca2000 JavaThread "Daemon worker" [_thread_blocked, id=72096, stack(0x000000a0ee900000,0x000000a0eea00000)]
  0x00000280dda49000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51832 to /127.0.0.1:51833" [_thread_blocked, id=73472, stack(0x000000a0eea00000,0x000000a0eeb00000)]
  0x00000280dda2d800 JavaThread "Stdin handler" [_thread_blocked, id=71668, stack(0x000000a0eeb00000,0x000000a0eec00000)]
  0x00000280dd23c000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=69928, stack(0x000000a0eec00000,0x000000a0eed00000)]
  0x00000280dd23e800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=55140, stack(0x000000a0eee00000,0x000000a0eef00000)]
  0x00000280dd23b000 JavaThread "File lock request listener" [_thread_in_native, id=64152, stack(0x000000a0eef00000,0x000000a0ef000000)]
  0x00000280dd23f000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=69432, stack(0x000000a0ef000000,0x000000a0ef100000)]
  0x00000280dd23d800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=72252, stack(0x000000a0ef100000,0x000000a0ef200000)]
  0x00000280dd241000 JavaThread "File watcher server" daemon [_thread_blocked, id=56932, stack(0x000000a0ef200000,0x000000a0ef300000)]
  0x00000280dd241800 JavaThread "File watcher consumer" daemon [_thread_blocked, id=72116, stack(0x000000a0ef300000,0x000000a0ef400000)]
  0x00000280dd23c800 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=73188, stack(0x000000a0ef400000,0x000000a0ef500000)]
  0x00000280dd240000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=50784, stack(0x000000a0ef500000,0x000000a0ef600000)]
  0x00000280de115000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=72248, stack(0x000000a0ef600000,0x000000a0ef700000)]
  0x00000280de11a800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=67348, stack(0x000000a0ef700000,0x000000a0ef800000)]
  0x00000280de11a000 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=71528, stack(0x000000a0ef800000,0x000000a0ef900000)]
  0x00000280de11b800 JavaThread "jar transforms" [_thread_blocked, id=41036, stack(0x000000a0ef900000,0x000000a0efa00000)]
  0x00000280de118000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=72092, stack(0x000000a0efa00000,0x000000a0efb00000)]
  0x00000280de115800 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=58240, stack(0x000000a0efb00000,0x000000a0efc00000)]
  0x00000280de11d000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=73700, stack(0x000000a0efc00000,0x000000a0efd00000)]
  0x00000280de11e000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=71308, stack(0x000000a0efd00000,0x000000a0efe00000)]
  0x00000280de11c800 JavaThread "Unconstrained build operations" [_thread_blocked, id=8620, stack(0x000000a0efe00000,0x000000a0eff00000)]
  0x00000280de11e800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=57440, stack(0x000000a0eff00000,0x000000a0f0000000)]
  0x00000280de119000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=69896, stack(0x000000a0f0000000,0x000000a0f0100000)]
  0x00000280de11f800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=69684, stack(0x000000a0f0100000,0x000000a0f0200000)]
  0x00000280de120800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=32212, stack(0x000000a0f0200000,0x000000a0f0300000)]
  0x00000280de121000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=55100, stack(0x000000a0f0300000,0x000000a0f0400000)]
  0x00000280de122000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=72152, stack(0x000000a0f0400000,0x000000a0f0500000)]
  0x00000280de123000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=73668, stack(0x000000a0f0500000,0x000000a0f0600000)]
  0x00000280de123800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=71428, stack(0x000000a0f0600000,0x000000a0f0700000)]
  0x00000280df9b1000 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=70080, stack(0x000000a0f0700000,0x000000a0f0800000)]
  0x00000280df9b3800 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=61284, stack(0x000000a0f0800000,0x000000a0f0900000)]
  0x00000280df9b2000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=73404, stack(0x000000a0f0900000,0x000000a0f0a00000)]
  0x00000280df9b3000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=51668, stack(0x000000a0f0a00000,0x000000a0f0b00000)]
  0x00000280df9b0800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=63664, stack(0x000000a0f0b00000,0x000000a0f0c00000)]
  0x00000280df9ae800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=67428, stack(0x000000a0f0c00000,0x000000a0f0d00000)]
  0x00000280df9b4800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=68276, stack(0x000000a0f0d00000,0x000000a0f0e00000)]
  0x00000280df9af800 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=38728, stack(0x000000a0f0e00000,0x000000a0f0f00000)]
  0x00000280df9b7800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=69840, stack(0x000000a0f0f00000,0x000000a0f1000000)]
  0x00000280df9b9800 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=73592, stack(0x000000a0f1000000,0x000000a0f1100000)]
  0x00000280df9b5000 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=70720, stack(0x000000a0f1100000,0x000000a0f1200000)]
  0x00000280df9ba000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=65196, stack(0x000000a0f1200000,0x000000a0f1300000)]
  0x00000280df9b8800 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=57068, stack(0x000000a0f1300000,0x000000a0f1400000)]
  0x00000280df9b7000 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=73576, stack(0x000000a0f1400000,0x000000a0f1500000)]
  0x00000280df9bb000 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=70952, stack(0x000000a0f1500000,0x000000a0f1600000)]
  0x00000280df9bc000 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=32888, stack(0x000000a0f1600000,0x000000a0f1700000)]
  0x00000280df9b6000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=67460, stack(0x000000a0f1700000,0x000000a0f1800000)]
  0x00000280df9bc800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=72844, stack(0x000000a0f1800000,0x000000a0f1900000)]
  0x00000280df9bd800 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=62892, stack(0x000000a0f1900000,0x000000a0f1a00000)]
  0x00000280dfdb3000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=72264, stack(0x000000a0f1a00000,0x000000a0f1b00000)]
  0x00000280dfdb0800 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=72872, stack(0x000000a0f1b00000,0x000000a0f1c00000)]
  0x00000280dfdb1800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=73360, stack(0x000000a0f1c00000,0x000000a0f1d00000)]
  0x00000280dfdb4000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=50192, stack(0x000000a0f1d00000,0x000000a0f1e00000)]
  0x00000280dfdb2800 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=60616, stack(0x000000a0f1e00000,0x000000a0f1f00000)]
  0x00000280dfdae800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=73180, stack(0x000000a0f1f00000,0x000000a0f2000000)]
  0x00000280dfdb5000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=53076, stack(0x000000a0f2000000,0x000000a0f2100000)]
  0x00000280dfdaf000 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=54080, stack(0x000000a0f2100000,0x000000a0f2200000)]
  0x00000280dfdb5800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=68204, stack(0x000000a0f2200000,0x000000a0f2300000)]
  0x00000280dfdb0000 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=73316, stack(0x000000a0f2300000,0x000000a0f2400000)]
  0x00000280dfdb9800 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=71452, stack(0x000000a0f2400000,0x000000a0f2500000)]
  0x00000280dfdb9000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=67320, stack(0x000000a0f2500000,0x000000a0f2600000)]
  0x00000280dfdb6800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=71188, stack(0x000000a0f2600000,0x000000a0f2700000)]
  0x00000280dfdb8000 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=69544, stack(0x000000a0f2700000,0x000000a0f2800000)]
  0x00000280dfdba800 JavaThread "Memory manager" [_thread_blocked, id=72320, stack(0x000000a0f2800000,0x000000a0f2900000)]
  0x00000280dfdbd000 JavaThread "build event listener" [_thread_blocked, id=56312, stack(0x000000a0f2900000,0x000000a0f2a00000)]
  0x00000280dfdb7800 JavaThread "Execution worker for ':'" [_thread_blocked, id=73324, stack(0x000000a0f2a00000,0x000000a0f2b00000)]
  0x00000280e2a8f000 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=72288, stack(0x000000a0f2b00000,0x000000a0f2c00000)]
  0x00000280e2a8e800 JavaThread "Execution worker for ':' Thread 3" [_thread_blocked, id=73608, stack(0x000000a0f2c00000,0x000000a0f2d00000)]
  0x00000280e2a92800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=11440, stack(0x000000a0f2d00000,0x000000a0f2e00000)]
  0x00000280e2a90800 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=73132, stack(0x000000a0f2e00000,0x000000a0f2f00000)]
  0x00000280e2a93000 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=65608, stack(0x000000a0f2f00000,0x000000a0f3000000)]
  0x00000280e2a90000 JavaThread "ForkJoinPool-1-worker-3" daemon [_thread_blocked, id=73308, stack(0x000000a0f3000000,0x000000a0f3100000)]
  0x00000280e2a97800 JavaThread "ForkJoinPool-1-worker-5" daemon [_thread_blocked, id=45280, stack(0x000000a0f3100000,0x000000a0f3200000)]
  0x00000280e2a91800 JavaThread "ForkJoinPool-1-worker-7" daemon [_thread_blocked, id=72888, stack(0x000000a0f3200000,0x000000a0f3300000)]
  0x00000280e2a94000 JavaThread "ForkJoinPool-1-worker-1" daemon [_thread_blocked, id=37372, stack(0x000000a0f3300000,0x000000a0f3400000)]

Other Threads:
=>0x00000280dafcc800 VMThread "VM Thread" [stack: 0x000000a0ed600000,0x000000a0ed700000] [id=69228]
  0x00000280dbafe800 WatcherThread [stack: 0x000000a0ee000000,0x000000a0ee100000] [id=50140]
  0x00000280c2718000 GCTaskThread "GC Thread#0" [stack: 0x000000a0ed100000,0x000000a0ed200000] [id=58100]
  0x00000280dbd25000 GCTaskThread "GC Thread#1" [stack: 0x000000a0ee100000,0x000000a0ee200000] [id=72296]
  0x00000280dbee5000 GCTaskThread "GC Thread#2" [stack: 0x000000a0ee200000,0x000000a0ee300000] [id=73108]
  0x00000280dd35b000 GCTaskThread "GC Thread#3" [stack: 0x000000a0eed00000,0x000000a0eee00000] [id=72932]
  0x00000280c273e000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000a0ed200000,0x000000a0ed300000] [id=58156]
  0x00000280c2741000 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000a0ed300000,0x000000a0ed400000] [id=71620]
  0x00000280c27d2000 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000a0ed400000,0x000000a0ed500000] [id=71704]
  0x00000280e47f3000 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000a0f3400000,0x000000a0f3500000] [id=71640]
  0x00000280e47f3800 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000a0f3500000,0x000000a0f3600000] [id=72648]
  0x00000280c27d6800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x000000a0ed500000,0x000000a0ed600000] [id=70980]

Threads with active compile tasks:
C2 CompilerThread0  26205 12680 %     4       com.android.tools.r8.dex.j::a @ 313 (1042 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000280c26fbe60] Threads_lock - owner thread: 0x00000280dafcc800
[0x00000280c26fa510] Heap_lock - owner thread: 0x00000280e2a90000

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 878592K, used 372224K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 102622K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0100000| Untracked 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0200000| Untracked 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0300000| Untracked 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0400000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0500000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0600000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0700000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0800000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0900000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0a00000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0b00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0c00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0d00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0e00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0f00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a1000000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1100000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1200000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1300000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1400000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1500000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1600000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1700000, 0x00000000a1700000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1800000, 0x00000000a1800000| Untracked 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1900000, 0x00000000a1900000| Untracked 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1a00000, 0x00000000a1a00000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HS|  |TAMS 0x00000000a1b00000, 0x00000000a1b00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HS|  |TAMS 0x00000000a1c00000, 0x00000000a1c00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HS|  |TAMS 0x00000000a1d00000, 0x00000000a1d00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%|HS|  |TAMS 0x00000000a1e00000, 0x00000000a1e00000| Complete 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%|HC|  |TAMS 0x00000000a1f00000, 0x00000000a1f00000| Complete 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%|HC|  |TAMS 0x00000000a2000000, 0x00000000a2000000| Complete 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%|HS|  |TAMS 0x00000000a2100000, 0x00000000a2100000| Complete 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%|HC|  |TAMS 0x00000000a2200000, 0x00000000a2200000| Complete 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2300000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2400000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2500000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2600000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2700000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2800000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2900000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2a00000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2b00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2c00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2d00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2e00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2f00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a3000000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3100000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3200000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3300000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3400000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3500000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3600000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3700000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3800000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3900000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3a00000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3b00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3c00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3d00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3e00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3f00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a4000000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4100000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4200000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4300000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4400000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4500000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4600000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4700000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4800000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4900000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4a00000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4b00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4c00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4d00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4e00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4f00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000, 0x00000000a5000000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000, 0x00000000a5100000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5200000, 0x00000000a5200000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%|HS|  |TAMS 0x00000000a5300000, 0x00000000a5300000| Complete 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%|HC|  |TAMS 0x00000000a5400000, 0x00000000a5400000| Complete 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%|HS|  |TAMS 0x00000000a5500000, 0x00000000a5500000| Complete 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%|HS|  |TAMS 0x00000000a5600000, 0x00000000a5600000| Complete 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%|HS|  |TAMS 0x00000000a5700000, 0x00000000a5700000| Complete 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%|HC|  |TAMS 0x00000000a5800000, 0x00000000a5800000| Complete 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%|HS|  |TAMS 0x00000000a5900000, 0x00000000a5900000| Complete 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%|HC|  |TAMS 0x00000000a5a00000, 0x00000000a5a00000| Complete 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%|HS|  |TAMS 0x00000000a5b00000, 0x00000000a5b00000| Complete 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%|HS|  |TAMS 0x00000000a5c00000, 0x00000000a5c00000| Complete 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%|HS|  |TAMS 0x00000000a5d00000, 0x00000000a5d00000| Complete 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%|HC|  |TAMS 0x00000000a5e00000, 0x00000000a5e00000| Complete 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%|HS|  |TAMS 0x00000000a5f00000, 0x00000000a5f00000| Complete 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%|HC|  |TAMS 0x00000000a6000000, 0x00000000a6000000| Complete 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6100000, 0x00000000a6100000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000, 0x00000000a6200000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000, 0x00000000a6300000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6400000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6500000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6600000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6700000, 0x00000000a6700000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|  |TAMS 0x00000000a6800000, 0x00000000a6800000| Untracked 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000, 0x00000000a6900000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000, 0x00000000a6a00000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%|HS|  |TAMS 0x00000000a6b00000, 0x00000000a6b00000| Complete 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%|HC|  |TAMS 0x00000000a6c00000, 0x00000000a6c00000| Complete 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%|HC|  |TAMS 0x00000000a6d00000, 0x00000000a6d00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%|HC|  |TAMS 0x00000000a6e00000, 0x00000000a6e00000| Complete 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%|HS|  |TAMS 0x00000000a6f00000, 0x00000000a6f00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%|HS|  |TAMS 0x00000000a7000000, 0x00000000a7000000| Complete 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%|HC|  |TAMS 0x00000000a7100000, 0x00000000a7100000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%|HS|  |TAMS 0x00000000a7200000, 0x00000000a7200000| Complete 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%|HC|  |TAMS 0x00000000a7300000, 0x00000000a7300000| Complete 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%|HC|  |TAMS 0x00000000a7400000, 0x00000000a7400000| Complete 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%|HS|  |TAMS 0x00000000a7500000, 0x00000000a7500000| Complete 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%|HS|  |TAMS 0x00000000a7600000, 0x00000000a7600000| Complete 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%|HS|  |TAMS 0x00000000a7700000, 0x00000000a7700000| Complete 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%|HS|  |TAMS 0x00000000a7800000, 0x00000000a7800000| Complete 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000, 0x00000000a7900000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000, 0x00000000a7a00000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000, 0x00000000a7b00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000, 0x00000000a7c00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7d00000, 0x00000000a7d00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7e00000, 0x00000000a7e00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7f00000, 0x00000000a7f00000| Untracked 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%|HS|  |TAMS 0x00000000a8000000, 0x00000000a8000000| Complete 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8100000, 0x00000000a8100000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%|HS|  |TAMS 0x00000000a8200000, 0x00000000a8200000| Complete 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%|HC|  |TAMS 0x00000000a8300000, 0x00000000a8300000| Complete 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HS|  |TAMS 0x00000000a8400000, 0x00000000a8400000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%|HS|  |TAMS 0x00000000a8500000, 0x00000000a8500000| Complete 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8600000, 0x00000000a8600000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%|HS|  |TAMS 0x00000000a8700000, 0x00000000a8700000| Complete 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%|HS|  |TAMS 0x00000000a8800000, 0x00000000a8800000| Complete 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8900000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8a00000, 0x00000000a8a00000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%|HS|  |TAMS 0x00000000a8b00000, 0x00000000a8b00000| Complete 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%|HC|  |TAMS 0x00000000a8c00000, 0x00000000a8c00000| Complete 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8d00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8e00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8f00000, 0x00000000a8f00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%|HS|  |TAMS 0x00000000a9000000, 0x00000000a9000000| Complete 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%|HC|  |TAMS 0x00000000a9100000, 0x00000000a9100000| Complete 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9200000, 0x00000000a9200000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%|HS|  |TAMS 0x00000000a9300000, 0x00000000a9300000| Complete 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9400000, 0x00000000a9400000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%|HS|  |TAMS 0x00000000a9500000, 0x00000000a9500000| Complete 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9600000, 0x00000000a9600000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|  |TAMS 0x00000000a9700000, 0x00000000a9700000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9800000, 0x00000000a9800000| Untracked 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%|HS|  |TAMS 0x00000000a9900000, 0x00000000a9900000| Complete 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%|HC|  |TAMS 0x00000000a9a00000, 0x00000000a9a00000| Complete 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000, 0x00000000a9b00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9c00000, 0x00000000a9c00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9d00000, 0x00000000a9d00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%|HS|  |TAMS 0x00000000a9e00000, 0x00000000a9e00000| Complete 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%|HC|  |TAMS 0x00000000a9f00000, 0x00000000a9f00000| Complete 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000aa000000, 0x00000000aa000000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa100000, 0x00000000aa100000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa200000, 0x00000000aa200000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa200000, 0x00000000aa300000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa400000, 0x00000000aa400000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa500000, 0x00000000aa500000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa600000, 0x00000000aa600000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| O|  |TAMS 0x00000000aa700000, 0x00000000aa700000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|  |TAMS 0x00000000aa800000, 0x00000000aa800000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000, 0x00000000aa900000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aaa00000, 0x00000000aaa00000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aab00000, 0x00000000aab00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aac00000, 0x00000000aac00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aac00000, 0x00000000aad00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|  |TAMS 0x00000000aae00000, 0x00000000aae00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%|HS|  |TAMS 0x00000000aaf00000, 0x00000000aaf00000| Complete 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%|HC|  |TAMS 0x00000000ab000000, 0x00000000ab000000| Complete 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%|HC|  |TAMS 0x00000000ab100000, 0x00000000ab100000| Complete 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%|HC|  |TAMS 0x00000000ab200000, 0x00000000ab200000| Complete 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%|HS|  |TAMS 0x00000000ab300000, 0x00000000ab300000| Complete 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab400000, 0x00000000ab400000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab500000, 0x00000000ab500000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab600000, 0x00000000ab600000| Untracked 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%|HS|  |TAMS 0x00000000ab700000, 0x00000000ab700000| Complete 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%|HC|  |TAMS 0x00000000ab800000, 0x00000000ab800000| Complete 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab900000, 0x00000000ab900000| Untracked 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000aba00000, 0x00000000aba00000| Untracked 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000abb00000, 0x00000000abb00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000, 0x00000000abc00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%|HS|  |TAMS 0x00000000abd00000, 0x00000000abd00000| Complete 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%|HC|  |TAMS 0x00000000abe00000, 0x00000000abe00000| Complete 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%|HS|  |TAMS 0x00000000abf00000, 0x00000000abf00000| Complete 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%|HC|  |TAMS 0x00000000ac000000, 0x00000000ac000000| Complete 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%|HC|  |TAMS 0x00000000ac100000, 0x00000000ac100000| Complete 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%| O|  |TAMS 0x00000000ac200000, 0x00000000ac200000| Untracked 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%|HS|  |TAMS 0x00000000ac300000, 0x00000000ac300000| Complete 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%| O|  |TAMS 0x00000000ac400000, 0x00000000ac400000| Untracked 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac500000, 0x00000000ac500000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%| O|  |TAMS 0x00000000ac600000, 0x00000000ac600000| Untracked 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac700000, 0x00000000ac700000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%|HS|  |TAMS 0x00000000ac800000, 0x00000000ac800000| Complete 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%|HC|  |TAMS 0x00000000ac900000, 0x00000000ac900000| Complete 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%|HS|  |TAMS 0x00000000aca00000, 0x00000000aca00000| Complete 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000acb00000, 0x00000000acb00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acc00000, 0x00000000acc00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acd00000, 0x00000000acd00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000ace00000, 0x00000000ace00000| Untracked 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000acf00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000ad000000, 0x00000000ad000000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000, 0x00000000ad100000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000, 0x00000000ad200000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000, 0x00000000ad300000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000, 0x00000000ad400000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000, 0x00000000ad500000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000, 0x00000000ad600000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad700000, 0x00000000ad700000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad800000, 0x00000000ad800000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad900000, 0x00000000ad900000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ada00000, 0x00000000ada00000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000adb00000, 0x00000000adb00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adc00000, 0x00000000adc00000| Untracked 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%| O|  |TAMS 0x00000000add00000, 0x00000000add00000| Untracked 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000ade00000, 0x00000000ade00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000adf00000, 0x00000000adf00000| Untracked 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000ae000000, 0x00000000ae000000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae100000, 0x00000000ae100000| Untracked 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae200000, 0x00000000ae200000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae300000, 0x00000000ae300000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae400000, 0x00000000ae400000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae500000, 0x00000000ae500000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae600000, 0x00000000ae600000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae700000, 0x00000000ae700000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae800000, 0x00000000ae800000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%| O|  |TAMS 0x00000000ae900000, 0x00000000ae900000| Untracked 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000aea00000, 0x00000000aea00000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aeb00000, 0x00000000aeb00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aec00000, 0x00000000aec00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aed00000, 0x00000000aed00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aee00000, 0x00000000aee00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aef00000, 0x00000000aef00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000af000000, 0x00000000af000000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af100000, 0x00000000af100000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af200000, 0x00000000af200000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af300000, 0x00000000af300000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af400000, 0x00000000af400000| Untracked 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af500000, 0x00000000af500000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af600000, 0x00000000af600000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af700000, 0x00000000af700000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af800000, 0x00000000af800000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| O|  |TAMS 0x00000000af900000, 0x00000000af900000| Untracked 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| O|  |TAMS 0x00000000afa00000, 0x00000000afa00000| Untracked 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| O|  |TAMS 0x00000000afb00000, 0x00000000afb00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afc00000, 0x00000000afc00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afd00000, 0x00000000afd00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afe00000, 0x00000000afe00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%| O|  |TAMS 0x00000000aff00000, 0x00000000aff00000| Untracked 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%| O|  |TAMS 0x00000000b0000000, 0x00000000b0000000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0100000, 0x00000000b0100000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0200000, 0x00000000b0200000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0300000, 0x00000000b0300000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0400000, 0x00000000b0400000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0500000, 0x00000000b0500000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0600000, 0x00000000b0600000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0700000, 0x00000000b0700000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0800000, 0x00000000b0800000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0900000, 0x00000000b0900000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%| O|  |TAMS 0x00000000b0a00000, 0x00000000b0a00000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%| O|  |TAMS 0x00000000b0b00000, 0x00000000b0b00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%| O|  |TAMS 0x00000000b0c00000, 0x00000000b0c00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%| O|  |TAMS 0x00000000b0d00000, 0x00000000b0d00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|  |TAMS 0x00000000b0e00000, 0x00000000b0e00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0f00000, 0x00000000b0f00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b1000000, 0x00000000b1000000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1100000, 0x00000000b1100000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1200000, 0x00000000b1200000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1300000, 0x00000000b1300000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1400000, 0x00000000b1400000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1500000, 0x00000000b1500000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1600000, 0x00000000b1600000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1700000, 0x00000000b1700000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1800000, 0x00000000b1800000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| O|  |TAMS 0x00000000b1880000, 0x00000000b1900000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1a00000, 0x00000000b1a00000|100%| O|  |TAMS 0x00000000b1900000, 0x00000000b1a00000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|  |TAMS 0x00000000b1a00000, 0x00000000b1b00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|  |TAMS 0x00000000b1b00000, 0x00000000b1c00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|  |TAMS 0x00000000b1c00000, 0x00000000b1d00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|  |TAMS 0x00000000b1d00000, 0x00000000b1e00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|  |TAMS 0x00000000b1e00000, 0x00000000b1f00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|  |TAMS 0x00000000b1f00000, 0x00000000b2000000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|  |TAMS 0x00000000b2000000, 0x00000000b2100000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|  |TAMS 0x00000000b2100000, 0x00000000b2200000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|  |TAMS 0x00000000b2200000, 0x00000000b2300000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|  |TAMS 0x00000000b2300000, 0x00000000b2400000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|  |TAMS 0x00000000b2400000, 0x00000000b2500000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|  |TAMS 0x00000000b2500000, 0x00000000b2600000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|  |TAMS 0x00000000b2600000, 0x00000000b2700000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|  |TAMS 0x00000000b2700000, 0x00000000b2800000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2800000, 0x00000000b2900000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2900000, 0x00000000b2a00000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2a00000, 0x00000000b2b00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2b00000, 0x00000000b2c00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| O|  |TAMS 0x00000000b2c00000, 0x00000000b2d00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2d00000, 0x00000000b2e00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| O|  |TAMS 0x00000000b2e00000, 0x00000000b2f00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| O|  |TAMS 0x00000000b2f00000, 0x00000000b3000000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3000000, 0x00000000b3100000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3100000, 0x00000000b3200000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000, 0x00000000b3300000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|  |TAMS 0x00000000b3300000, 0x00000000b3400000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3400000, 0x00000000b3500000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3500000, 0x00000000b3600000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3600000, 0x00000000b3700000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3700000, 0x00000000b3800000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3800000, 0x00000000b3900000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3900000, 0x00000000b3a00000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3a00000, 0x00000000b3b00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|  |TAMS 0x00000000b3b00000, 0x00000000b3c00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3d00000, 0x00000000b3d00000|100%| O|  |TAMS 0x00000000b3c00000, 0x00000000b3d00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%| O|  |TAMS 0x00000000b3d00000, 0x00000000b3e00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3e00000, 0x00000000b3f00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b3f00000, 0x00000000b4000000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4100000, 0x00000000b4100000|100%| O|  |TAMS 0x00000000b4000000, 0x00000000b4100000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4200000, 0x00000000b4200000|100%| O|  |TAMS 0x00000000b4100000, 0x00000000b4200000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4200000, 0x00000000b4300000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|  |TAMS 0x00000000b4300000, 0x00000000b4400000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4400000, 0x00000000b4500000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4600000, 0x00000000b4600000|100%| O|  |TAMS 0x00000000b4500000, 0x00000000b4600000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4600000, 0x00000000b4700000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4800000, 0x00000000b4800000|100%| O|  |TAMS 0x00000000b4700000, 0x00000000b4800000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4800000, 0x00000000b4900000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4a00000, 0x00000000b4a00000|100%| O|  |TAMS 0x00000000b4900000, 0x00000000b4a00000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%| O|  |TAMS 0x00000000b4a00000, 0x00000000b4b00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%| O|  |TAMS 0x00000000b4b00000, 0x00000000b4c00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4c00000, 0x00000000b4d00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4e00000, 0x00000000b4e00000|100%| O|  |TAMS 0x00000000b4d00000, 0x00000000b4e00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4f00000, 0x00000000b4f00000|100%| O|  |TAMS 0x00000000b4e00000, 0x00000000b4f00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b4f00000, 0x00000000b5000000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5100000, 0x00000000b5100000|100%| O|  |TAMS 0x00000000b5000000, 0x00000000b5100000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5100000, 0x00000000b5200000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5200000, 0x00000000b5300000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%| O|  |TAMS 0x00000000b5300000, 0x00000000b5400000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%| O|  |TAMS 0x00000000b5400000, 0x00000000b5500000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5600000, 0x00000000b5600000|100%| O|  |TAMS 0x00000000b5500000, 0x00000000b5600000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5600000, 0x00000000b5700000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5700000, 0x00000000b5800000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|  |TAMS 0x00000000b5800000, 0x00000000b5900000| Untracked 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|  |TAMS 0x00000000b5900000, 0x00000000b5a00000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5b00000, 0x00000000b5b00000|100%| O|  |TAMS 0x00000000b5a00000, 0x00000000b5b00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|  |TAMS 0x00000000b5b00000, 0x00000000b5c00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|  |TAMS 0x00000000b5c00000, 0x00000000b5d00000| Untracked 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%| O|  |TAMS 0x00000000b5d00000, 0x00000000b5e00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%| O|  |TAMS 0x00000000b5e00000, 0x00000000b5f00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b5f00000, 0x00000000b6000000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6100000, 0x00000000b6100000|100%| O|  |TAMS 0x00000000b6000000, 0x00000000b6100000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6180000, 0x00000000b6200000| 50%| O|  |TAMS 0x00000000b6100000, 0x00000000b6180000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6200000, 0x00000000b6300000|  0%| F|  |TAMS 0x00000000b6200000, 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6300000, 0x00000000b6400000|  0%| F|  |TAMS 0x00000000b6300000, 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6400000, 0x00000000b6500000|  0%| F|  |TAMS 0x00000000b6400000, 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6500000, 0x00000000b6600000|  0%| F|  |TAMS 0x00000000b6500000, 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6600000, 0x00000000b6700000|  0%| F|  |TAMS 0x00000000b6600000, 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6700000, 0x00000000b6800000|  0%| F|  |TAMS 0x00000000b6700000, 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6800000, 0x00000000b6900000|  0%| F|  |TAMS 0x00000000b6800000, 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6900000, 0x00000000b6a00000|  0%| F|  |TAMS 0x00000000b6900000, 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6a00000, 0x00000000b6b00000|  0%| F|  |TAMS 0x00000000b6a00000, 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6b00000, 0x00000000b6c00000|  0%| F|  |TAMS 0x00000000b6b00000, 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6c00000, 0x00000000b6d00000|  0%| F|  |TAMS 0x00000000b6c00000, 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6d00000, 0x00000000b6e00000|  0%| F|  |TAMS 0x00000000b6d00000, 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6e00000, 0x00000000b6f00000|  0%| F|  |TAMS 0x00000000b6e00000, 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b6f00000, 0x00000000b7000000|  0%| F|  |TAMS 0x00000000b6f00000, 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7000000, 0x00000000b7100000|  0%| F|  |TAMS 0x00000000b7000000, 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7100000, 0x00000000b7200000|  0%| F|  |TAMS 0x00000000b7100000, 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7200000, 0x00000000b7300000|  0%| F|  |TAMS 0x00000000b7200000, 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7300000, 0x00000000b7400000|  0%| F|  |TAMS 0x00000000b7300000, 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7400000, 0x00000000b7500000|  0%| F|  |TAMS 0x00000000b7400000, 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7500000, 0x00000000b7600000|  0%| F|  |TAMS 0x00000000b7500000, 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7600000, 0x00000000b7700000|  0%| F|  |TAMS 0x00000000b7600000, 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7700000, 0x00000000b7800000|  0%| F|  |TAMS 0x00000000b7700000, 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7800000, 0x00000000b7900000|  0%| F|  |TAMS 0x00000000b7800000, 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7900000, 0x00000000b7a00000|  0%| F|  |TAMS 0x00000000b7900000, 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7a00000, 0x00000000b7b00000|  0%| F|  |TAMS 0x00000000b7a00000, 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7b00000, 0x00000000b7c00000|  0%| F|  |TAMS 0x00000000b7b00000, 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7c00000, 0x00000000b7d00000|  0%| F|  |TAMS 0x00000000b7c00000, 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7d00000, 0x00000000b7e00000|  0%| F|  |TAMS 0x00000000b7d00000, 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7e00000, 0x00000000b7f00000|  0%| F|  |TAMS 0x00000000b7e00000, 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b7f00000, 0x00000000b8000000|  0%| F|  |TAMS 0x00000000b7f00000, 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8000000, 0x00000000b8100000|  0%| F|  |TAMS 0x00000000b8000000, 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8100000, 0x00000000b8200000|  0%| F|  |TAMS 0x00000000b8100000, 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8200000, 0x00000000b8300000|  0%| F|  |TAMS 0x00000000b8200000, 0x00000000b8200000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000, 0x00000000b8300000| Untracked 
| 388|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000, 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000, 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000, 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000, 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000, 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000, 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000, 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000, 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000, 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000, 0x00000000b8d00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000, 0x00000000b8e00000| Untracked 
| 399|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000, 0x00000000b8f00000| Untracked 
| 400|0x00000000b9000000, 0x00000000b9000000, 0x00000000b9100000|  0%| F|  |TAMS 0x00000000b9000000, 0x00000000b9000000| Untracked 
| 401|0x00000000b9100000, 0x00000000b9100000, 0x00000000b9200000|  0%| F|  |TAMS 0x00000000b9100000, 0x00000000b9100000| Untracked 
| 402|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000, 0x00000000b9200000| Untracked 
| 403|0x00000000b9300000, 0x00000000b9300000, 0x00000000b9400000|  0%| F|  |TAMS 0x00000000b9300000, 0x00000000b9300000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000, 0x00000000b9400000| Untracked 
| 405|0x00000000b9500000, 0x00000000b9500000, 0x00000000b9600000|  0%| F|  |TAMS 0x00000000b9500000, 0x00000000b9500000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9600000, 0x00000000b9700000|  0%| F|  |TAMS 0x00000000b9600000, 0x00000000b9600000| Untracked 
| 407|0x00000000b9700000, 0x00000000b9700000, 0x00000000b9800000|  0%| F|  |TAMS 0x00000000b9700000, 0x00000000b9700000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9800000, 0x00000000b9900000|  0%| F|  |TAMS 0x00000000b9800000, 0x00000000b9800000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9900000, 0x00000000b9a00000|  0%| F|  |TAMS 0x00000000b9900000, 0x00000000b9900000| Untracked 
| 410|0x00000000b9a00000, 0x00000000b9a00000, 0x00000000b9b00000|  0%| F|  |TAMS 0x00000000b9a00000, 0x00000000b9a00000| Untracked 
| 411|0x00000000b9b00000, 0x00000000b9b00000, 0x00000000b9c00000|  0%| F|  |TAMS 0x00000000b9b00000, 0x00000000b9b00000| Untracked 
| 412|0x00000000b9c00000, 0x00000000b9c00000, 0x00000000b9d00000|  0%| F|  |TAMS 0x00000000b9c00000, 0x00000000b9c00000| Untracked 
| 413|0x00000000b9d00000, 0x00000000b9d00000, 0x00000000b9e00000|  0%| F|  |TAMS 0x00000000b9d00000, 0x00000000b9d00000| Untracked 
| 414|0x00000000b9e00000, 0x00000000b9e00000, 0x00000000b9f00000|  0%| F|  |TAMS 0x00000000b9e00000, 0x00000000b9e00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000b9f00000, 0x00000000ba000000|  0%| F|  |TAMS 0x00000000b9f00000, 0x00000000b9f00000| Untracked 
| 416|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000, 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000, 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000, 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba300000, 0x00000000ba400000|  0%| F|  |TAMS 0x00000000ba300000, 0x00000000ba300000| Untracked 
| 420|0x00000000ba400000, 0x00000000ba400000, 0x00000000ba500000|  0%| F|  |TAMS 0x00000000ba400000, 0x00000000ba400000| Untracked 
| 421|0x00000000ba500000, 0x00000000ba500000, 0x00000000ba600000|  0%| F|  |TAMS 0x00000000ba500000, 0x00000000ba500000| Untracked 
| 422|0x00000000ba600000, 0x00000000ba600000, 0x00000000ba700000|  0%| F|  |TAMS 0x00000000ba600000, 0x00000000ba600000| Untracked 
| 423|0x00000000ba700000, 0x00000000ba700000, 0x00000000ba800000|  0%| F|  |TAMS 0x00000000ba700000, 0x00000000ba700000| Untracked 
| 424|0x00000000ba800000, 0x00000000ba800000, 0x00000000ba900000|  0%| F|  |TAMS 0x00000000ba800000, 0x00000000ba800000| Untracked 
| 425|0x00000000ba900000, 0x00000000ba900000, 0x00000000baa00000|  0%| F|  |TAMS 0x00000000ba900000, 0x00000000ba900000| Untracked 
| 426|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000, 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bab00000, 0x00000000bac00000|  0%| F|  |TAMS 0x00000000bab00000, 0x00000000bab00000| Untracked 
| 428|0x00000000bac00000, 0x00000000bac00000, 0x00000000bad00000|  0%| F|  |TAMS 0x00000000bac00000, 0x00000000bac00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bad00000, 0x00000000bae00000|  0%| F|  |TAMS 0x00000000bad00000, 0x00000000bad00000| Untracked 
| 430|0x00000000bae00000, 0x00000000bae00000, 0x00000000baf00000|  0%| F|  |TAMS 0x00000000bae00000, 0x00000000bae00000| Untracked 
| 431|0x00000000baf00000, 0x00000000baf00000, 0x00000000bb000000|  0%| F|  |TAMS 0x00000000baf00000, 0x00000000baf00000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb000000, 0x00000000bb100000|  0%| F|  |TAMS 0x00000000bb000000, 0x00000000bb000000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb100000, 0x00000000bb200000|  0%| F|  |TAMS 0x00000000bb100000, 0x00000000bb100000| Untracked 
| 434|0x00000000bb200000, 0x00000000bb200000, 0x00000000bb300000|  0%| F|  |TAMS 0x00000000bb200000, 0x00000000bb200000| Untracked 
| 435|0x00000000bb300000, 0x00000000bb300000, 0x00000000bb400000|  0%| F|  |TAMS 0x00000000bb300000, 0x00000000bb300000| Untracked 
| 436|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000, 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000, 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000, 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb700000, 0x00000000bb800000|  0%| F|  |TAMS 0x00000000bb700000, 0x00000000bb700000| Untracked 
| 440|0x00000000bb800000, 0x00000000bb800000, 0x00000000bb900000|  0%| F|  |TAMS 0x00000000bb800000, 0x00000000bb800000| Untracked 
| 441|0x00000000bb900000, 0x00000000bb900000, 0x00000000bba00000|  0%| F|  |TAMS 0x00000000bb900000, 0x00000000bb900000| Untracked 
| 442|0x00000000bba00000, 0x00000000bba00000, 0x00000000bbb00000|  0%| F|  |TAMS 0x00000000bba00000, 0x00000000bba00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000, 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000, 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbd00000, 0x00000000bbe00000|  0%| F|  |TAMS 0x00000000bbd00000, 0x00000000bbd00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbe00000, 0x00000000bbf00000|  0%| F|  |TAMS 0x00000000bbe00000, 0x00000000bbe00000| Untracked 
| 447|0x00000000bbf00000, 0x00000000bbf00000, 0x00000000bc000000|  0%| F|  |TAMS 0x00000000bbf00000, 0x00000000bbf00000| Untracked 
| 448|0x00000000bc000000, 0x00000000bc000000, 0x00000000bc100000|  0%| F|  |TAMS 0x00000000bc000000, 0x00000000bc000000| Untracked 
| 449|0x00000000bc100000, 0x00000000bc100000, 0x00000000bc200000|  0%| F|  |TAMS 0x00000000bc100000, 0x00000000bc100000| Untracked 
| 450|0x00000000bc200000, 0x00000000bc200000, 0x00000000bc300000|  0%| F|  |TAMS 0x00000000bc200000, 0x00000000bc200000| Untracked 
| 451|0x00000000bc300000, 0x00000000bc300000, 0x00000000bc400000|  0%| F|  |TAMS 0x00000000bc300000, 0x00000000bc300000| Untracked 
| 452|0x00000000bc400000, 0x00000000bc400000, 0x00000000bc500000|  0%| F|  |TAMS 0x00000000bc400000, 0x00000000bc400000| Untracked 
| 453|0x00000000bc500000, 0x00000000bc500000, 0x00000000bc600000|  0%| F|  |TAMS 0x00000000bc500000, 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc600000, 0x00000000bc700000|  0%| F|  |TAMS 0x00000000bc600000, 0x00000000bc600000| Untracked 
| 455|0x00000000bc700000, 0x00000000bc700000, 0x00000000bc800000|  0%| F|  |TAMS 0x00000000bc700000, 0x00000000bc700000| Untracked 
| 456|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000, 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000, 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000, 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000, 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000, 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000, 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000, 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bd000000, 0x00000000bd000000|100%| S|CS|TAMS 0x00000000bcf00000, 0x00000000bcf00000| Complete 
| 464|0x00000000bd000000, 0x00000000bd100000, 0x00000000bd100000|100%| S|CS|TAMS 0x00000000bd000000, 0x00000000bd000000| Complete 
| 465|0x00000000bd100000, 0x00000000bd200000, 0x00000000bd200000|100%| S|CS|TAMS 0x00000000bd100000, 0x00000000bd100000| Complete 
| 466|0x00000000bd200000, 0x00000000bd300000, 0x00000000bd300000|100%| S|CS|TAMS 0x00000000bd200000, 0x00000000bd200000| Complete 
| 467|0x00000000bd300000, 0x00000000bd400000, 0x00000000bd400000|100%| S|CS|TAMS 0x00000000bd300000, 0x00000000bd300000| Complete 
| 468|0x00000000bd400000, 0x00000000bd500000, 0x00000000bd500000|100%| S|CS|TAMS 0x00000000bd400000, 0x00000000bd400000| Complete 
| 469|0x00000000bd500000, 0x00000000bd600000, 0x00000000bd600000|100%| S|CS|TAMS 0x00000000bd500000, 0x00000000bd500000| Complete 
| 470|0x00000000bd600000, 0x00000000bd700000, 0x00000000bd700000|100%| S|CS|TAMS 0x00000000bd600000, 0x00000000bd600000| Complete 
| 471|0x00000000bd700000, 0x00000000bd800000, 0x00000000bd800000|100%| S|CS|TAMS 0x00000000bd700000, 0x00000000bd700000| Complete 
| 472|0x00000000bd800000, 0x00000000bd900000, 0x00000000bd900000|100%| S|CS|TAMS 0x00000000bd800000, 0x00000000bd800000| Complete 
| 473|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000, 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000, 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000, 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000, 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000, 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000, 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000, 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000, 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000, 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000, 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000, 0x00000000be300000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000, 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000, 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000, 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000, 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000, 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000, 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000, 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000, 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000, 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000, 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000, 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000, 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000, 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000, 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000, 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000, 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000, 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000, 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000, 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf700000, 0x00000000bf800000|  0%| F|  |TAMS 0x00000000bf700000, 0x00000000bf700000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000, 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bf900000, 0x00000000bfa00000|  0%| F|  |TAMS 0x00000000bf900000, 0x00000000bf900000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfa00000, 0x00000000bfb00000|  0%| F|  |TAMS 0x00000000bfa00000, 0x00000000bfa00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfb00000, 0x00000000bfc00000|  0%| F|  |TAMS 0x00000000bfb00000, 0x00000000bfb00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfc00000, 0x00000000bfd00000|  0%| F|  |TAMS 0x00000000bfc00000, 0x00000000bfc00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfd00000, 0x00000000bfe00000|  0%| F|  |TAMS 0x00000000bfd00000, 0x00000000bfd00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bfe00000, 0x00000000bff00000|  0%| F|  |TAMS 0x00000000bfe00000, 0x00000000bfe00000| Untracked 
| 511|0x00000000bff00000, 0x00000000bff00000, 0x00000000c0000000|  0%| F|  |TAMS 0x00000000bff00000, 0x00000000bff00000| Untracked 
| 512|0x00000000c0000000, 0x00000000c0000000, 0x00000000c0100000|  0%| F|  |TAMS 0x00000000c0000000, 0x00000000c0000000| Untracked 
| 513|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000, 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0200000, 0x00000000c0300000|  0%| F|  |TAMS 0x00000000c0200000, 0x00000000c0200000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0300000, 0x00000000c0400000|  0%| F|  |TAMS 0x00000000c0300000, 0x00000000c0300000| Untracked 
| 516|0x00000000c0400000, 0x00000000c0400000, 0x00000000c0500000|  0%| F|  |TAMS 0x00000000c0400000, 0x00000000c0400000| Untracked 
| 517|0x00000000c0500000, 0x00000000c0500000, 0x00000000c0600000|  0%| F|  |TAMS 0x00000000c0500000, 0x00000000c0500000| Untracked 
| 518|0x00000000c0600000, 0x00000000c0600000, 0x00000000c0700000|  0%| F|  |TAMS 0x00000000c0600000, 0x00000000c0600000| Untracked 
| 519|0x00000000c0700000, 0x00000000c0700000, 0x00000000c0800000|  0%| F|  |TAMS 0x00000000c0700000, 0x00000000c0700000| Untracked 
| 520|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000, 0x00000000c0800000| Untracked 
| 521|0x00000000c0900000, 0x00000000c0900000, 0x00000000c0a00000|  0%| F|  |TAMS 0x00000000c0900000, 0x00000000c0900000| Untracked 
| 522|0x00000000c0a00000, 0x00000000c0a00000, 0x00000000c0b00000|  0%| F|  |TAMS 0x00000000c0a00000, 0x00000000c0a00000| Untracked 
| 523|0x00000000c0b00000, 0x00000000c0b00000, 0x00000000c0c00000|  0%| F|  |TAMS 0x00000000c0b00000, 0x00000000c0b00000| Untracked 
| 524|0x00000000c0c00000, 0x00000000c0c00000, 0x00000000c0d00000|  0%| F|  |TAMS 0x00000000c0c00000, 0x00000000c0c00000| Untracked 
| 525|0x00000000c0d00000, 0x00000000c0d00000, 0x00000000c0e00000|  0%| F|  |TAMS 0x00000000c0d00000, 0x00000000c0d00000| Untracked 
| 526|0x00000000c0e00000, 0x00000000c0e00000, 0x00000000c0f00000|  0%| F|  |TAMS 0x00000000c0e00000, 0x00000000c0e00000| Untracked 
| 527|0x00000000c0f00000, 0x00000000c0f00000, 0x00000000c1000000|  0%| F|  |TAMS 0x00000000c0f00000, 0x00000000c0f00000| Untracked 
| 528|0x00000000c1000000, 0x00000000c1000000, 0x00000000c1100000|  0%| F|  |TAMS 0x00000000c1000000, 0x00000000c1000000| Untracked 
| 529|0x00000000c1100000, 0x00000000c1100000, 0x00000000c1200000|  0%| F|  |TAMS 0x00000000c1100000, 0x00000000c1100000| Untracked 
| 530|0x00000000c1200000, 0x00000000c1200000, 0x00000000c1300000|  0%| F|  |TAMS 0x00000000c1200000, 0x00000000c1200000| Untracked 
| 531|0x00000000c1300000, 0x00000000c1300000, 0x00000000c1400000|  0%| F|  |TAMS 0x00000000c1300000, 0x00000000c1300000| Untracked 
| 532|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000, 0x00000000c1400000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000, 0x00000000c1500000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000, 0x00000000c1600000| Untracked 

Card table byte_map: [0x00000280d5510000,0x00000280d5810000] _byte_map_base: 0x00000280d5010000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000280c27374e8, (CMBitMap*) 0x00000280c2737520
 Prev Bits: [0x00000280d5b10000, 0x00000280d7310000)
 Next Bits: [0x00000280d7310000, 0x00000280d8b10000)

Polling page: 0x00000280c1ee0000

Metaspace:

Usage:
  Non-class:     88.55 MB capacity,    87.10 MB ( 98%) used,     1.16 MB (  1%) free+waste,   292.06 KB ( <1%) overhead. 
      Class:     13.98 MB capacity,    13.11 MB ( 94%) used,   758.41 KB (  5%) free+waste,   129.44 KB ( <1%) overhead. 
       Both:    102.53 MB capacity,   100.22 MB ( 98%) used,     1.90 MB (  2%) free+waste,   421.50 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       90.00 MB reserved,      88.70 MB ( 99%) committed 
      Class space:        1.00 GB reserved,      14.00 MB (  1%) committed 
             Both:        1.09 GB reserved,     102.70 MB (  9%) committed 

Chunk freelists:
   Non-Class:  26.00 KB
       Class:  0 bytes
        Both:  26.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 171.17 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=6100Kb max_used=6100Kb free=113963Kb
 bounds [0x00000280cdcd0000, 0x00000280ce2d0000, 0x00000280d5210000]
CodeHeap 'profiled nmethods': size=120000Kb used=26648Kb max_used=26648Kb free=93352Kb
 bounds [0x00000280c67a0000, 0x00000280c81b0000, 0x00000280cdcd0000]
CodeHeap 'non-nmethods': size=5696Kb used=2407Kb max_used=2456Kb free=3288Kb
 bounds [0x00000280c6210000, 0x00000280c6490000, 0x00000280c67a0000]
 total_blobs=13534 nmethods=12609 adapters=837
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 25.901 Thread 0x00000280db98f000 12945       2       com.android.tools.r8.code.q0::<init> (7 bytes)
Event: 25.901 Thread 0x00000280db98f000 nmethod 12945 0x00000280c81a0210 code [0x00000280c81a03e0, 0x00000280c81a0618]
Event: 25.906 Thread 0x00000280db98f000 12946       3       com.android.tools.r8.graph.d1::a (49 bytes)
Event: 25.907 Thread 0x00000280db98f000 nmethod 12946 0x00000280c81a0810 code [0x00000280c81a0a40, 0x00000280c81a10e8]
Event: 25.926 Thread 0x00000280db98f000 12947       2       com.android.tools.r8.code.e2::<init> (7 bytes)
Event: 25.926 Thread 0x00000280db98f000 nmethod 12947 0x00000280c81a1390 code [0x00000280c81a1540, 0x00000280c81a1698]
Event: 25.929 Thread 0x00000280db98f000 12948       2       com.android.tools.r8.code.v2::<init> (7 bytes)
Event: 25.929 Thread 0x00000280db98f000 nmethod 12948 0x00000280c81a1790 code [0x00000280c81a1960, 0x00000280c81a1b98]
Event: 25.960 Thread 0x00000280db98f000 12949 %     3       com.android.tools.r8.dex.j::c @ 14 (73 bytes)
Event: 25.961 Thread 0x00000280db98f000 nmethod 12949% 0x00000280c81a1d90 code [0x00000280c81a1fe0, 0x00000280c81a2a18]
Event: 25.982 Thread 0x00000280db98f000 12950       2       com.android.tools.r8.code.a::<init> (7 bytes)
Event: 25.983 Thread 0x00000280db98f000 nmethod 12950 0x00000280c81a2e90 code [0x00000280c81a3040, 0x00000280c81a3198]
Event: 25.983 Thread 0x00000280db98f000 12951       2       com.android.tools.r8.code.O3::<init> (7 bytes)
Event: 25.983 Thread 0x00000280db98f000 nmethod 12951 0x00000280c81a3290 code [0x00000280c81a3460, 0x00000280c81a3698]
Event: 26.015 Thread 0x00000280db98f000 12952 %     3       com.android.tools.r8.graph.N2::b @ 72 (133 bytes)
Event: 26.017 Thread 0x00000280db98f000 nmethod 12952% 0x00000280c81a3890 code [0x00000280c81a3ba0, 0x00000280c81a5028]
Event: 26.039 Thread 0x00000280db98f000 12953       2       com.android.tools.r8.code.z1::<init> (7 bytes)
Event: 26.039 Thread 0x00000280db98f000 nmethod 12953 0x00000280c81a5810 code [0x00000280c81a59c0, 0x00000280c81a5b18]
Event: 26.056 Thread 0x00000280db98f000 12954       2       com.android.tools.r8.code.i::<init> (7 bytes)
Event: 26.056 Thread 0x00000280db98f000 nmethod 12954 0x00000280c81a5c10 code [0x00000280c81a5dc0, 0x00000280c81a5f18]

GC Heap History (20 events):
Event: 20.307 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 241664K, used 194560K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 100120K, capacity 102420K, committed 102732K, reserved 1138688K
  class space    used 12976K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.347 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 241664K, used 201728K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 3 survivors (3072K)
 Metaspace       used 100120K, capacity 102420K, committed 102732K, reserved 1138688K
  class space    used 12976K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.370 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 241664K, used 195519K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100120K, capacity 102420K, committed 102732K, reserved 1138688K
  class space    used 12976K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.382 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 241664K, used 196543K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 2 survivors (2048K)
 Metaspace       used 100120K, capacity 102420K, committed 102732K, reserved 1138688K
  class space    used 12976K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.397 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 290816K, used 197169K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100120K, capacity 102420K, committed 102732K, reserved 1138688K
  class space    used 12976K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.741 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 290816K, used 221745K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 2 survivors (2048K)
 Metaspace       used 100159K, capacity 102488K, committed 102732K, reserved 1138688K
  class space    used 12977K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.767 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 290816K, used 203910K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 100159K, capacity 102488K, committed 102732K, reserved 1138688K
  class space    used 12977K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.947 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 290816K, used 220294K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 4 survivors (4096K)
 Metaspace       used 100179K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 20.981 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 290816K, used 209905K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 100179K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 21.267 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 290816K, used 233457K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 4 survivors (4096K)
 Metaspace       used 100179K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 21.297 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 290816K, used 216775K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 100179K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 21.532 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 290816K, used 239303K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 4 survivors (4096K)
 Metaspace       used 100195K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 21.591 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 547840K, used 228211K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 100195K, capacity 102490K, committed 102732K, reserved 1138688K
  class space    used 12978K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 23.382 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 547840K, used 317299K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 91 young (93184K), 3 survivors (3072K)
 Metaspace       used 102567K, capacity 104920K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 23.483 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 547840K, used 260872K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 102567K, capacity 104920K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 23.902 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 547840K, used 346888K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 97 young (99328K), 12 survivors (12288K)
 Metaspace       used 102604K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 24.050 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 547840K, used 300544K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 102604K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 25.732 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 547840K, used 373248K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 87 young (89088K), 13 survivors (13312K)
 Metaspace       used 102619K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 25.840 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 547840K, used 336896K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 102619K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}
Event: 26.070 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 547840K, used 399360K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 73 young (74752K), 11 survivors (11264K)
 Metaspace       used 102622K, capacity 104990K, committed 105164K, reserved 1140736K
  class space    used 13428K, capacity 14316K, committed 14336K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 24.185 Thread 0x00000280e2a90000 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f30fe0a0
Event: 24.185 Thread 0x00000280e2a90000 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f30fdcd0 mode 0
Event: 24.191 Thread 0x00000280e2a90000 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f30fe0a0
Event: 24.191 Thread 0x00000280e2a90000 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f30fdcd0 mode 0
Event: 24.197 Thread 0x00000280e2a91800 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f32fe470
Event: 24.197 Thread 0x00000280e2a91800 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f32fe0a0 mode 0
Event: 24.201 Thread 0x00000280e2a91800 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f32fe470
Event: 24.201 Thread 0x00000280e2a91800 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f32fe0a0 mode 0
Event: 24.208 Thread 0x00000280e2a91800 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f32fe470
Event: 24.208 Thread 0x00000280e2a91800 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f32fe0a0 mode 0
Event: 24.213 Thread 0x00000280e2a91800 DEOPT PACKING pc=0x00000280c80a1bef sp=0x000000a0f32fe450
Event: 24.213 Thread 0x00000280e2a91800 DEOPT UNPACKING pc=0x00000280c625a95e sp=0x000000a0f32fe080 mode 0
Event: 24.952 Thread 0x00000280e2a90000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x00000280ce2bdd00 relative=0x0000000000009be0
Event: 24.952 Thread 0x00000280e2a90000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000280ce2bdd00 method=com.android.tools.r8.code.y1.a(Ljava/nio/ShortBuffer;IILcom/android/tools/r8/internal/mI;)[Lcom/android/tools/r8/code/w1; @ 541 c2
Event: 24.952 Thread 0x00000280e2a90000 DEOPT PACKING pc=0x00000280ce2bdd00 sp=0x000000a0f30fe7f0
Event: 24.952 Thread 0x00000280e2a90000 DEOPT UNPACKING pc=0x00000280c625a1af sp=0x000000a0f30fe7d8 mode 2
Event: 24.953 Thread 0x00000280e2a90000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x00000280ce293e08 relative=0x00000000000076e8
Event: 24.953 Thread 0x00000280e2a90000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000280ce293e08 method=com.android.tools.r8.code.y1.a(Ljava/nio/ShortBuffer;IILcom/android/tools/r8/internal/mI;)[Lcom/android/tools/r8/code/w1; @ 541 c2
Event: 24.953 Thread 0x00000280e2a90000 DEOPT PACKING pc=0x00000280ce293e08 sp=0x000000a0f30fe800
Event: 24.953 Thread 0x00000280e2a90000 DEOPT UNPACKING pc=0x00000280c625a1af sp=0x000000a0f30fe7f8 mode 2

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 15.137 Thread 0x00000280dcca2000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a9392630}: groovy/util/slurpersupport/AttributeCustomizer> (0x00000000a9392630) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 15.406 Thread 0x00000280e2a8e800 Implicit null exception at 0x00000280ce010294 to 0x00000280ce010858
Event: 15.642 Thread 0x00000280e2a8e800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a81399f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000a81399f0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 15.645 Thread 0x00000280e2a8e800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a814e300}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000a814e300) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 15.709 Thread 0x00000280e2a8e800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a805ba58}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a805ba58) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 16.270 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa72d6a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000aa72d6a8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 16.657 Thread 0x00000280df9ae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a8f80fd8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000a8f80fd8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 17.330 Thread 0x00000280df9b9800 Implicit null exception at 0x00000280ce1641db to 0x00000280ce164270
Event: 17.486 Thread 0x00000280df9b7800 Implicit null exception at 0x00000280ce181d71 to 0x00000280ce182454
Event: 18.854 Thread 0x00000280e2a93000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ac664088}> (0x00000000ac664088) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 19.106 Thread 0x00000280e2a93000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aad392d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0
Event: 22.838 Thread 0x00000280e2a90000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf631d68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int)'> (0x00000000bf631d68) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 22.851 Thread 0x00000280e2a97800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000c05f8250}: Found class java.lang.Object, but interface was expected> (0x00000000c05f8250) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 22.873 Thread 0x00000280e2a97800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000bf6651c8}: Found class java.lang.Object, but interface was expected> (0x00000000bf6651c8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 22.873 Thread 0x00000280e2a97800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000bf66af48}: Found class java.lang.Object, but interface was expected> (0x00000000bf66af48) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 22.980 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf1cac00}: <clinit>> (0x00000000bf1cac00) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 22.980 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf1ccca8}: <clinit>> (0x00000000bf1ccca8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 22.980 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf1d0590}: <clinit>> (0x00000000bf1d0590) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 22.981 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf1f5538}: <clinit>> (0x00000000bf1f5538) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 22.982 Thread 0x00000280dcca2000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000bf1f7210}: <clinit>> (0x00000000bf1f7210) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]

Events (20 events):
Event: 23.002 loading class com/android/tools/r8/internal/XG done
Event: 23.002 loading class com/android/tools/r8/internal/cH
Event: 23.002 loading class com/android/tools/r8/internal/cH done
Event: 23.006 loading class com/android/tools/r8/internal/aB
Event: 23.006 loading class com/android/tools/r8/internal/aB done
Event: 23.108 loading class com/android/tools/r8/internal/vg
Event: 23.108 loading class com/android/tools/r8/internal/vg done
Event: 23.115 loading class com/android/tools/r8/internal/Dj
Event: 23.115 loading class com/android/tools/r8/internal/Dj done
Event: 23.380 Executing VM operation: G1CollectForAllocation
Event: 23.483 Executing VM operation: G1CollectForAllocation done
Event: 23.900 Executing VM operation: G1CollectForAllocation
Event: 24.050 Executing VM operation: G1CollectForAllocation done
Event: 24.711 Executing VM operation: CGC_Operation
Event: 24.789 Executing VM operation: CGC_Operation done
Event: 25.211 Executing VM operation: CGC_Operation
Event: 25.263 Executing VM operation: CGC_Operation done
Event: 25.732 Executing VM operation: G1CollectForAllocation
Event: 25.840 Executing VM operation: G1CollectForAllocation done
Event: 26.069 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff75b6b0000 - 0x00007ff75b6ba000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa744b0000 - 0x00007ffa744c7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa49770000 - 0x00007ffa4980d000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa42180000 - 0x00007ffa422c4000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:22 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (215M free)
TotalPageFile size 32602M (AvailPageFile size 95M)
current process WorkingSet (physical memory assigned to process): 727M, peak: 730M
current process commit charge ("private bytes"): 861M, peak: 1181M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
