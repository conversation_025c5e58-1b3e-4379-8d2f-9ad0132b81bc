<!--
  ~ SIBEL INC ("SIBEL HEALTH") CONFIDENTIAL
  ~ Copyright 2018-2021 [Sibel Inc.], All Rights Reserved.
  ~ NOTICE: All information contained herein is, and remains the property of SIBEL
  ~ INC. The intellectual and technical concepts contained herein are proprietary
  ~ to SIBEL INC and may be covered by U.S. and Foreign Patents, patents in
  ~ process, and are protected by trade secret or copyright law. Dissemination of
  ~ this information or reproduction of this material is strictly forbidden unless
  ~ prior written permission is obtained from SIBEL INC. Access to the source code
  ~ contained herein is hereby forbidden to anyone except current SIBEL INC
  ~ employees, managers or contractors who have executed Confidentiality and
  ~ Non-disclosure agreements explicitly covering such access.
  ~ The copyright notice above does not evidence any actual or intended
  ~ publication or disclosure of this source code, which includes information that
  ~ is confidential and/or proprietary, and is a trade secret, of SIBEL INC.
  ~ ANY REPRODUCTION, MODIFICATION, DISTRIBUTION, PUBLIC PERFORMANCE, OR PUBLIC
  ~ DISPLAY OF OR THROUGH USE OF THIS SOURCE CODE WITHOUT THE EXPRESS WRITTEN
  ~ CONSENT OF COMPANY IS STRICTLY PROHIBITED, AND IN VIOLATION OF APPLICABLE
  ~ LAWS AND INTERNATIONAL TREATIES. THE RECEIPT OR POSSESSION OF THIS SOURCE
  ~ CODE AND/OR RELATED INFORMATION DOES NOT CONVEY OR IMPLY ANY RIGHTS TO
  ~ REPRODUCE, DISCLOSE OR DISTRIBUTE ITS CONTENTS, OR TO MANUFACTURE, USE, OR
  ~ SELL ANYTHING THAT IT MAY DESCRIBE, IN WHOLE OR IN PART.
  -->

<resources>
    <!-- Base application theme. -->
    <style name="NoMultiTouch" parent="@style/Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.DarkActionBar">
    </style>
    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="spinnerTheme">
        <item name="android:textColor">@color/white</item>
        <item name="background">@drawable/spinner_bg</item>
    </style>
    
    <style name="customtheme" parent="@android:style/Theme.Light">
        <item name="android:spinnerItemStyle">@style/spinnerItem</item>
    </style>

    <style name="spinnerItem">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/rounded_edit_text</item>
    </style>

    <style name="whiteCheckedTextView">
        <item name="colorControlNormal">#f0f0f0</item>
    </style>

    <style name="SearchViewStyle" parent="Widget.AppCompat.SearchView">
        <!-- Gets rid of the search icon -->
        <item name="searchIcon">@drawable/ic_search</item>
        <!-- Gets rid of the "underline" in the text -->
        <item name="queryBackground">@color/white</item>
        <!-- Gets rid of the search icon when the SearchView is expanded -->
        <item name="searchHintIcon">@null</item>
        <item name="marginRight">50dp</item>
        <!-- The hint text that appears when the user has not typed anything -->
        <item name="queryHint">@string/sensor_search_hint</item>
        <item name="closeIcon">@drawable/ic_close_24</item>
        <item name="android:actionBarWidgetTheme">@color/white</item>

        <item name="android:textColorHint">@color/white</item>
    </style>

    <style name="MyActionBarWidgetTheme" parent="@android:style/Theme.Holo">
        <item name="android:textColorHint">@color/white</item>
    </style>

    <style name="SCBSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
        <!-- active thumb & track color (30% transparency) -->
        <item name="colorControlActivated">@color/lightBlue</item>

        <!-- inactive thumb color -->
        <item name="colorSwitchThumbNormal">@color/lightBlue
        </item>

        <!-- inactive track color (30% transparency) -->
        <item name="android:colorForeground">@color/lightBlue
        </item>
    </style>

</resources>
