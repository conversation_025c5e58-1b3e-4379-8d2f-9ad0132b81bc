<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="2.5dp"
    android:paddingBottom="2.5dp"
    tools:ignore="VisualLintBounds">

    <TextView
        android:id="@+id/PatientAlarmId"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="PatientAlarmId"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:id="@+id/PatientId"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Patient Id"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/ParamId"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Param Id"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



    <TextView
        android:id="@+id/AlarmCategory"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmCategory"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmName"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Alarm Name"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/Value"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Value"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/LowValue"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="LowValue"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:id="@+id/ExtremeLowValue"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="ExtremeLowValue"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/HighValue"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="HighValue"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:id="@+id/ExtremeHighValue"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="ExtremeHighValue"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/TimeStamp"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="TimeStamp"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmStatus"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmStatus"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmSound"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmSound"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



</LinearLayout>

