<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/layout1"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/navLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        android:baselineAligned="false"
        tools:ignore="UnknownIdInLayout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:baselineAligned="false">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:layout_weight=".2">

                    <!--<TextView
                        style="@style/TextAppearance.AppCompat.Headline"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/legalInformation" />-->

                    <TextView
                        android:id="@+id/swVersion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        style="@style/TextAppearance.AppCompat.Medium"/>

                    <TextView
                        android:id="@+id/androidId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        style="@style/TextAppearance.AppCompat.Medium"/>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginTop="22dp"
                            android:gravity="start"
                            android:textColor="@color/ap_gray"
                            android:text="@string/periodicDbClearanceTime" />

                        <Spinner
                            android:id="@+id/delete_timer_spinner"
                            android:layout_width="150dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="10dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="90dp"
                            android:background="@drawable/spinner_bg"
                            android:dropDownVerticalOffset="41dp"
                            android:gravity="center"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:popupBackground="@color/gray"
                            android:spinnerMode="dropdown"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />


                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1.3"
            android:background="@color/background"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background"
                android:orientation="vertical"
                android:layout_weight="1"
                tools:ignore="NestedWeights">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/wifi"
                    android:textColor="@color/white"
                    android:layout_margin="20dp"
                    android:textStyle="bold"
                    style="@style/TextAppearance.AppCompat.Medium"/>

                <TextView
                    android:id="@+id/wifi_ssid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/wifi_name"
                    android:textColor="@color/white"
                    android:layout_gravity="center_horizontal"
                    android:textStyle="bold"
                    style="@style/TextAppearance.AppCompat.Medium"/>


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/config_wifi"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:paddingStart="40dp"
                    android:paddingEnd="40dp"
                    android:text="@string/configwifi"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue"
                    android:layout_gravity="center"/>

                <Spinner
                    android:visibility="gone"
                    android:id="@+id/wifi_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:spinnerStyle="@style/SpinnerStyle"
                    android:layout_margin="10dp"
                    android:paddingStart="20dp"
                    android:paddingEnd="10dp"
                    android:background="@drawable/spinner_bg"
                    android:spinnerMode="dropdown"
                    android:popupBackground="@color/gray"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:dropDownVerticalOffset="41dp"
                    android:prompt="@string/your_prompt_text" />

                <EditText
                    android:id="@+id/wifi_user_name"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:textColor="@color/white"
                    android:hint="@string/username"
                    android:textColorHint="@color/ap_gray"
                    android:visibility="gone"
                    android:paddingStart="20dp"
                    android:paddingEnd="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/gray"
                    android:autofillHints=""
                    android:inputType="text" />

                <EditText
                    android:id="@+id/wifi_password"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:hint="@string/password"
                    android:textColorHint="@color/ap_gray"
                    android:visibility="gone"
                    android:paddingStart="20dp"
                    android:paddingEnd="10dp"
                    android:backgroundTint="@color/gray"
                    android:autofillHints=""
                    android:inputType="text" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="end|center"
                android:orientation="vertical"
                android:layout_weight="4">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/wifi_save"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:paddingStart="40dp"
                    android:paddingEnd="40dp"
                    android:text="@string/save"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    android:textAllCaps="false"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue"
                    android:layout_gravity="end"/>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>