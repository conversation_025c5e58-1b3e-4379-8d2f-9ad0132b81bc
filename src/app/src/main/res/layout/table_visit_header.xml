<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="5dp"
    android:paddingBottom="10dp"
    android:orientation="horizontal"
    >

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:text="VisiID"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientID2"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="VisitDate"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AdmitDateTime"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


</LinearLayout>
