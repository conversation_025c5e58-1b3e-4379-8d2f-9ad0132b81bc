<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="@id/navLayout"
        tools:ignore="MissingConstraints,NotSibling"
        tools:layout_editor_absoluteX="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingStart="15dp"
                android:paddingEnd="0dp"
                tools:ignore="NestedWeights">

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_gravity="start"
                    android:layout_marginTop="10dp"
                    android:gravity="start|center"
                    android:text="@string/bpParameter"
                    android:textColor="@color/ap_gray" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/highBpSys"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/highBpSys"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:layout_gravity="start"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/gray"
                        android:enabled="false"
                        android:inputType="number"
                        android:nextFocusDown="@id/lowBpSys"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highSysInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/upIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highSysDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/downIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/highBpDia"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/highBpDia"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:layout_gravity="start"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/gray"
                        android:enabled="false"
                        android:inputType="number"
                        android:nextFocusDown="@id/lowBpDia"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highDiaInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/upIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highDiaDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/downIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingEnd="0dp"
                tools:ignore="NestedWeights">

                <Switch
                    android:id="@+id/bpAlarmSwitch"
                    android:layout_width="50dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center|start"
                    android:layout_marginTop="10dp"
                    android:checked="true"
                    android:gravity="start|center"
                    android:scaleX="1.2"
                    android:scaleY="1.2"
                    tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/lowBpSys"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/lowBpSys"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:layout_gravity="start"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/gray"
                        android:enabled="false"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowSysInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/upIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowSysDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/downIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/lowBpDia"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/lowBpDia"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:layout_gravity="start"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/gray"
                        android:enabled="false"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowDiaInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/upIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowDiaDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:gravity="center"
                        android:text="@string/downIcon"
                        android:textColor="@color/white"
                        android:theme="@style/fontAwesomeText" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1.5"
            android:orientation="vertical">

            <TextView
                android:id="@+id/volumeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="60dp"
                android:text="@string/bpVolume"
                android:textColor="@color/ap_gray"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.097"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.31" />

            <com.google.android.material.slider.Slider
                android:id="@+id/bpVolume"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="90dp"
                app:thumbColor="@color/white"
                app:trackColorActive="@color/lightBlue"
                app:trackColorInactive="@color/components_gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="10dp"
                android:layout_marginTop="22dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="start|center"
                    android:layout_weight="2"
                    android:text="@string/autoBp"
                    android:textColor="@color/ap_gray" />

                <Switch
                    android:id="@+id/autoBpSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2.2"
                    android:layout_gravity="start"
                    android:checked="true"
                    android:gravity="start"
                    android:scaleX="1.2"
                    android:scaleY="1.2"
                    tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />

                <androidx.legacy.widget.Space
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/spinnerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="start|center"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="22dp"
                    android:text="@string/measureBp"
                    android:textColor="@color/ap_gray" />

                <Spinner
                    android:id="@+id/timer_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="90dp"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:gravity="center"
                    android:background="@drawable/spinner_bg"
                    android:dropDownVerticalOffset="41dp"
                    android:popupBackground="@color/gray"
                    android:spinnerMode="dropdown"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/bpStartButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:padding="10dp"
                android:layout_marginTop="20dp"
                android:layout_marginStart="10dp"
                android:textAllCaps="false"
                android:text="@string/startBp"
                android:textColor="@color/white"
                android:backgroundTint="@color/lightBlue"
                android:background="@drawable/rounded_edit_text"/>

            <TextView
                android:id="@+id/bp_measuring_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Measuring..."
                android:textColor="@color/lightBlue"
                android:textSize="12sp"
                android:textStyle="italic"
                android:visibility="gone"
                android:layout_marginTop="8dp"
                android:layout_gravity="center_horizontal" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>