<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:orientation="horizontal"
    >

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:text="SensorDefID"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="SensorCategory"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="SensorName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="SensorType"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


</LinearLayout>
