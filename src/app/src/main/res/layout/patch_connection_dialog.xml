<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ap_transparent"
    android:gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:layout_weight="4.3"
            style="@style/TextAppearance.AppCompat.Medium"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/alertTitle"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginStart="20dp"
                android:layout_weight="1.5"
                android:text="@string/sensor_connected"
                android:textStyle="bold"
                android:textColor="@color/white"
                tools:ignore="NestedWeights" />

            <Button
                android:id="@+id/closeBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:layout_gravity="top"
                android:gravity="end"
                android:background="@color/ap_transparent"
                android:theme="@style/fontAwesomeText"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_weight="0"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="130dp"
            android:layout_weight="4"
            android:orientation="horizontal"
            android:paddingStart="70dp"
            android:paddingTop="5dp"
            android:paddingEnd="70dp"
            android:paddingBottom="5dp">

            <ImageView
                android:id="@+id/chestIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_chest"
                tools:ignore="NestedWeights"
                android:visibility="gone"/>

            <ImageView
                android:id="@+id/limbIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_limb"
                tools:ignore="NestedWeights"
                android:visibility="gone"/>


            <ImageView
                android:id="@+id/bpIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_bp"
                tools:ignore="NestedWeights" />

        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="20dp"
        android:layout_weight="3.7"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:orientation="horizontal"
            tools:ignore="NestedWeights">

            <Space
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="2.7"/>

            <TextView
                android:id="@+id/connectionText"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:textColor="@color/white"
                tools:ignore="NestedWeights" />

            <Space
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="2.7"/>

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnOk"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="170dp"
            android:layout_marginEnd="170dp"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:text="@string/ok"
            android:textColor="@color/white"
            android:layout_weight="0.3"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue"
            style="@style/TextAppearance.AppCompat.Title"/>

    </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>