<?xml version="1.0" encoding="utf-8"?>

    <androidx.appcompat.widget.AppCompatCheckedTextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@android:id/text1"
        android:textColor="@color/white"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/listPreferredItemHeightSmall"
        style="@style/TextAppearance.AppCompat.Medium"
        android:checkMark="@drawable/custom_check_box"
        android:gravity="center_vertical"
        android:background="@drawable/checked_text_view_background"
        android:theme="@style/whiteCheckedTextView"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"/>
