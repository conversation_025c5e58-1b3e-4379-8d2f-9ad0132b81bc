<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="1dp"
    android:divider="@color/ap_white"
    android:orientation="vertical">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/checked_text_view_background"

    >
    <!-- Top row containing Icon and Sensor Name -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/sensorIcon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="30dp"
            android:paddingEnd="15dp"
            android:src="@drawable/ic_sensor_bp"
            app:tint="@color/white" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sensorName"
            android:textColor="@color/white"
            android:layout_width="match_parent"
            android:layout_height="?android:attr/listPreferredItemHeightSmall"
            android:layout_weight="1"
            android:text="Bluetooth Device"
            style="@style/TextAppearance.AppCompat.Medium"
            android:checkMark="@drawable/custom_check_box"
            android:gravity="center_vertical"
            android:theme="@style/whiteCheckedTextView"
            android:paddingStart="?android:attr/listPreferredItemPaddingStart"
            android:paddingEnd="?android:attr/listPreferredItemPaddingEnd" />
        <TextView
            android:id="@+id/SensorSignal"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:paddingEnd="0dp"
            android:gravity="center"
            android:visibility="visible"
            android:text="" />
        <ImageView
            android:id="@+id/connectingProgress"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:visibility="invisible"
            android:src="@drawable/loading"/>
        <Button
            android:id="@+id/deviceDisconnect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Disconnect"
            android:backgroundTint="@color/batteryVeryLow"
            android:visibility="gone" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_weight="1"
        android:layout_gravity="center">


    </LinearLayout>
    <!-- Middle row containing Sensor Address and Sensor Signal -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="@drawable/textview_border"
        android:visibility="gone"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_weight="1"
            android:gravity="start"
            android:layout_gravity="start"
            >
        <TextView
            android:id="@+id/SensorAddressHeading"
            android:layout_width="60dp"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:gravity="center"
            android:text="Mac Address:" />



        <TextView
            android:id="@+id/SensorAddress"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:gravity="center"
            android:text="Address" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_weight="1"
            >
        <TextView
            android:id="@+id/SensorSignalHeading"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:gravity="center"
            android:text="Signal Strength: " />
        <TextView
            android:id="@+id/SensorSignal1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:paddingEnd="20dp"
            android:gravity="center"
            android:text="Signal Strength" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>
    <!-- Bottom row containing Disconnect button -->

</LinearLayout>
