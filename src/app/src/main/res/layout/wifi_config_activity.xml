<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:orientation="vertical"
        android:elevation="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="500dp"
            android:layout_height="100dp"
            android:scaleX=".7"
            android:scaleY=".7"
            android:src="@drawable/app_logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chooseAWifi"
            android:textColor="@color/white"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:visibility="gone"
            />

        <Spinner
            android:id="@+id/wifiSpinner"
            android:layout_width="300dp"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/spinner_bg"
            android:popupBackground="@drawable/spinner_popup_background"
            android:dropDownSelector="@drawable/spinner_popup_background"
            android:dropDownVerticalOffset="45dp"
            android:popupElevation="@dimen/fab_margin"
            android:spinnerMode="dropdown"
            android:gravity="start|center"
            android:visibility="gone"
            android:nextFocusForward="@id/weight"/>

        <EditText
            android:id="@+id/password"
            android:layout_width="300dp"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:inputType="textPassword"
            android:visibility="gone"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@color/white"
            android:hint="@string/password"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            tools:ignore="Autofill,LabelFor" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/nextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:paddingStart="50dp"
            android:paddingEnd="50dp"
            android:textColor="@color/white"
            android:text="@string/next"
            android:textAllCaps="false"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/wifiLoading"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:src="@drawable/hr_signal_loading" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:text="Choose Your Device"
            android:textAppearance="@style/TextAppearance.AppCompat.Small"
            />
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">
            <androidx.cardview.widget.CardView
                android:id="@+id/bmpmsCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardUseCompatPadding="true"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/bmpmsTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="16dp"
                    android:text="@string/bodymount_pms"
                    android:textSize="18sp"
                    android:textColor="@color/white"
                    />
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/EcgChestPatchCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardUseCompatPadding="true"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/EcgChestPatch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="16dp"
                    android:text="@string/iecg_chest_patch"
                    android:textSize="18sp"
                    android:textColor="@color/white"/>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/animationView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"

        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
       <!-- android:background="@color/SibelTransparency2"-->
        <TextView
            android:id="@+id/animationText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:paddingStart="15dp"
            android:paddingEnd="0dp"
            android:visibility="gone"
            android:textColor="@color/white"/>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/skipBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:padding="10dp"
        android:text="@string/next"
        android:textColor="@color/white"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:background="@drawable/rounded_edit_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>