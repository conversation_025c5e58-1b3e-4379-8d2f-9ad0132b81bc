<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:scaleX=".5"
            android:scaleY="0.5"
            android:src="@drawable/app_logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sysAccountId"
            android:textColor="@color/white"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/text1"
                style="@style/otpEditTextTheme"
                android:nextFocusDown="@id/text2"
                tools:ignore="Autofill,LabelFor" />

            <EditText
                android:id="@+id/text2"
                style="@style/otpEditTextTheme"
                android:nextFocusDown="@id/text3"
                tools:ignore="Autofill,LabelFor" />

            <EditText
                android:id="@+id/text3"
                style="@style/otpEditTextTheme"
                android:nextFocusDown="@id/text4"
                tools:ignore="Autofill,LabelFor" />

            <EditText
                android:id="@+id/text4"
                android:nextFocusDown="@id/nextButton"
                style="@style/otpEditTextTheme"
                tools:ignore="Autofill,LabelFor" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/nextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:paddingStart="50dp"
            android:paddingEnd="50dp"
            android:textColor="@color/white"
            android:text="@string/next"
            android:textAllCaps="false"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>