<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:orientation="horizontal"
    >

    <TextView
        android:id="@+id/EventLogId"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:text="EventLogId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:background="@drawable/textview_border"
        android:gravity="center" />

    <TextView
        android:id="@+id/EventId"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="EventId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:background="@drawable/textview_border"
        android:gravity="center" />

    <TextView
        android:id="@+id/PatientId"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:background="@drawable/textview_border"
        android:gravity="center" />

    <TextView
        android:id="@+id/PatientID1"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientID1"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:background="@drawable/textview_border"
        android:gravity="center" />

    <TextView
        android:id="@+id/TimeStamp"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="TimeStamp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:background="@drawable/textview_border"
        android:gravity="center" />


</LinearLayout>
