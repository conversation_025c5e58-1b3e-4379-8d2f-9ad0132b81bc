<?xml version="1.0" encoding="utf-8"?>

<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        tools:ignore="VisualLintBounds">
        <TextView
            android:id="@+id/alaram_id"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="AlarmId"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/param_id"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamId"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:id="@+id/default_alarm_category"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultAlarmCategory"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/default_alarm_hv"
            android:layout_width="170dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultAlarmHighValue"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/default_alarm_lv"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="DefaultAlarmLowValue"
            android:textColor="@color/white"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/default_alarm_ehv"
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultAlarmExtremeHighValue"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/default_alarm_elv"
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultAlarmExtremeLowValue"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/default_alarm_status"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultAlarmStatus"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/default_sound"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="DefaultSound"
            android:textColor="@color/white"
            android:textStyle="bold" />


    </LinearLayout>

</HorizontalScrollView>
