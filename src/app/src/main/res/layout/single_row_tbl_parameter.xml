<?xml version="1.0" encoding="utf-8"?>

<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        tools:ignore="VisualLintBounds">
        <TextView
            android:id="@+id/param_id"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamId"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/param_name"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamName"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:id="@+id/param_category"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamCategory"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/param_type"
            android:layout_width="170dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamType"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/linked_sensor_def"
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="LinkedSensorDef"
            android:textColor="@color/white"
            android:textStyle="bold" />


    </LinearLayout>

</HorizontalScrollView>
