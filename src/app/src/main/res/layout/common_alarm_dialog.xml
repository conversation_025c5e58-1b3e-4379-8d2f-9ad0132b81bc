<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ap_transparent"
    android:gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:layout_weight="4.3"
            style="@style/TextAppearance.AppCompat.Medium"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/alarmTitle"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginStart="20dp"
                android:layout_weight="1.5"
                android:text="@string/allAlarmPaused"
                android:textStyle="bold"
                android:textColor="@color/white"
                tools:ignore="NestedWeights" />
        </LinearLayout>

        <ImageView
            android:id="@+id/alarmIcon"
            android:layout_width="wrap_content"
            android:layout_height="150dp"
            android:layout_weight="4"
            android:orientation="horizontal"
            android:layout_marginTop="15dp"
            android:src="@drawable/ic_arrows_turn"
            android:paddingBottom="5dp"
            app:tint="@color/orange" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="20dp"
            android:layout_weight="3.7"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                tools:ignore="NestedWeights">

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2.7"/>

                <TextView
                    android:id="@+id/alarmText"
                    style="@style/TextAppearance.AppCompat.Title"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/white"
                    tools:ignore="NestedWeights" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2.7"/>

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/acknowledgeBtn"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:layout_marginTop="10dp"
                android:layout_marginStart="155dp"
                android:layout_marginEnd="155dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:textAllCaps="false"
                android:text="@string/resume"
                android:textColor="@color/white"
                android:layout_weight="0.3"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                style="@style/TextAppearance.AppCompat.Title"/>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>