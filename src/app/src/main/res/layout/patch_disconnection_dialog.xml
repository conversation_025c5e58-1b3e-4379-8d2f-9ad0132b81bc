<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ap_transparent"
    android:gravity="center">



<LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:layout_weight="4.3"
            style="@style/TextAppearance.AppCompat.Medium"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/alertTitle"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="10dp"
                android:layout_weight="1.5"
                android:text="@string/sensor_disconnected"
                android:textColor="@color/white"
                android:textStyle="bold"
                tools:ignore="NestedWeights" />

            <Button
                android:id="@+id/closeBtn"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="0"
                android:background="@color/ap_transparent"
                android:gravity="end"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                android:theme="@style/fontAwesomeText" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="130dp"
            android:layout_weight="4"
            android:orientation="horizontal"
            android:paddingStart="70dp"
            android:paddingTop="5dp"
            android:paddingEnd="70dp"
            android:paddingBottom="5dp">

            <ImageView
                android:id="@+id/chestIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_chest"
                android:visibility="gone"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/limbIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_limb"
                android:visibility="gone"
                tools:ignore="NestedWeights" />


            <ImageView
                android:id="@+id/bpIcon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:src="@drawable/ic_sensor_bp"
                tools:ignore="NestedWeights" />

        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="20dp"
        android:layout_weight="3.7"
        android:gravity="center"
        android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.3"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                tools:ignore="NestedWeights">

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2.7"/>


                <TextView
                    android:id="@+id/connectionText"
                    style="@style/TextAppearance.AppCompat.Title"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/white"
                    tools:ignore="NestedWeights" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2.7"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="10dp"
                android:layout_weight="0.7"
                android:visibility="gone"
                android:orientation="horizontal"
                tools:ignore="NestedWeights">

                <TextView
                    style="@style/TextAppearance.AppCompat.Small"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Would you like to remove the sensor?"
                    android:textColor="@color/white"
                    tools:ignore="NestedWeights" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:visibility="gone"
                    android:gravity="center">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnWait"

                        style="@style/TextAppearance.AppCompat.Title"
                        android:layout_width="wrap_content"
                        android:layout_height="29dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"
                        android:text="Wait"
                        android:textColor="@color/white" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginTop="5dp"
                    android:gravity="center">
                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnOk"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginStart="170dp"
                        android:layout_marginEnd="150dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:text="@string/ok"
                        android:textColor="@color/white"
                        android:layout_weight="0.3"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        style="@style/TextAppearance.AppCompat.Title"/>/>
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>