<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background"
    android:background="@drawable/rounded_popup_view">

    <TextView
        android:id="@+id/textView6"
        style="@style/TextAppearance.AppCompat.Headline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:text="@string/newSensorDiscovered"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:orientation="horizontal"
        android:paddingStart="50dp"
        android:paddingEnd="50dp"
        app:layout_constraintBottom_toTopOf="@+id/confirmationText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView6"
        android:baselineAligned="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="20dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/icon1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingTop="30dp"
                android:paddingBottom="0dp"
                android:scaleType="fitCenter"
                app:srcCompat="@drawable/ic_sensor_chest"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/sensor1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_weight="3"
                android:gravity="top|center"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Title" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="40dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                style="@style/TextAppearance.AppCompat.Display2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="50dp"
                android:paddingEnd="50dp"
                android:paddingTop="30dp"
                android:paddingBottom="0dp"
                android:gravity="center"
                android:text="@string/rightArrow"
                android:textColor="@color/white"
                android:theme="@style/fontAwesomeText"
                tools:ignore="NestedWeights" />

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_weight="3" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="20dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/icon2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingTop="30dp"
                android:paddingBottom="0dp"
                android:scaleType="fitCenter"
                app:srcCompat="@drawable/ic_sensor_chest"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/sensor2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_weight="3"
                android:gravity="top|center"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Title" />

        </LinearLayout>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/cancelBtn"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:layout_weight="2"
        android:layout_gravity="end"
        android:text="@string/no"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/TextAppearance.AppCompat.Title" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/okBtn"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:layout_margin="20dp"
        android:layout_gravity="end"
        android:text="@string/yes"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/lightBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        style="@style/TextAppearance.AppCompat.Title" />

    <TextView
        android:id="@+id/confirmationText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:paddingStart="50dp"
        android:paddingEnd="50dp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/okBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        style="@style/TextAppearance.AppCompat.Title" />

</androidx.constraintlayout.widget.ConstraintLayout>