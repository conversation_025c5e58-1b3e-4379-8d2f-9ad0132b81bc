<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="5dp"
    android:paddingBottom="10dp"
    android:orientation="horizontal"
    >

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="FristName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="MiddleName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="LastName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientID1"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientID2"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="DOB"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Age"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Gender"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Height"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Weight"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="BedName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AdminDate"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="FacilityName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="CreatedOn"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Status"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:background="@drawable/textview_border"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="isTempPatient"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



</LinearLayout>
