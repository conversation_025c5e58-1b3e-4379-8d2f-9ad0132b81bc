<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="5dp"
    android:paddingBottom="10dp"
    android:orientation="horizontal"
   >

    <TextView
        android:id="@+id/setting_id"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:text="SettingID"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:gravity="center" />

    <TextView
        android:id="@+id/setting_name"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Setting Name"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/setting_value"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Setting Value"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



</LinearLayout>
