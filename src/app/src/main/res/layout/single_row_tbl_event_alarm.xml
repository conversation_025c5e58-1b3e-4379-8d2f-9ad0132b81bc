<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="2.5dp"
    android:paddingBottom="2.5dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/AlarmEventId"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmEventId"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"  />

    <TextView
        android:id="@+id/PatientId"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_weight="1"
        android:text="PatientId"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/PatientID1"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_weight="1"
        android:text="PatientID1"
        android:padding="10dp"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/DefaultAlarmId"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="DefaultAlarmId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/PatientAlarmId"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="PatientAlarmId"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmName"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AlarmName"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/TriggedBy"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="TriggedBy"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmCauseValue"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AlarmCauseValue"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/ValueOverLimit"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="ValueOverLimit"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmStartTime"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AlarmStartTime"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/AlarmEndTime"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="AlarmEndTime"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/Duration"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="Duration"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/isAcknowledged"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="isAcknowledged"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:id="@+id/TimeOfAcknowledge"
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="TimeOfAcknowledge"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/SilencedTime"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="SilencedTime"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:id="@+id/UploadStatus"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:padding="10dp"
        android:text="UploadStatus"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



</LinearLayout>

</HorizontalScrollView>
