<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:id="@+id/ecgBtn"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/ecg"/>

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:id="@+id/prBtn"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/pulseRate"/>

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:id="@+id/respBtn"
            android:textAllCaps="false"
            android:padding="10dp"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/respirations"/>

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:id="@+id/spo2Btn"
            android:textAllCaps="false"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/spo2"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/tempBtn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/temperature"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/bpBtn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/bloodPressure"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/activityBtn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/activity"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/settingsBtn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_margin="10dp"
            android:padding="10dp"
            android:textAllCaps="false"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:textColor="@color/ap_gray"
            android:layout_gravity="center"
            android:text="@string/settings"/>

    </LinearLayout>

</HorizontalScrollView>