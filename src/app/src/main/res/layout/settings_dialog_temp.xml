<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toTopOf="@id/navLayout"
        tools:layout_editor_absoluteX="0dp"
        tools:ignore="MissingConstraints,NotSibling">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/paramLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="top"
                android:layout_weight="2"
                android:orientation="horizontal"
                tools:ignore="NestedWeights"
                android:baselineAligned="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="10dp"
                    android:paddingEnd="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="match_parent"
                        android:layout_height="30dp"
                        android:layout_marginTop="10dp"
                        android:gravity="start|center"
                        android:layout_gravity="start"
                        android:text="@string/tempParameter"
                        android:textColor="@color/ap_gray"/>

                    <TextView
                        android:id="@+id/textView2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_gravity="center"
                        android:gravity="start"
                        android:text="@string/highTemp"
                        android:textColor="@color/ap_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">

                        <EditText
                            android:id="@+id/highTemp"
                            android:layout_width="100dp"
                            android:layout_height="40dp"
                            android:enabled="false"
                            android:layout_gravity="start"
                            android:autofillHints=""
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"
                            android:inputType="number"
                            android:paddingStart="20dp"
                            android:paddingEnd="0dp"
                            android:textColor="@color/white"
                            android:nextFocusDown="@id/lowTemp"
                            tools:ignore="LabelFor" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/tempHighIncButton"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:layout_marginStart="10dp"
                            android:textColor="@color/white"
                            android:text="@string/upIcon"
                            android:theme="@style/fontAwesomeText"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/lightBlue" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/tempHighDecButton"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:layout_marginStart="10dp"
                            android:textColor="@color/white"
                            android:text="@string/downIcon"
                            android:theme="@style/fontAwesomeText"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/lightBlue" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="10dp"
                    android:paddingEnd="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <Switch
                        android:id="@+id/tempAlarmSwitch"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_marginTop="10dp"
                        android:layout_gravity="center|start"
                        android:gravity="start|center"
                        android:checked="true"
                        android:scaleX="1.2"
                        android:scaleY="1.2"
                        tools:ignore="UseSwitchCompatOrMaterialXml" />

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_gravity="center"
                        android:gravity="start"
                        android:text="@string/lowTemp"
                        android:textColor="@color/ap_gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp">

                        <EditText
                            android:id="@+id/lowTemp"
                            android:layout_width="100dp"
                            android:layout_height="40dp"
                            android:enabled="false"
                            android:layout_gravity="start"
                            android:autofillHints=""
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"
                            android:inputType="number"
                            android:paddingStart="20dp"
                            android:paddingEnd="0dp"
                            android:imeOptions="actionDone"
                            android:textColor="@color/white"
                            tools:ignore="LabelFor" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/tempLowIncButton"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:layout_marginStart="10dp"
                            android:textColor="@color/white"
                            android:text="@string/upIcon"
                            android:theme="@style/fontAwesomeText"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/lightBlue" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/tempLowDecButton"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:layout_marginStart="10dp"
                            android:textColor="@color/white"
                            android:text="@string/downIcon"
                            android:theme="@style/fontAwesomeText"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/lightBlue" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="top"
                android:layout_weight="1"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <RadioGroup
                    android:id="@+id/currentSensor"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:gravity="center"
                    android:visibility="gone"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toBottomOf="@+id/lowTemp"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:ignore="UselessParent">

                    <RadioButton
                        android:id="@+id/chestRadio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingStart="10dp"
                        android:paddingEnd="20dp"
                        android:buttonTint="@color/lightBlue"
                        android:text="@string/chest"/>

                    <RadioButton
                        android:id="@+id/limbRadio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:buttonTint="@color/lightBlue"
                        android:text="@string/limb"/>

                </RadioGroup>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1.5"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="50dp"
                android:backgroundTint="@color/background">

                <TextView
                    android:id="@+id/volumeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tempVolume"
                    android:textColor="@color/ap_gray"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.097"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.31" />

                <com.google.android.material.slider.Slider
                    android:id="@+id/tempVolume"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    app:thumbColor="@color/white"
                    app:trackColorInactive="@color/components_gray"
                    app:trackColorActive="@color/lightBlue"/>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>