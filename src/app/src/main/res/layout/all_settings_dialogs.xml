<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.AppCompat"
    android:background="@color/ap_transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="40dp"
        android:background="@drawable/linear_layout_bg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/navLayout"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:orientation="horizontal"
            tools:ignore="MissingConstraints">

            <include
                layout="@layout/settings_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/closeBtn"
                android:layout_weight="6"
                android:gravity="end"
                android:background="@color/ap_transparent"
                android:theme="@style/fontAwesomeText"
                style="@style/TextAppearance.AppCompat.Display1"
                android:textColor="@color/white"
                android:text="@string/closeButtonIcon"/>
        </LinearLayout>

        <include android:id="@+id/ecgSection"
            layout="@layout/settings_dialog_ecg"
            android:visibility="gone"/>

        <include android:id="@+id/prSection"
            layout="@layout/settings_dialog_pr"
            android:visibility="gone"/>

        <include android:id="@+id/respSection"
            layout="@layout/settings_dialog_resp"
            android:visibility="gone"/>

        <include android:id="@+id/spo2Section"
            layout="@layout/settings_dialog_spo2"
            android:visibility="gone"/>

        <include android:id="@+id/tempSection"
            layout="@layout/settings_dialog_temp"
            android:visibility="gone"/>

        <include android:id="@+id/bpSection"
            layout="@layout/settings_dialog_bp"
            android:visibility="gone"/>

        <include android:id="@+id/settingsSection"
            layout="@layout/settings_dialog_settings"
            android:visibility="gone"/>

        <include android:id="@+id/activitySection"
            layout="@layout/settings_dialog_activity"
            android:visibility="gone"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>