<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="sensor"
            type="com.bodymount.app.common.CommonDataArea" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginTop="40dp"
            android:background="@drawable/linear_layout_bg"
            android:backgroundTint="@color/background"
            android:baselineAligned="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:layout_weight="1"
                android:baselineAligned="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="0.9"
                        android:outlineProvider="none"
                        app:cardBackgroundColor="@android:color/transparent"
                        tools:ignore="NestedWeights">

                        <TextView
                            android:id="@+id/noSensorText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/noDeviceConnected"
                            android:textColor="@color/ap_gray"
                            android:visibility="visible"
                            style="@style/TextAppearance.AppCompat.Title"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="visible"
                            android:orientation="vertical">


                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="70dp"
                                android:paddingStart="15dp"
                                android:paddingEnd="10dp"
                                android:paddingTop="10dp"
                                android:textColor="@color/white"
                                android:text="@string/manageDevice"
                                android:textStyle="bold"
                                style="@style/TextAppearance.AppCompat.Headline"/>

                            <LinearLayout
                                android:id="@+id/chestBoxOuter"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="40dp"
                                android:layout_marginEnd="40dp"
                                android:layout_marginBottom="10dp"
                                android:gravity="center"
                                android:background="@drawable/rounded_corner_view"
                                android:backgroundTint="@color/components_gray"
                                android:layout_weight="0.3"
                                android:orientation="horizontal"
                                android:visibility="invisible">


                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:padding="10dp"
                                    android:layout_marginStart="20dp"
                                    android:src="@drawable/ic_sensor_bp"
                                    android:layout_weight="4.7"
                                    app:tint="@color/white"/>

                                <TextView
                                    android:id="@+id/chestText"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:layout_gravity="center"
                                    android:text="@string/chestSensor"
                                    android:textColor="@color/white"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_weight="1.6" />

                                <com.mikepenz.iconics.view.IconicsTextView
                                    android:id="@+id/chestBattery"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|center_vertical"
                                    android:gravity="end"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:layout_marginStart="10dp"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_weight="5.2"
                                    tools:ignore="MissingClass" />

                                <androidx.appcompat.widget.AppCompatCheckBox
                                    android:id="@+id/chestBox"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:button="@drawable/custom_check_box2"
                                    android:layout_gravity="end|center"
                                    android:layout_weight="4.8"
                                    android:textColor="@color/white"/>

                            </LinearLayout>

                            <!--<LinearLayout
                                android:id="@+id/limbBoxOuter"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:layout_marginStart="40dp"
                                android:layout_marginEnd="40dp"
                                android:layout_marginBottom="10dp"
                                android:gravity="center"
                                android:background="@drawable/rounded_corner_view"
                                android:backgroundTint="@color/components_gray"
                                android:layout_weight="0.3"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:padding="10dp"
                                    android:layout_marginStart="20dp"
                                    android:src="@drawable/ic_sensor_limb"
                                    android:layout_weight="4.7"
                                    app:tint="@color/white"/>

                                <TextView
                                    android:id="@+id/limbText"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_width="216dp"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="10dp"
                                    android:layout_weight="1.6"
                                    android:text="@string/limbSensor"
                                    android:textColor="@color/white" />

                                <info.androidhive.fontawesome.FontTextView
                                    android:id="@+id/limbBattery"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|center_vertical"
                                    android:gravity="end"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:layout_marginStart="10dp"
                                    android:theme="@style/fontAwesomeText"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_weight="5.2"  />

                                <androidx.appcompat.widget.AppCompatCheckBox
                                    android:id="@+id/limbBox"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:button="@drawable/custom_check_box2"
                                    android:layout_weight="4.8"
                                    android:layout_gravity="end|center"/>

                            </LinearLayout>-->

                           <!-- <LinearLayout
                                android:id="@+id/bpBoxOuter"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:layout_marginStart="40dp"
                                android:layout_marginEnd="40dp"
                                android:layout_marginBottom="10dp"
                                android:background="@drawable/rounded_corner_view"
                                android:backgroundTint="@color/components_gray"
                                android:layout_weight="0.3"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:padding="10dp"
                                    android:layout_marginStart="20dp"
                                    android:src="@drawable/ic_sensor_bp"
                                    android:layout_weight="4.7"
                                    app:tint="@color/white"/>

                                <TextView
                                    android:id="@+id/bpText"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="10dp"
                                    android:layout_weight="1.6"
                                    android:text="@string/bpSensor"
                                    android:textColor="@color/white" />

                                <info.androidhive.fontawesome.FontTextView
                                    android:id="@+id/bpBattery"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|center_vertical"
                                    android:gravity="end"
                                    android:paddingStart="10dp"
                                    android:paddingEnd="10dp"
                                    android:layout_marginStart="10dp"
                                    android:theme="@style/fontAwesomeText"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_weight="5.2"  />

                                <androidx.appcompat.widget.AppCompatCheckBox
                                    android:id="@+id/bpBox"
                                    style="@style/TextAppearance.AppCompat.Title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end|center"
                                    android:layout_weight="4.8"
                                    android:button="@drawable/custom_check_box2"
                                    android:textColor="@color/white" />

                            </LinearLayout>-->

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:orientation="horizontal">

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.2"
                    android:orientation="vertical"
                    tools:ignore="UselessParent">

                    <Button
                        android:layout_width="wrap_content"
                        android:layout_height="70dp"
                        android:id="@+id/closeBtn"
                        android:layout_weight="0"
                        android:background="@color/ap_transparent"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:theme="@style/fontAwesomeText"
                        style="@style/TextAppearance.AppCompat.Display1"
                        android:textColor="@color/white"
                        android:text="@string/closeButtonIcon"
                        tools:ignore="NestedWeights" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <androidx.appcompat.widget.SearchView
                            android:id="@+id/searchBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            app:queryHint="search"
                            app:iconifiedByDefault="false"
                            app:searchIcon="@drawable/ic_search"
                            android:backgroundTint="@color/components_gray"
                            android:layout_weight="1"
                            app:queryBackground="@drawable/rounded_edit_text"
                            style="@style/SearchViewStyle"
                            android:background="@drawable/rounded_edit_text"/>

                    </LinearLayout>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:backgroundTint="@color/ap_transparent"
                        android:layout_weight="2"
                        app:cardElevation="0dp"
                        tools:ignore="NestedWeights">

                        <!--<ProgressBar
                            android:id="@+id/scanningProgress"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            style="@style/Animation.Design.BottomSheetDialog"
                            android:indeterminateTint="@color/white" />-->
                        <ImageView
                            android:id="@+id/scanningProgress"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:layout_gravity="center"
                            android:visibility="invisible"
                            android:src="@drawable/loading"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/sensorsList"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:choiceMode="multipleChoice"
                            android:divider="@color/ap_transparent"
                            android:dividerHeight="5dp"
                            tools:listitem="@layout/sensor_list_row"
                            tools:ignore="NestedWeights" />



                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_weight="5">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    tools:ignore="NestedWeights">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/cancel"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="start|bottom"
                        android:layout_marginStart="20dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:text="@string/cancel"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"/>

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"/>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/remove"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="end|bottom"
                        android:layout_marginEnd="20dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:textColor="@color/white"
                        android:visibility="invisible"
                        android:text="@string/remove"
                        android:backgroundTint="@color/red"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_edit_text" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center|end"
                    android:layout_weight="1.2">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/okBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_marginEnd="20dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:layout_gravity="end"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:text="@string/save"
                        android:backgroundTint="@color/lightBlue"
                        android:background="@drawable/rounded_edit_text" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>