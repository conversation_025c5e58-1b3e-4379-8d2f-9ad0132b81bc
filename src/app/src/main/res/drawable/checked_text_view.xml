<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="false">
        <layer-list>
            <item>
                <shape android:shape="rectangle"
                    android:padding="10dp">

                    <corners
                        android:bottomRightRadius="15dp"
                        android:bottomLeftRadius="15dp"
                        android:topLeftRadius="15dp"
                        android:topRightRadius="15dp" />

                    <solid android:color="@color/background"/>

                    <size
                        android:width="30dp"
                        android:height="30dp" />
                </shape>
            </item>

            <item
                android:width="25dp"
                android:height="25dp"
                android:top="2.5dp"
                android:left="2.5dp">

                <shape android:shape="rectangle">
                    <stroke android:color="@color/components_gray"
                        android:width="2dp"/>

                    <corners
                        android:bottomRightRadius="12.5dp"
                        android:bottomLeftRadius="12.5dp"
                        android:topLeftRadius="12.5dp"
                        android:topRightRadius="12.5dp" />

                    <solid android:color="@color/lightBlue"/>
                </shape>
            </item>
        </layer-list>
    </item>
</selector>