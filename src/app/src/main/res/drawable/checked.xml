<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="false">
        <layer-list>
            <item>
                <shape android:shape="rectangle"
                    android:padding="10dp">

                    <corners
                        android:bottomRightRadius="5dp"
                        android:bottomLeftRadius="5dp"
                        android:topLeftRadius="5dp"
                        android:topRightRadius="5dp" />

                    <gradient
                        android:startColor="@color/lightBlue"
                        android:centerColor="@color/lightBlue"
                        android:endColor="@color/lightBlue"
                        android:angle="270" />

                    <size
                        android:width="30dp"
                        android:height="30dp" />
                </shape>
            </item>

            <item
                android:width="8dp"
                android:height="2dp"
                android:top="18dp"
                android:left="6dp">
                <rotate
                    android:fromDegrees="45">
                    <shape android:shape="rectangle">
                        <solid android:color="@color/white"/>
                    </shape>
                </rotate>
            </item>

            <item
                android:width="19dp"
                android:height="2dp"
                android:top="14dp"
                android:left="8.8dp">
                <rotate
                    android:fromDegrees="-45">
                    <shape android:shape="rectangle">
                        <solid android:color="@color/white"/>
                    </shape>
                </rotate>
            </item>
        </layer-list>
    </item>

</selector>