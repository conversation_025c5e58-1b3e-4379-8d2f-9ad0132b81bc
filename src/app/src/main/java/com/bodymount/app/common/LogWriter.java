package com.bodymount.app.common;

import android.os.Environment;
import android.util.Log;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Dictionary;
import java.util.Hashtable;

 public class LogWriter {

    public static boolean disableLog = true;
    public static boolean disableDetailedLog = true;
    public static void writeLog(String content, String event)
    {
        try {
            if(disableLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/Log.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void writeAlarmLog(String content, String event)
    {
        try {
            if(disableLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/AlamLog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static int exceptionCount =0;
    public static void writeExceptLog(String content, String event)
    {
        try {
            if(disableLog) return;
            String filePath;
            //++exceptionCount;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/ExceptLog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void writeUILog(String content, String event)
    {
        try {
            if(disableLog) return;
            if(disableDetailedLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/UILog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    static void writeMemLog(String content, String event)
    {
        try {
            if(disableLog) return;
            if(disableDetailedLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/MemLog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                writeExceptLog("Write mem Log", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    static long memStartTime=0;
    public static long logMemUsage() {
        try {
            if ((System.currentTimeMillis() - memStartTime) < 60000) return -1;
            memStartTime = System.currentTimeMillis();
            long freeSize;
            long totalSize;
            long usedSize = -1L;
            try {
                Runtime info = Runtime.getRuntime();
                freeSize = info.freeMemory();
                totalSize = info.totalMemory();
                usedSize = totalSize - freeSize;
                writeMemLog("mem log", "FreeSize->" + freeSize + " Total Size->" + totalSize + " Used Size->" + usedSize);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return usedSize;
        }catch(Exception exp){
            exp.printStackTrace();
            return -1;
        }
    }

    public static void writeSensorLog(String content, String event)
    {
        try {
            if(disableLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/SensorLog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                writeExceptLog("Write SensorLog", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " +event
                    + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    static long startTime=0;
    public static void timeCheckPointStart(){
        startTime = System.currentTimeMillis();
    }
    public static void writeTimingLog(String event)
    {
        try {
            if(disableLog) return;
            if(disableDetailedLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/TimingLog.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                writeExceptLog("Write Timing Log", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(
                    "yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            long timeTaken = System.currentTimeMillis()-startTime;
            bw.write(dateFormat.format(date) + " : "  +event +" Time Taken ->"+timeTaken+ "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void writeTimingLogText(String event)
    {
        try {
            if(disableLog) return;
            String filePath;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/DataFrequency.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if(isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                LogWriter.writeExceptLog("Write timing log", e.getMessage());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : "  +event+"\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    static Dictionary<String, Integer> countersDic;
    static Dictionary<String, Long> timersDic;
    public static void initCounter(String name){
//        if(disableLog) return;
        if(countersDic==null) countersDic= new Hashtable<>();
        if(timersDic==null)  timersDic = new Hashtable<>();
        countersDic.put(name,0);
        timersDic.put(name,System.currentTimeMillis());
    }
    
    static long millsInMin = 10*1000;
    public static int calcFrequency(String name, int numSamples){
        try {
//            if(disableLog) return -1;
            if (timersDic == null || countersDic == null) {
                initCounter(name);
            }
            Long timeSt = timersDic.get(name);
            Integer count = countersDic.get(name);
            if ((timeSt == null) || (count == null)) {
                initCounter(name);
            }else{
                count+=numSamples;
                if ((System.currentTimeMillis() - timeSt) > millsInMin) {
                    int freq = (count * 1000) / (int) (System.currentTimeMillis() - timeSt);
//                    writeTimingLogText("Frequency of " + name + "=" + freq);
                    countersDic.put(name, 0);
                    timersDic.put(name, System.currentTimeMillis());
                    return freq;
                } else {
                    countersDic.put(name, count);
                }
            }
        }catch(Exception exp){
            /*writeExceptLog("Freq Counter Init",exp.getMessage());
            writeExceptLog("Freq Counter Init",exp.getStackTrace().toString());*/

        }
        return -1;
    }

    public static void calcFrequency(String name){
        try {
//            if(disableLog) return;
            if (timersDic == null || countersDic == null) {
                initCounter(name);
            }
            Long timeSt = timersDic.get(name);
            Integer count = countersDic.get(name);
            if ((timeSt == null) || (count == null)) {
                initCounter(name);
            }else{
                count++;
                if ((System.currentTimeMillis() - timeSt) > millsInMin) {
                    int freq = (count * 1000) / (int) (System.currentTimeMillis() - timeSt);
//                    writeTimingLog("Frequency of " + name + "=" + freq);
                    countersDic.put(name, 0);
                    timersDic.put(name, System.currentTimeMillis());
                } else {
                    countersDic.put(name, count);
                }
            }
        }catch(Exception exp){
//            writeExceptLog("Counter Init - 2",exp.getMessage());
        }

    }


    public static void writeException(String exception) throws IOException {
        BufferedWriter bw = null;
        try {
            String filePath;
            //++exceptionCount;
            File file = null;
            try {
                filePath = Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/GlobalExceptions.txt";
                if (!(new File(Environment.getExternalStorageDirectory()
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(Environment.getExternalStorageDirectory()
                            + "/SLHub" + "/")).mkdirs();
                    if (isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", e.getMessage());
            }

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            bw = new BufferedWriter(fw);
            bw.write(exception + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(bw != null)
                bw.close();
        }
    }

}
