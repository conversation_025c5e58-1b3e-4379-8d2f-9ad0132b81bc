package com.bodymount.app.common;

import android.inputmethodservice.InputMethodService;
import android.inputmethodservice.Keyboard;
import android.inputmethodservice.KeyboardView;
import android.media.AudioManager;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputConnection;
import com.bodymount.app.R;

public class KeyboardInput extends InputMethodService implements KeyboardView.OnKeyboardActionListener {

    private KeyboardView kv;
    private Keyboard keyboard,keyboardNumeric,keyboardSymbol;

    private boolean isCaps = false;


    @Override
    public View onCreateInputView() {
        kv = (KeyboardView) getLayoutInflater().inflate(R.layout.keyboard,null);
        keyboard =  new Keyboard(this,R.xml.keys);
        kv.setKeyboard(keyboard);
        kv.setOnKeyboardActionListener(this);
        return kv;
    }

    @Override
    public void onPress(int i) {

    }

    @Override
    public void onRelease(int i) {

    }

    @Override
    public void onKey(int i, int[] ints) {
        InputConnection ic = getCurrentInputConnection();
        if(ic != null){
            playclick(i);
            switch (i){
                case Keyboard.KEYCODE_DELETE:
                  ic.deleteSurroundingText(1,0);
                  break;
                case 3:
                    isCaps = !isCaps;
                    keyboard.setShifted(isCaps);
                    kv.invalidateAllKeys();
                    break;
                case Keyboard.KEYCODE_DONE:
                    ic.sendKeyEvent(new KeyEvent(KeyEvent.ACTION_DOWN,KeyEvent.KEYCODE_ENTER));
                    break;
                case -16:  // switch to numeric layout
                    if (kv.getKeyboard() == keyboard) {  // current layout is the original layout
                        keyboardNumeric = new Keyboard(this, R.xml.numeric_keys);
                        kv.setKeyboard(keyboardNumeric);
                        kv.setOnKeyboardActionListener(this);
                    } else {  // current layout is the numeric layout
                        kv.setKeyboard(keyboard);
                        kv.setOnKeyboardActionListener(this);
                    }
                    break;
                case -1:
                    if (kv.getKeyboard() == keyboardNumeric) {
                        keyboard = new Keyboard(this, R.xml.keys);
                        kv.setKeyboard(keyboard);
                        kv.setOnKeyboardActionListener(this);
                    } else {
                        kv.setKeyboard(keyboardNumeric);
                        kv.setOnKeyboardActionListener(this);
                    }
                    break;
                case -102:
                    if (kv.getKeyboard() == keyboardNumeric) {
                        keyboardSymbol = new Keyboard(this, R.xml.symbol_keys);
                        kv.setKeyboard(keyboardSymbol);
                        kv.setOnKeyboardActionListener(this);
                    } else {
                        kv.setKeyboard(keyboardNumeric);
                        kv.setOnKeyboardActionListener(this);
                    }
                    break;
                case -2:
                    if (kv.getKeyboard() == keyboardSymbol) {
                        keyboard = new Keyboard(this, R.xml.keys);
                        kv.setKeyboard(keyboard);
                        kv.setOnKeyboardActionListener(this);
                    } else {
                        kv.setKeyboard(keyboardSymbol);
                        kv.setOnKeyboardActionListener(this);
                    }
                    break;
                case 46: // Done button
                    // Perform your desired action for the Done button
                    // For example, you can close the keyboard
                    requestHideSelf(0);
                    break;
                case 2:
                    ic.deleteSurroundingText(Integer.MAX_VALUE, Integer.MAX_VALUE);
                    break;
                default:
                    char code = (char) i;
                    if(Character.isLetter(code) && isCaps)
                        code = Character.toUpperCase(code);
                    ic.commitText(String.valueOf(code),1);
            }
        }

    }

    private void playclick(int i) {
        AudioManager am = (AudioManager) getSystemService(AUDIO_SERVICE);
        switch (i){
            case 32:
                am.playSoundEffect(AudioManager.FX_KEYPRESS_SPACEBAR);
                break;
            case Keyboard.KEYCODE_DONE:
            case 10:
                am.playSoundEffect(AudioManager.FX_KEYPRESS_RETURN);
                break;
            case Keyboard.KEYCODE_DELETE:
                am.playSoundEffect(AudioManager.FX_KEYPRESS_DELETE);
                break;
            default:am.playSoundEffect(AudioManager.FX_KEYPRESS_STANDARD);
        }
    }

    @Override
    public void onText(CharSequence charSequence) {

    }

    @Override
    public void swipeLeft() {

    }

    @Override
    public void swipeRight() {

    }

    @Override
    public void swipeDown() {

    }

    @Override
    public void swipeUp() {

    }
}
