        private val MIGRATION_1_2 = object : Migration(1,2){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblParameters (paramId INTEGER PRIMARY KEY, parmaName TEXT NOT NULL, paramCategory TEXT, paramType INTEGER, linkedSensorDef TEXT)".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblDefaultAlarmSettings (alarmId INTEGER PRIMARY KEY, paramId INTEGER NOT NULL, defaultAlarmCategory TEXT, defaultAlarmHighValue REAL, defaultAlarmLowValue REAL, defaultAlarmExtremeHighValue REAL, defaultAlarmExtremeLowValue REAL, defaultAlarmStatus INTEGER NOT NULL)".trimIndent())
            }
        }

        private val MIGRATION_2_3 = object : Migration(2,3){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblPatient ADD COLUMN status TEXT NOT NULL DEFAULT 'active'")
            }
        }

        private val MIGRATION_3_4 = object: Migration(3,4){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblPatientAlarm (patientAlarmId INTEGER PRIMARY KEY, patientId INTEGER NOT NULL, paramId INTEGER NOT NULL, alarmCategory TEXT, alarmName TEXT, value TEXT, lowValue REAL, extremeLowValue REAL, highValue REAL, extremeHighValue REAL, timeStamp TEXT NOT NULL, alarmStatus INTEGER NOT NULL)".trimIndent())
                database.execSQL("ALTER TABLE tblParameters RENAME COLUMN parmaName to paramName")
            }
        }

        private val MIGRATION_4_5 = object: Migration(4,5){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblSensorDef (sensorDefId INTEGER PRIMARY KEY, sensorCategory TEXT NOT NULL, sensorName TEXT NOT NULL, sensorType TEXT NOT NULL)".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblSensors (sensorId INTEGER PRIMARY KEY, sensorDefId INTEGER NOT NULL, patientId INTEGER NOT NULL, sensorType TEXT NOT NULL, sensorName TEXT NOT NULL, sensorAddress TEXT, registerTimestamp TEXT NOT NULL, pairingStatus TEXT DEFAULT 'not connected')".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblPatientDataEx (visitId INTEGER PRIMARY KEY, patientId INTEGER NOT NULL, PatientID2 TEXT, visitDate TEXT NOT NULL, admitDateTime TEXT)".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblMeasurementData (measurementId INTEGER PRIMARY KEY, visitId INTEGER NOT NULL, sensorId INTEGER, paramId INTEGER NOT NULL, PatientID1 TEXT, valueType TEXT, paramName TEXT NOT NULL, value REAL, measurementData BLOB, numberOfSamples INTEGER, timestamp TEXT NOT NULL, uploadStatus INTEGER DEFAULT 0, uploadTimestamp TEXT)".trimIndent())
            }

        }

        private val MIGRATION_5_6 = object: Migration(5,6){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblSensor (sensorId INTEGER PRIMARY KEY, sensorDefId INTEGER, patientId INTEGER NOT NULL, sensorType TEXT NOT NULL, sensorName TEXT NOT NULL, sensorAddress TEXT, registerTimestamp TEXT NOT NULL, pairingStatus TEXT DEFAULT 'not connected')".trimIndent())
                database.execSQL("DROP TABLE tblSensors")
                database.execSQL("ALTER TABLE tblSensor RENAME TO tblSensors")
            }

        }

        private val MIGRATION_6_7 = object: Migration(6,7){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblPatientDataEx RENAME TO tblVisit")
            }
        }

        private val MIGRATION_7_8 = object: Migration(7,8){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblAlarmEvent (alarmEventId INTEGER PRIMARY KEY, patientId INTEGER NOT NULL, patientID1 TEXT NOT NULL, defaultAlarmId INTEGER, patientAlarmId INTEGER, alarmName TEXT, triggeredBy INTEGER NOT NULL, alarmCauseValue REAL NOT NULL, valueOverLimit REAL, alarmStartTime TEXT NOT NULL, alarmEndTime TEXT, duration INTEGER, isAcknowledged INTEGER NOT NULL DEFAULT 0, timeOfAcknowledge INTEGER, silencedTime TEXT, uploadStatus INTEGER DEFAULT 0)".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblEventList (eventId INTEGER PRIMARY KEY, eventName TEXT NOT NULL, eventType TEXT NOT NULL)".trimIndent())
                database.execSQL("CREATE TABLE IF NOT EXISTS tblEventsLog (eventLogId INTEGER PRIMARY KEY, eventId INTEGER NOT NULL, patientId INTEGER NOT NULL, patientID1 TEXT NOT NULL, timeStamp TEXT NOT NULL)".trimIndent())
            }
        }

        private val MIGRATION_8_9 = object: Migration(8,9){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblDefaultAlarmSettings ADD COLUMN defaultSound INTEGER NOT NULL DEFAULT 15")
            }
        }

        private val MIGRATION_9_10 = object: Migration(10,9){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblPatientAlarm ADD COLUMN alarmSound INTEGER")
            }
        }

        private val MIGRATION_10_11 = object: Migration(10, 11){
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblPatient ADD COLUMN isTempPatient INTEGER NOT NULL DEFAULT 1")
            }

        }

        private val MIGRATION_11_12 = object: Migration(11,12) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("CREATE TABLE IF NOT EXISTS tblSettings (id INTEGER PRIMARY KEY AUTOINCREMENT, clearanceTime INTEGER NOT NULL )")
            }
        }

        private val MIGRATION_12_13 = object: Migration(12,13) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Rename the existing clearanceTime column to settingsName and change its type to TEXT
                database.execSQL("ALTER TABLE tblSettings RENAME COLUMN clearanceTime TO settingsName")
                database.execSQL("ALTER TABLE tblSettings RENAME TO temp_table")
                database.execSQL("CREATE TABLE tblSettings (id INTEGER PRIMARY KEY AUTOINCREMENT, settingsName TEXT NOT NULL, settingsValue TEXT NOT NULL )")
                database.execSQL("INSERT INTO tblSettings (id, settingsName, settingsValue) SELECT id, settingsName, '' FROM temp_table")
                database.execSQL("DROP TABLE temp_table")
            }
        }

        private val MIGRATION_13_14 = object: Migration(13,14) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE tblMeasurementData ADD COLUMN measurementUuid TEXT NOT NULL DEFAULT ''")
            }
        }