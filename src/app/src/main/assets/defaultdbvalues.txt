{"parameters": [{"paramName": "HR", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 150.0, "defaultAlarmLowValue": 50.0, "defaultAlarmExtremeHighValue": 170.0, "defaultAlarmExtremeLowValue": 40.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 301.0}, {"paramName": "RR", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 30.0, "defaultAlarmLowValue": 6.0, "defaultAlarmExtremeHighValue": 20.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 121.0}, {"paramName": "SpO2", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 100.0, "defaultAlarmLowValue": 90.0, "defaultAlarmExtremeHighValue": 20.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 101.0}, {"paramName": "COUGH_COUNT", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "ECG", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "RESP", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "PR", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 150.0, "defaultAlarmLowValue": 50.0, "defaultAlarmExtremeHighValue": 170.0, "defaultAlarmExtremeLowValue": 40.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 200.0}, {"paramName": "PI", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "TEMP", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 98.4, "defaultAlarmLowValue": 92.3, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 123.0}, {"paramName": "PPG", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "BP_SYS", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 160.0, "defaultAlarmLowValue": 90.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 271.0}, {"paramName": "BP_DIA", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 90.0, "defaultAlarmLowValue": 50.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 211.0}, {"paramName": "BP", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "BODY_POSITION", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}, {"paramName": "FALL", "paramType": null, "paramCategory": null, "linkedSensorDef": null, "defaultAlarmCategory": null, "defaultAlarmHighValue": 0.0, "defaultAlarmLowValue": 0.0, "defaultAlarmExtremeHighValue": 0.0, "defaultAlarmExtremeLowValue": 0.0, "defaultAlarmStatus": 1, "defaultSound": 15, "highMaxAllowed": 0.0}], "events": [{"eventName": "enrolled new chest sensor", "eventType": "sensor event"}, {"eventName": "enrolled new limb sensor", "eventType": "sensor event"}, {"eventName": "connected to chest sensor", "eventType": "sensor event"}, {"eventName": "connected to limb sensor", "eventType": "sensor event"}, {"eventName": "turn on bluetooth", "eventType": "device event"}, {"eventName": "turn off bluetooth", "eventType": "device event"}, {"eventName": "patient discharged", "eventType": "server event"}, {"eventName": "patient changed", "eventType": "server event"}, {"eventName": "ecg settings changed", "eventType": "server event"}, {"eventName": "resp settings changed", "eventType": "server event"}, {"eventName": "spo2 settings changed", "eventType": "server event"}, {"eventName": "chest patch connection lost", "eventType": "sensor event"}, {"eventName": "limb patch connection lost", "eventType": "sensor event"}, {"eventName": "chest patch disconnected", "eventType": "sensor event"}, {"eventName": "limb patch disconnected", "eventType": "sensor event"}, {"eventName": "chest patch onCharging", "eventType": "sensor event"}, {"eventName": "limb patch onCharging", "eventType": "sensor event"}]}