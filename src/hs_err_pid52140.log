#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 297795584 bytes for Failed to commit area from 0x00000000c4300000 to 0x00000000d5f00000 of length 297795584.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3521), pid=52140, tid=16228
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 17:53:30 2022 India Standard Time elapsed time: 41.812116 seconds (0d 0h 0m 41s)

---------------  T H R E A D  ---------------

Current thread (0x000001f878dd7800):  VMThread "VM Thread" [stack: 0x000000cf03300000,0x000000cf03400000] [id=16228]

Stack: [0x000000cf03300000,0x000000cf03400000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000001f86402112d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x733535]
V  [jvm.dll+0x7334eb]
V  [jvm.dll+0x5fb080]
V  [jvm.dll+0x5fb818]
C  [ntdll.dll+0xa8fcf]
C  [ntdll.dll+0x35e9a]
C  [ntdll.dll+0xa7fde]
C  0x000001f86402112d

VM_Operation (0x000000cf08cfd020): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001f801cab800


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f804f5de90, length=96, elements={
0x000001f86050f800, 0x000001f878dda000, 0x000001f878dfe800, 0x000001f878e21000,
0x000001f878e21800, 0x000001f878e23000, 0x000001f878e24000, 0x000001f878e29800,
0x000001f878e37000, 0x000001f8798cd800, 0x000001f879b6e800, 0x000001f87a17a000,
0x000001f87b1a7000, 0x000001f87b8ae000, 0x000001f87b24c000, 0x000001f87a222000,
0x000001f879fdf800, 0x000001f87b905800, 0x000001f87b11b000, 0x000001f87b8d9800,
0x000001f87b8da800, 0x000001f87b8dd000, 0x000001f87b8dc000, 0x000001f87b8d7000,
0x000001f87b8d8000, 0x000001f87b8db000, 0x000001f87b8d9000, 0x000001f87b8dd800,
0x000001f87c6a9000, 0x000001f87c6ac000, 0x000001f87c6ab800, 0x000001f87c6ad000,
0x000001f87c6aa800, 0x000001f87c6b3800, 0x000001f87c6b0000, 0x000001f87c6b2000,
0x000001f87c6ad800, 0x000001f87c6b2800, 0x000001f87c6af800, 0x000001f87c6b4800,
0x000001f87c6ae800, 0x000001f87c6b5000, 0x000001f87c6b6000, 0x000001f87c6b6800,
0x000001f87c6b1000, 0x000001f87c6b7800, 0x000001f87bf9f000, 0x000001f87bfa2800,
0x000001f87bfa3000, 0x000001f87bfa1800, 0x000001f87bfa0000, 0x000001f87bfa0800,
0x000001f87bfa4000, 0x000001f87bf9e800, 0x000001f87bfa9800, 0x000001f87bfa7800,
0x000001f87bfa6800, 0x000001f87bfa9000, 0x000001f87bfaa800, 0x000001f87bfa5000,
0x000001f87bfab800, 0x000001f87bfa5800, 0x000001f87bfac000, 0x000001f87bfad000,
0x000001f87bfa8000, 0x000001f87df9b000, 0x000001f87df9a800, 0x000001f87df9c000,
0x000001f87df9c800, 0x000001f87df97000, 0x000001f87df9d800, 0x000001f87df98800,
0x000001f87df99800, 0x000001f87df98000, 0x000001f87df9e800, 0x000001f87dfa5000,
0x000001f87dfa4000, 0x000001f87dfa2800, 0x000001f87df9f000, 0x000001f87dfa0000,
0x000001f87dfa3800, 0x000001f87dfa1000, 0x000001f87dfa1800, 0x000001f801cac000,
0x000001f801ca9800, 0x000001f801cab800, 0x000001f801cad000, 0x000001f801caa800,
0x000001f801cae000, 0x000001f801cae800, 0x000001f801cb0800, 0x000001f801cb5000,
0x000001f801cb3800, 0x000001f801cb2000, 0x000001f801cb4800, 0x000001f801cb1000
}

Java Threads: ( => current thread )
  0x000001f86050f800 JavaThread "main" [_thread_blocked, id=29748, stack(0x000000cf02d00000,0x000000cf02e00000)]
  0x000001f878dda000 JavaThread "Reference Handler" daemon [_thread_blocked, id=70568, stack(0x000000cf03400000,0x000000cf03500000)]
  0x000001f878dfe800 JavaThread "Finalizer" daemon [_thread_blocked, id=70108, stack(0x000000cf03500000,0x000000cf03600000)]
  0x000001f878e21000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=55140, stack(0x000000cf03600000,0x000000cf03700000)]
  0x000001f878e21800 JavaThread "Attach Listener" daemon [_thread_blocked, id=73296, stack(0x000000cf03700000,0x000000cf03800000)]
  0x000001f878e23000 JavaThread "Service Thread" daemon [_thread_blocked, id=68928, stack(0x000000cf03800000,0x000000cf03900000)]
  0x000001f878e24000 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=73372, stack(0x000000cf03900000,0x000000cf03a00000)]
  0x000001f878e29800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=69456, stack(0x000000cf03a00000,0x000000cf03b00000)]
  0x000001f878e37000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=69304, stack(0x000000cf03b00000,0x000000cf03c00000)]
  0x000001f8798cd800 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=44484, stack(0x000000cf03c00000,0x000000cf03d00000)]
  0x000001f879b6e800 JavaThread "Daemon health stats" [_thread_blocked, id=28352, stack(0x000000cf04000000,0x000000cf04100000)]
  0x000001f87a17a000 JavaThread "Incoming local TCP Connector on port 51661" [_thread_in_native, id=72240, stack(0x000000cf04100000,0x000000cf04200000)]
  0x000001f87b1a7000 JavaThread "Daemon periodic checks" [_thread_blocked, id=73168, stack(0x000000cf04200000,0x000000cf04300000)]
  0x000001f87b8ae000 JavaThread "Daemon" [_thread_blocked, id=62032, stack(0x000000cf04300000,0x000000cf04400000)]
  0x000001f87b24c000 JavaThread "Handler for socket connection from /127.0.0.1:51661 to /127.0.0.1:51662" [_thread_in_native, id=34732, stack(0x000000cf04400000,0x000000cf04500000)]
  0x000001f87a222000 JavaThread "Cancel handler" [_thread_blocked, id=6256, stack(0x000000cf04600000,0x000000cf04700000)]
  0x000001f879fdf800 JavaThread "Daemon worker" [_thread_blocked, id=73196, stack(0x000000cf04700000,0x000000cf04800000)]
  0x000001f87b905800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51661 to /127.0.0.1:51662" [_thread_blocked, id=58600, stack(0x000000cf04800000,0x000000cf04900000)]
  0x000001f87b11b000 JavaThread "Stdin handler" [_thread_blocked, id=43700, stack(0x000000cf04900000,0x000000cf04a00000)]
  0x000001f87b8d9800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=70720, stack(0x000000cf04a00000,0x000000cf04b00000)]
  0x000001f87b8da800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=69512, stack(0x000000cf04b00000,0x000000cf04c00000)]
  0x000001f87b8dd000 JavaThread "File lock request listener" [_thread_in_native, id=70160, stack(0x000000cf04c00000,0x000000cf04d00000)]
  0x000001f87b8dc000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=73128, stack(0x000000cf04d00000,0x000000cf04e00000)]
  0x000001f87b8d7000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=71664, stack(0x000000cf04e00000,0x000000cf04f00000)]
  0x000001f87b8d8000 JavaThread "File watcher server" daemon [_thread_blocked, id=72412, stack(0x000000cf04f00000,0x000000cf05000000)]
  0x000001f87b8db000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=72320, stack(0x000000cf05000000,0x000000cf05100000)]
  0x000001f87b8d9000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=67496, stack(0x000000cf05100000,0x000000cf05200000)]
  0x000001f87b8dd800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=72288, stack(0x000000cf05200000,0x000000cf05300000)]
  0x000001f87c6a9000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=59796, stack(0x000000cf05300000,0x000000cf05400000)]
  0x000001f87c6ac000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=73548, stack(0x000000cf05400000,0x000000cf05500000)]
  0x000001f87c6ab800 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=60616, stack(0x000000cf05500000,0x000000cf05600000)]
  0x000001f87c6ad000 JavaThread "jar transforms" [_thread_blocked, id=71188, stack(0x000000cf05600000,0x000000cf05700000)]
  0x000001f87c6aa800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=66664, stack(0x000000cf05700000,0x000000cf05800000)]
  0x000001f87c6b3800 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=60220, stack(0x000000cf05800000,0x000000cf05900000)]
  0x000001f87c6b0000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=52184, stack(0x000000cf05900000,0x000000cf05a00000)]
  0x000001f87c6b2000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=69308, stack(0x000000cf05a00000,0x000000cf05b00000)]
  0x000001f87c6ad800 JavaThread "Unconstrained build operations" [_thread_blocked, id=72548, stack(0x000000cf05b00000,0x000000cf05c00000)]
  0x000001f87c6b2800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=73500, stack(0x000000cf05c00000,0x000000cf05d00000)]
  0x000001f87c6af800 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=72264, stack(0x000000cf05d00000,0x000000cf05e00000)]
  0x000001f87c6b4800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=53700, stack(0x000000cf05e00000,0x000000cf05f00000)]
  0x000001f87c6ae800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=64220, stack(0x000000cf05f00000,0x000000cf06000000)]
  0x000001f87c6b5000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=56988, stack(0x000000cf06000000,0x000000cf06100000)]
  0x000001f87c6b6000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=52480, stack(0x000000cf06100000,0x000000cf06200000)]
  0x000001f87c6b6800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=55344, stack(0x000000cf06200000,0x000000cf06300000)]
  0x000001f87c6b1000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=49460, stack(0x000000cf06300000,0x000000cf06400000)]
  0x000001f87c6b7800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=53704, stack(0x000000cf06400000,0x000000cf06500000)]
  0x000001f87bf9f000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=46792, stack(0x000000cf06500000,0x000000cf06600000)]
  0x000001f87bfa2800 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=58820, stack(0x000000cf06600000,0x000000cf06700000)]
  0x000001f87bfa3000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=72096, stack(0x000000cf06700000,0x000000cf06800000)]
  0x000001f87bfa1800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=72928, stack(0x000000cf06800000,0x000000cf06900000)]
  0x000001f87bfa0000 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=67476, stack(0x000000cf06900000,0x000000cf06a00000)]
  0x000001f87bfa0800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=50192, stack(0x000000cf06a00000,0x000000cf06b00000)]
  0x000001f87bfa4000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=54280, stack(0x000000cf06b00000,0x000000cf06c00000)]
  0x000001f87bf9e800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=72932, stack(0x000000cf06c00000,0x000000cf06d00000)]
  0x000001f87bfa9800 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=72420, stack(0x000000cf06d00000,0x000000cf06e00000)]
  0x000001f87bfa7800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=47712, stack(0x000000cf06e00000,0x000000cf06f00000)]
  0x000001f87bfa6800 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=73412, stack(0x000000cf06f00000,0x000000cf07000000)]
  0x000001f87bfa9000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=67948, stack(0x000000cf07000000,0x000000cf07100000)]
  0x000001f87bfaa800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=42644, stack(0x000000cf07100000,0x000000cf07200000)]
  0x000001f87bfa5000 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=61596, stack(0x000000cf07200000,0x000000cf07300000)]
  0x000001f87bfab800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=57408, stack(0x000000cf07300000,0x000000cf07400000)]
  0x000001f87bfa5800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=39268, stack(0x000000cf07400000,0x000000cf07500000)]
  0x000001f87bfac000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=55100, stack(0x000000cf07500000,0x000000cf07600000)]
  0x000001f87bfad000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=6716, stack(0x000000cf07600000,0x000000cf07700000)]
  0x000001f87bfa8000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=67296, stack(0x000000cf07700000,0x000000cf07800000)]
  0x000001f87df9b000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=57224, stack(0x000000cf07800000,0x000000cf07900000)]
  0x000001f87df9a800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=72780, stack(0x000000cf07900000,0x000000cf07a00000)]
  0x000001f87df9c000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=72372, stack(0x000000cf07a00000,0x000000cf07b00000)]
  0x000001f87df9c800 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=69444, stack(0x000000cf07b00000,0x000000cf07c00000)]
  0x000001f87df97000 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=72652, stack(0x000000cf07c00000,0x000000cf07d00000)]
  0x000001f87df9d800 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=73348, stack(0x000000cf07d00000,0x000000cf07e00000)]
  0x000001f87df98800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=71992, stack(0x000000cf07e00000,0x000000cf07f00000)]
  0x000001f87df99800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=72424, stack(0x000000cf07f00000,0x000000cf08000000)]
  0x000001f87df98000 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=72380, stack(0x000000cf08000000,0x000000cf08100000)]
  0x000001f87df9e800 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=71748, stack(0x000000cf08100000,0x000000cf08200000)]
  0x000001f87dfa5000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=73476, stack(0x000000cf08200000,0x000000cf08300000)]
  0x000001f87dfa4000 JavaThread "jar transforms Thread 4" [_thread_blocked, id=73532, stack(0x000000cf08300000,0x000000cf08400000)]
  0x000001f87dfa2800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=72016, stack(0x000000cf08400000,0x000000cf08500000)]
  0x000001f87df9f000 JavaThread "Memory manager" [_thread_blocked, id=66828, stack(0x000000cf08500000,0x000000cf08600000)]
  0x000001f87dfa0000 JavaThread "build event listener" [_thread_blocked, id=66820, stack(0x000000cf08600000,0x000000cf08700000)]
  0x000001f87dfa3800 JavaThread "Execution worker for ':'" [_thread_blocked, id=73648, stack(0x000000cf08700000,0x000000cf08800000)]
  0x000001f87dfa1000 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=53016, stack(0x000000cf08800000,0x000000cf08900000)]
  0x000001f87dfa1800 JavaThread "Execution worker for ':' Thread 3" [_thread_blocked, id=73604, stack(0x000000cf08900000,0x000000cf08a00000)]
  0x000001f801cac000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=69288, stack(0x000000cf08a00000,0x000000cf08b00000)]
  0x000001f801ca9800 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=70732, stack(0x000000cf08b00000,0x000000cf08c00000)]
  0x000001f801cab800 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=42260, stack(0x000000cf08c00000,0x000000cf08d00000)]
  0x000001f801cad000 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=69848, stack(0x000000cf08d00000,0x000000cf08e00000)]
  0x000001f801caa800 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=71176, stack(0x000000cf08e00000,0x000000cf08f00000)]
  0x000001f801cae000 JavaThread "pool-3-thread-1" [_thread_blocked, id=71504, stack(0x000000cf08f00000,0x000000cf09000000)]
  0x000001f801cae800 JavaThread "WorkerExecutor Queue Thread 5" [_thread_blocked, id=70164, stack(0x000000cf09200000,0x000000cf09300000)]
  0x000001f801cb0800 JavaThread "stderr" [_thread_in_native, id=73592, stack(0x000000cf09400000,0x000000cf09500000)]
  0x000001f801cb5000 JavaThread "stdout" [_thread_in_native, id=69560, stack(0x000000cf09500000,0x000000cf09600000)]
  0x000001f801cb3800 JavaThread "ForkJoinPool-1-worker-3" daemon [_thread_blocked, id=71900, stack(0x000000cf09600000,0x000000cf09700000)]
  0x000001f801cb2000 JavaThread "ForkJoinPool-1-worker-5" daemon [_thread_blocked, id=42312, stack(0x000000cf09700000,0x000000cf09800000)]
  0x000001f801cb4800 JavaThread "ForkJoinPool-1-worker-7" daemon [_thread_blocked, id=71428, stack(0x000000cf09800000,0x000000cf09900000)]
  0x000001f801cb1000 JavaThread "ForkJoinPool-1-worker-1" daemon [_thread_blocked, id=73060, stack(0x000000cf09900000,0x000000cf09a00000)]

Other Threads:
=>0x000001f878dd7800 VMThread "VM Thread" [stack: 0x000000cf03300000,0x000000cf03400000] [id=16228]
  0x000001f8798e1800 WatcherThread [stack: 0x000000cf03d00000,0x000000cf03e00000] [id=73576]
  0x000001f860528000 GCTaskThread "GC Thread#0" [stack: 0x000000cf02e00000,0x000000cf02f00000] [id=73656]
  0x000001f879c66000 GCTaskThread "GC Thread#1" [stack: 0x000000cf03e00000,0x000000cf03f00000] [id=73420]
  0x000001f87a0fd000 GCTaskThread "GC Thread#2" [stack: 0x000000cf03f00000,0x000000cf04000000] [id=68792]
  0x000001f87b24d000 GCTaskThread "GC Thread#3" [stack: 0x000000cf04500000,0x000000cf04600000] [id=72048]
  0x000001f860550000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000cf02f00000,0x000000cf03000000] [id=72820]
  0x000001f860553000 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000cf03000000,0x000000cf03100000] [id=72688]
  0x000001f8605e2000 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000cf03100000,0x000000cf03200000] [id=55088]
  0x000001f8605e6800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x000000cf03200000,0x000000cf03300000] [id=69228]

Threads with active compile tasks:
C2 CompilerThread0  41844 17049       4       com.android.tools.r8.dex.j::a (1042 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f86050a6f0] Threads_lock - owner thread: 0x000001f878dd7800
[0x000001f86050aa50] Heap_lock - owner thread: 0x000001f801cab800

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 883712K, used 410880K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 121472K, capacity 124302K, committed 124460K, reserved 1157120K
  class space    used 15359K, capacity 16434K, committed 16512K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Untracked 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Untracked 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Untracked 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%|HS|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Complete 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%|HS|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Complete 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%|HS|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Complete 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| O|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Untracked 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| O|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Untracked 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| O|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Untracked 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| O|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Untracked 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%|HS|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Complete 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%|HC|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Complete 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%|HC|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Complete 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%|HS|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Complete 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%|HC|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Complete 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3000000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3100000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3200000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3700000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4300000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4800000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4a00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000, 0x00000000a4f00000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000, 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5200000, 0x00000000a5100000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5300000, 0x00000000a5200000| Untracked 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5400000, 0x00000000a5300000| Untracked 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5500000, 0x00000000a5400000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5600000, 0x00000000a5500000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5700000, 0x00000000a5600000| Untracked 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5800000, 0x00000000a5700000| Untracked 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5900000, 0x00000000a5800000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%|HS|  |TAMS 0x00000000a5a00000, 0x00000000a5900000| Complete 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5b00000, 0x00000000a5a00000| Untracked 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%|HS|  |TAMS 0x00000000a5c00000, 0x00000000a5b00000| Complete 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5d00000, 0x00000000a5c00000| Untracked 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5e00000, 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5f00000, 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a6000000, 0x00000000a5f00000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%|HS|  |TAMS 0x00000000a6100000, 0x00000000a6000000| Complete 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000, 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000, 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6700000, 0x00000000a6600000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%|HS|  |TAMS 0x00000000a6800000, 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000, 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000, 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6b00000, 0x00000000a6a00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6c00000, 0x00000000a6b00000| Untracked 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%|HS|  |TAMS 0x00000000a6d00000, 0x00000000a6c00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|  |TAMS 0x00000000a6e00000, 0x00000000a6d00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%|HS|  |TAMS 0x00000000a6f00000, 0x00000000a6e00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%|HC|  |TAMS 0x00000000a7000000, 0x00000000a6f00000| Complete 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%|HS|  |TAMS 0x00000000a7100000, 0x00000000a7000000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%|HC|  |TAMS 0x00000000a7200000, 0x00000000a7100000| Complete 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%|HS|  |TAMS 0x00000000a7300000, 0x00000000a7200000| Complete 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%|HC|  |TAMS 0x00000000a7400000, 0x00000000a7300000| Complete 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%|HS|  |TAMS 0x00000000a7500000, 0x00000000a7400000| Complete 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%|HS|  |TAMS 0x00000000a7600000, 0x00000000a7500000| Complete 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%|HS|  |TAMS 0x00000000a7700000, 0x00000000a7600000| Complete 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%|HC|  |TAMS 0x00000000a7800000, 0x00000000a7700000| Complete 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%|HS|  |TAMS 0x00000000a7900000, 0x00000000a7800000| Complete 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%|HC|  |TAMS 0x00000000a7a00000, 0x00000000a7900000| Complete 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%|HS|  |TAMS 0x00000000a7b00000, 0x00000000a7a00000| Complete 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%|HC|  |TAMS 0x00000000a7c00000, 0x00000000a7b00000| Complete 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%|HC|  |TAMS 0x00000000a7d00000, 0x00000000a7c00000| Complete 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%|HC|  |TAMS 0x00000000a7e00000, 0x00000000a7d00000| Complete 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%|HS|  |TAMS 0x00000000a7f00000, 0x00000000a7e00000| Complete 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%|HS|  |TAMS 0x00000000a8000000, 0x00000000a7f00000| Complete 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%|HC|  |TAMS 0x00000000a8100000, 0x00000000a8000000| Complete 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%|HS|  |TAMS 0x00000000a8200000, 0x00000000a8100000| Complete 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%|HC|  |TAMS 0x00000000a8300000, 0x00000000a8200000| Complete 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HC|  |TAMS 0x00000000a8400000, 0x00000000a8300000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%|HS|  |TAMS 0x00000000a8500000, 0x00000000a8400000| Complete 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%|HS|  |TAMS 0x00000000a8600000, 0x00000000a8500000| Complete 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%|HS|  |TAMS 0x00000000a8700000, 0x00000000a8600000| Complete 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%|HS|  |TAMS 0x00000000a8800000, 0x00000000a8700000| Complete 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8900000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8b00000, 0x00000000a8a00000| Untracked 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8c00000, 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8d00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8f00000, 0x00000000a8e00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a9000000, 0x00000000a8f00000| Untracked 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9100000, 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9200000, 0x00000000a9100000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%|HS|  |TAMS 0x00000000a9300000, 0x00000000a9200000| Complete 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9400000, 0x00000000a9300000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%|HS|  |TAMS 0x00000000a9500000, 0x00000000a9400000| Complete 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%|HC|  |TAMS 0x00000000a9600000, 0x00000000a9500000| Complete 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%|HS|  |TAMS 0x00000000a9700000, 0x00000000a9600000| Complete 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%|HS|  |TAMS 0x00000000a9800000, 0x00000000a9700000| Complete 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%|HC|  |TAMS 0x00000000a9900000, 0x00000000a9800000| Complete 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%|HS|  |TAMS 0x00000000a9a00000, 0x00000000a9900000| Complete 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000, 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9c00000, 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9d00000, 0x00000000a9c00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9e00000, 0x00000000a9d00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9f00000, 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000aa000000, 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa100000, 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa200000, 0x00000000aa100000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa300000, 0x00000000aa200000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa400000, 0x00000000aa300000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa500000, 0x00000000aa400000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| O|  |TAMS 0x00000000aa600000, 0x00000000aa500000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%|HS|  |TAMS 0x00000000aa700000, 0x00000000aa600000| Complete 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%|HC|  |TAMS 0x00000000aa800000, 0x00000000aa700000| Complete 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000, 0x00000000aa800000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aaa00000, 0x00000000aa900000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%|HS|  |TAMS 0x00000000aab00000, 0x00000000aaa00000| Complete 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%|HC|  |TAMS 0x00000000aac00000, 0x00000000aab00000| Complete 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%|HS|  |TAMS 0x00000000aad00000, 0x00000000aac00000| Complete 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%|HS|  |TAMS 0x00000000aae00000, 0x00000000aad00000| Complete 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aaf00000, 0x00000000aae00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%|HS|  |TAMS 0x00000000ab000000, 0x00000000aaf00000| Complete 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|  |TAMS 0x00000000ab000000, 0x00000000ab000000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab200000, 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| O|  |TAMS 0x00000000ab300000, 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%|HS|  |TAMS 0x00000000ab400000, 0x00000000ab300000| Complete 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%|HC|  |TAMS 0x00000000ab500000, 0x00000000ab400000| Complete 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%|HS|  |TAMS 0x00000000ab600000, 0x00000000ab500000| Complete 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%|HC|  |TAMS 0x00000000ab700000, 0x00000000ab600000| Complete 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%|HC|  |TAMS 0x00000000ab800000, 0x00000000ab700000| Complete 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%|HS|  |TAMS 0x00000000ab900000, 0x00000000ab800000| Complete 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%|HC|  |TAMS 0x00000000aba00000, 0x00000000ab900000| Complete 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%|HS|  |TAMS 0x00000000abb00000, 0x00000000aba00000| Complete 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000, 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%| O|  |TAMS 0x00000000abd00000, 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%| O|  |TAMS 0x00000000abe00000, 0x00000000abd00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%| O|  |TAMS 0x00000000abf00000, 0x00000000abe00000| Untracked 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|  |TAMS 0x00000000ac000000, 0x00000000abf00000| Untracked 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%|HS|  |TAMS 0x00000000ac100000, 0x00000000ac000000| Complete 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%|HC|  |TAMS 0x00000000ac200000, 0x00000000ac100000| Complete 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%|HC|  |TAMS 0x00000000ac300000, 0x00000000ac200000| Complete 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%|HC|  |TAMS 0x00000000ac400000, 0x00000000ac300000| Complete 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac500000, 0x00000000ac400000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%|HS|  |TAMS 0x00000000ac600000, 0x00000000ac500000| Complete 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac700000, 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac800000, 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac900000, 0x00000000ac800000| Untracked 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000aca00000, 0x00000000ac900000| Untracked 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000acb00000, 0x00000000aca00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acc00000, 0x00000000acb00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acd00000, 0x00000000acc00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000ace00000, 0x00000000acd00000| Untracked 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000ace00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000acf00000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000, 0x00000000ad000000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000, 0x00000000ad100000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000, 0x00000000ad200000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000, 0x00000000ad300000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000, 0x00000000ad400000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000, 0x00000000ad500000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad700000, 0x00000000ad600000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad800000, 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad900000, 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ada00000, 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000adb00000, 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%|HS|  |TAMS 0x00000000adc00000, 0x00000000adb00000| Complete 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%|HC|  |TAMS 0x00000000add00000, 0x00000000adc00000| Complete 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000ade00000, 0x00000000add00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000adf00000, 0x00000000ade00000| Untracked 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000ae000000, 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae100000, 0x00000000ae000000| Untracked 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae200000, 0x00000000ae100000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae300000, 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae400000, 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae500000, 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae600000, 0x00000000ae500000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae700000, 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%|HS|  |TAMS 0x00000000ae800000, 0x00000000ae700000| Complete 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%|HC|  |TAMS 0x00000000ae900000, 0x00000000ae800000| Complete 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000aea00000, 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aeb00000, 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aec00000, 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aed00000, 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aee00000, 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aef00000, 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000af000000, 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af100000, 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af200000, 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af300000, 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af400000, 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af500000, 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af600000, 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af700000, 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af800000, 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| O|  |TAMS 0x00000000af900000, 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| O|  |TAMS 0x00000000afa00000, 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| O|  |TAMS 0x00000000afb00000, 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afc00000, 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afd00000, 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afe00000, 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%| O|  |TAMS 0x00000000aff00000, 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%| O|  |TAMS 0x00000000b0000000, 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0100000, 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0200000, 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0300000, 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0400000, 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0400000, 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0500000, 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0600000, 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0700000, 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0800000, 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%| O|  |TAMS 0x00000000b0900000, 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%| O|  |TAMS 0x00000000b0a00000, 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%| O|  |TAMS 0x00000000b0b00000, 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%| O|  |TAMS 0x00000000b0c00000, 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|  |TAMS 0x00000000b0d00000, 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0e00000, 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b0f00000, 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1000000, 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1100000, 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1200000, 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1300000, 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1400000, 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1500000, 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1600000, 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1700000, 0x00000000b1700000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| O|  |TAMS 0x00000000b1800000, 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1a00000, 0x00000000b1a00000|100%| O|  |TAMS 0x00000000b1900000, 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|  |TAMS 0x00000000b1a00000, 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|  |TAMS 0x00000000b1b00000, 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|  |TAMS 0x00000000b1c00000, 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|  |TAMS 0x00000000b1d00000, 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|  |TAMS 0x00000000b1e00000, 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|  |TAMS 0x00000000b1f00000, 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|  |TAMS 0x00000000b2000000, 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|  |TAMS 0x00000000b2100000, 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|  |TAMS 0x00000000b2200000, 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|  |TAMS 0x00000000b2300000, 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|  |TAMS 0x00000000b2400000, 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|  |TAMS 0x00000000b2500000, 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|  |TAMS 0x00000000b2600000, 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|  |TAMS 0x00000000b2700000, 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2800000, 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2900000, 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2a00000, 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2b00000, 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| O|  |TAMS 0x00000000b2c00000, 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2d00000, 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| O|  |TAMS 0x00000000b2e00000, 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| O|  |TAMS 0x00000000b2f00000, 0x00000000b2f00000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3000000, 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3100000, 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3200000, 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|  |TAMS 0x00000000b3300000, 0x00000000b3300000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3400000, 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3500000, 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3600000, 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3700000, 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3800000, 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3900000, 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3a00000, 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|  |TAMS 0x00000000b3b00000, 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3d00000, 0x00000000b3d00000|100%| O|  |TAMS 0x00000000b3c00000, 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%| O|  |TAMS 0x00000000b3d00000, 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3e00000, 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b3f00000, 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4100000, 0x00000000b4100000|100%| O|  |TAMS 0x00000000b4000000, 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4200000, 0x00000000b4200000|100%| O|  |TAMS 0x00000000b4100000, 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4200000, 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|  |TAMS 0x00000000b4300000, 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4400000, 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4600000, 0x00000000b4600000|100%| O|  |TAMS 0x00000000b4500000, 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4600000, 0x00000000b4600000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4800000, 0x00000000b4800000|100%| O|  |TAMS 0x00000000b4700000, 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4800000, 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4a00000, 0x00000000b4a00000|100%| O|  |TAMS 0x00000000b4900000, 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%| O|  |TAMS 0x00000000b4a00000, 0x00000000b4a00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%| O|  |TAMS 0x00000000b4b00000, 0x00000000b4b00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4c00000, 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4e00000, 0x00000000b4e00000|100%| O|  |TAMS 0x00000000b4d00000, 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4f00000, 0x00000000b4f00000|100%| O|  |TAMS 0x00000000b4e00000, 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b4f00000, 0x00000000b4f00000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5100000, 0x00000000b5100000|100%| O|  |TAMS 0x00000000b5000000, 0x00000000b5000000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5100000, 0x00000000b5100000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5200000, 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%| O|  |TAMS 0x00000000b5300000, 0x00000000b5300000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%| O|  |TAMS 0x00000000b5400000, 0x00000000b5400000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5600000, 0x00000000b5600000|100%| O|  |TAMS 0x00000000b5500000, 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5600000, 0x00000000b5600000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5700000, 0x00000000b5700000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|  |TAMS 0x00000000b5800000, 0x00000000b5800000| Untracked 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|  |TAMS 0x00000000b5900000, 0x00000000b5900000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5b00000, 0x00000000b5b00000|100%| O|  |TAMS 0x00000000b5a00000, 0x00000000b5a00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|  |TAMS 0x00000000b5b00000, 0x00000000b5b00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|  |TAMS 0x00000000b5c00000, 0x00000000b5c00000| Untracked 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%| O|  |TAMS 0x00000000b5d00000, 0x00000000b5d00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%| O|  |TAMS 0x00000000b5e00000, 0x00000000b5e00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b5f00000, 0x00000000b5f00000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6100000, 0x00000000b6100000|100%| O|  |TAMS 0x00000000b6000000, 0x00000000b6000000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6200000, 0x00000000b6200000|100%| O|  |TAMS 0x00000000b6100000, 0x00000000b6100000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6300000, 0x00000000b6300000|100%| O|  |TAMS 0x00000000b6200000, 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6400000, 0x00000000b6400000|100%| O|  |TAMS 0x00000000b6300000, 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6400000, 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6600000, 0x00000000b6600000|100%| O|  |TAMS 0x00000000b6500000, 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6600000, 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6800000, 0x00000000b6800000|100%| O|  |TAMS 0x00000000b6700000, 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6900000, 0x00000000b6900000|100%| O|  |TAMS 0x00000000b6800000, 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6a00000, 0x00000000b6a00000|100%| O|  |TAMS 0x00000000b6900000, 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6b00000, 0x00000000b6b00000|100%| O|  |TAMS 0x00000000b6a00000, 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%| O|  |TAMS 0x00000000b6b00000, 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%| O|  |TAMS 0x00000000b6c00000, 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%| O|  |TAMS 0x00000000b6d00000, 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%| O|  |TAMS 0x00000000b6e00000, 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%| O|  |TAMS 0x00000000b6f00000, 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7100000, 0x00000000b7100000|100%| O|  |TAMS 0x00000000b7000000, 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|  |TAMS 0x00000000b7100000, 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7300000, 0x00000000b7300000|100%| O|  |TAMS 0x00000000b7200000, 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7400000, 0x00000000b7400000|100%| O|  |TAMS 0x00000000b7300000, 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7500000, 0x00000000b7500000|100%| O|  |TAMS 0x00000000b7400000, 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7600000, 0x00000000b7600000|100%| O|  |TAMS 0x00000000b7500000, 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7700000, 0x00000000b7700000|100%| O|  |TAMS 0x00000000b7600000, 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7800000, 0x00000000b7800000|100%| O|  |TAMS 0x00000000b7700000, 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7900000, 0x00000000b7900000|100%| O|  |TAMS 0x00000000b7800000, 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7a00000, 0x00000000b7a00000|100%| O|  |TAMS 0x00000000b7900000, 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7b00000, 0x00000000b7b00000|100%| O|  |TAMS 0x00000000b7a00000, 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7c00000, 0x00000000b7c00000|100%| O|  |TAMS 0x00000000b7b00000, 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7d00000, 0x00000000b7d00000|100%| O|  |TAMS 0x00000000b7c00000, 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7e00000, 0x00000000b7e00000|100%| O|  |TAMS 0x00000000b7d00000, 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7f00000, 0x00000000b7f00000|100%| O|  |TAMS 0x00000000b7e00000, 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b8000000, 0x00000000b8000000|100%| O|  |TAMS 0x00000000b7f00000, 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8100000, 0x00000000b8100000|100%| O|  |TAMS 0x00000000b8000000, 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8200000, 0x00000000b8200000|100%| O|  |TAMS 0x00000000b8100000, 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8300000, 0x00000000b8300000|100%| O|  |TAMS 0x00000000b8200000, 0x00000000b8200000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8400000, 0x00000000b8400000|100%| O|  |TAMS 0x00000000b8300000, 0x00000000b8300000| Untracked 
| 388|0x00000000b8400000, 0x00000000b8500000, 0x00000000b8500000|100%| O|  |TAMS 0x00000000b8400000, 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8600000, 0x00000000b8600000|100%| O|  |TAMS 0x00000000b8500000, 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8700000, 0x00000000b8700000|100%| O|  |TAMS 0x00000000b8600000, 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8800000, 0x00000000b8800000|100%| O|  |TAMS 0x00000000b8700000, 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8840200, 0x00000000b8900000| 25%| O|  |TAMS 0x00000000b8800000, 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000, 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000, 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000, 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000, 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000, 0x00000000b8d00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000, 0x00000000b8e00000| Untracked 
| 399|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000, 0x00000000b8f00000| Untracked 
| 400|0x00000000b9000000, 0x00000000b9000000, 0x00000000b9100000|  0%| F|  |TAMS 0x00000000b9000000, 0x00000000b9000000| Untracked 
| 401|0x00000000b9100000, 0x00000000b9100000, 0x00000000b9200000|  0%| F|  |TAMS 0x00000000b9100000, 0x00000000b9100000| Untracked 
| 402|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000, 0x00000000b9200000| Untracked 
| 403|0x00000000b9300000, 0x00000000b9300000, 0x00000000b9400000|  0%| F|  |TAMS 0x00000000b9300000, 0x00000000b9300000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000, 0x00000000b9400000| Untracked 
| 405|0x00000000b9500000, 0x00000000b9500000, 0x00000000b9600000|  0%| F|  |TAMS 0x00000000b9500000, 0x00000000b9500000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9600000, 0x00000000b9700000|  0%| F|  |TAMS 0x00000000b9600000, 0x00000000b9600000| Untracked 
| 407|0x00000000b9700000, 0x00000000b9700000, 0x00000000b9800000|  0%| F|  |TAMS 0x00000000b9700000, 0x00000000b9700000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9800000, 0x00000000b9900000|  0%| F|  |TAMS 0x00000000b9800000, 0x00000000b9800000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9900000, 0x00000000b9a00000|  0%| F|  |TAMS 0x00000000b9900000, 0x00000000b9900000| Untracked 
| 410|0x00000000b9a00000, 0x00000000b9a00000, 0x00000000b9b00000|  0%| F|  |TAMS 0x00000000b9a00000, 0x00000000b9a00000| Untracked 
| 411|0x00000000b9b00000, 0x00000000b9b00000, 0x00000000b9c00000|  0%| F|  |TAMS 0x00000000b9b00000, 0x00000000b9b00000| Untracked 
| 412|0x00000000b9c00000, 0x00000000b9c00000, 0x00000000b9d00000|  0%| F|  |TAMS 0x00000000b9c00000, 0x00000000b9c00000| Untracked 
| 413|0x00000000b9d00000, 0x00000000b9d00000, 0x00000000b9e00000|  0%| F|  |TAMS 0x00000000b9d00000, 0x00000000b9d00000| Untracked 
| 414|0x00000000b9e00000, 0x00000000b9e00000, 0x00000000b9f00000|  0%| F|  |TAMS 0x00000000b9e00000, 0x00000000b9e00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000b9f00000, 0x00000000ba000000|  0%| F|  |TAMS 0x00000000b9f00000, 0x00000000b9f00000| Untracked 
| 416|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000, 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000, 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000, 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba300000, 0x00000000ba400000|  0%| F|  |TAMS 0x00000000ba300000, 0x00000000ba300000| Untracked 
| 420|0x00000000ba400000, 0x00000000ba400000, 0x00000000ba500000|  0%| F|  |TAMS 0x00000000ba400000, 0x00000000ba400000| Untracked 
| 421|0x00000000ba500000, 0x00000000ba500000, 0x00000000ba600000|  0%| F|  |TAMS 0x00000000ba500000, 0x00000000ba500000| Untracked 
| 422|0x00000000ba600000, 0x00000000ba600000, 0x00000000ba700000|  0%| F|  |TAMS 0x00000000ba600000, 0x00000000ba600000| Untracked 
| 423|0x00000000ba700000, 0x00000000ba700000, 0x00000000ba800000|  0%| F|  |TAMS 0x00000000ba700000, 0x00000000ba700000| Untracked 
| 424|0x00000000ba800000, 0x00000000ba800000, 0x00000000ba900000|  0%| F|  |TAMS 0x00000000ba800000, 0x00000000ba800000| Untracked 
| 425|0x00000000ba900000, 0x00000000ba900000, 0x00000000baa00000|  0%| F|  |TAMS 0x00000000ba900000, 0x00000000ba900000| Untracked 
| 426|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000, 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bab00000, 0x00000000bac00000|  0%| F|  |TAMS 0x00000000bab00000, 0x00000000bab00000| Untracked 
| 428|0x00000000bac00000, 0x00000000bac00000, 0x00000000bad00000|  0%| F|  |TAMS 0x00000000bac00000, 0x00000000bac00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bad00000, 0x00000000bae00000|  0%| F|  |TAMS 0x00000000bad00000, 0x00000000bad00000| Untracked 
| 430|0x00000000bae00000, 0x00000000bae00000, 0x00000000baf00000|  0%| F|  |TAMS 0x00000000bae00000, 0x00000000bae00000| Untracked 
| 431|0x00000000baf00000, 0x00000000baf00000, 0x00000000bb000000|  0%| F|  |TAMS 0x00000000baf00000, 0x00000000baf00000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb000000, 0x00000000bb100000|  0%| F|  |TAMS 0x00000000bb000000, 0x00000000bb000000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb100000, 0x00000000bb200000|  0%| F|  |TAMS 0x00000000bb100000, 0x00000000bb100000| Untracked 
| 434|0x00000000bb200000, 0x00000000bb200000, 0x00000000bb300000|  0%| F|  |TAMS 0x00000000bb200000, 0x00000000bb200000| Untracked 
| 435|0x00000000bb300000, 0x00000000bb300000, 0x00000000bb400000|  0%| F|  |TAMS 0x00000000bb300000, 0x00000000bb300000| Untracked 
| 436|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000, 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000, 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000, 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb700000, 0x00000000bb800000|  0%| F|  |TAMS 0x00000000bb700000, 0x00000000bb700000| Untracked 
| 440|0x00000000bb800000, 0x00000000bb800000, 0x00000000bb900000|  0%| F|  |TAMS 0x00000000bb800000, 0x00000000bb800000| Untracked 
| 441|0x00000000bb900000, 0x00000000bb900000, 0x00000000bba00000|  0%| F|  |TAMS 0x00000000bb900000, 0x00000000bb900000| Untracked 
| 442|0x00000000bba00000, 0x00000000bba00000, 0x00000000bbb00000|  0%| F|  |TAMS 0x00000000bba00000, 0x00000000bba00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000, 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000, 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbd00000, 0x00000000bbe00000|  0%| F|  |TAMS 0x00000000bbd00000, 0x00000000bbd00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbe00000, 0x00000000bbf00000|  0%| F|  |TAMS 0x00000000bbe00000, 0x00000000bbe00000| Untracked 
| 447|0x00000000bbf00000, 0x00000000bbf00000, 0x00000000bc000000|  0%| F|  |TAMS 0x00000000bbf00000, 0x00000000bbf00000| Untracked 
| 448|0x00000000bc000000, 0x00000000bc000000, 0x00000000bc100000|  0%| F|  |TAMS 0x00000000bc000000, 0x00000000bc000000| Untracked 
| 449|0x00000000bc100000, 0x00000000bc100000, 0x00000000bc200000|  0%| F|  |TAMS 0x00000000bc100000, 0x00000000bc100000| Untracked 
| 450|0x00000000bc200000, 0x00000000bc200000, 0x00000000bc300000|  0%| F|  |TAMS 0x00000000bc200000, 0x00000000bc200000| Untracked 
| 451|0x00000000bc300000, 0x00000000bc300000, 0x00000000bc400000|  0%| F|  |TAMS 0x00000000bc300000, 0x00000000bc300000| Untracked 
| 452|0x00000000bc400000, 0x00000000bc400000, 0x00000000bc500000|  0%| F|  |TAMS 0x00000000bc400000, 0x00000000bc400000| Untracked 
| 453|0x00000000bc500000, 0x00000000bc500000, 0x00000000bc600000|  0%| F|  |TAMS 0x00000000bc500000, 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc600000, 0x00000000bc700000|  0%| F|  |TAMS 0x00000000bc600000, 0x00000000bc600000| Untracked 
| 455|0x00000000bc700000, 0x00000000bc700000, 0x00000000bc800000|  0%| F|  |TAMS 0x00000000bc700000, 0x00000000bc700000| Untracked 
| 456|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000, 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000, 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000, 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000, 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000, 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000, 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000, 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000, 0x00000000bcf00000| Untracked 
| 464|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000, 0x00000000bd000000| Untracked 
| 465|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000, 0x00000000bd100000| Untracked 
| 466|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000, 0x00000000bd200000| Untracked 
| 467|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000, 0x00000000bd300000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000, 0x00000000bd400000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000, 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000, 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000, 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000, 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000, 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000, 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000, 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000, 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000, 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000, 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000, 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000, 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000, 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000, 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000, 0x00000000be300000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000, 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000, 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000, 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000, 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000, 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000, 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000, 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000, 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000, 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000, 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000, 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000, 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000, 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000, 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000, 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000, 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000, 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000, 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000, 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf700000, 0x00000000bf800000|  0%| F|  |TAMS 0x00000000bf700000, 0x00000000bf700000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000, 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bf900000, 0x00000000bfa00000|  0%| F|  |TAMS 0x00000000bf900000, 0x00000000bf900000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfa00000, 0x00000000bfb00000|  0%| F|  |TAMS 0x00000000bfa00000, 0x00000000bfa00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfb00000, 0x00000000bfc00000|  0%| F|  |TAMS 0x00000000bfb00000, 0x00000000bfb00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfc00000, 0x00000000bfd00000|  0%| F|  |TAMS 0x00000000bfc00000, 0x00000000bfc00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfd00000, 0x00000000bfe00000|  0%| F|  |TAMS 0x00000000bfd00000, 0x00000000bfd00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bfe00000, 0x00000000bff00000|  0%| F|  |TAMS 0x00000000bfe00000, 0x00000000bfe00000| Untracked 
| 511|0x00000000bff00000, 0x00000000bff00000, 0x00000000c0000000|  0%| F|  |TAMS 0x00000000bff00000, 0x00000000bff00000| Untracked 
| 512|0x00000000c0000000, 0x00000000c0000000, 0x00000000c0100000|  0%| F|  |TAMS 0x00000000c0000000, 0x00000000c0000000| Untracked 
| 513|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000, 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0200000, 0x00000000c0300000|  0%| F|  |TAMS 0x00000000c0200000, 0x00000000c0200000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0400000, 0x00000000c0400000|100%| S|CS|TAMS 0x00000000c0300000, 0x00000000c0300000| Complete 
| 516|0x00000000c0400000, 0x00000000c0500000, 0x00000000c0500000|100%| S|CS|TAMS 0x00000000c0400000, 0x00000000c0400000| Complete 
| 517|0x00000000c0500000, 0x00000000c0600000, 0x00000000c0600000|100%| S|CS|TAMS 0x00000000c0500000, 0x00000000c0500000| Complete 
| 518|0x00000000c0600000, 0x00000000c0700000, 0x00000000c0700000|100%| S|CS|TAMS 0x00000000c0600000, 0x00000000c0600000| Complete 
| 519|0x00000000c0700000, 0x00000000c0800000, 0x00000000c0800000|100%| S|CS|TAMS 0x00000000c0700000, 0x00000000c0700000| Complete 
| 520|0x00000000c0800000, 0x00000000c0900000, 0x00000000c0900000|100%| S|CS|TAMS 0x00000000c0800000, 0x00000000c0800000| Complete 
| 521|0x00000000c0900000, 0x00000000c0a00000, 0x00000000c0a00000|100%| S|CS|TAMS 0x00000000c0900000, 0x00000000c0900000| Complete 
| 522|0x00000000c0a00000, 0x00000000c0b00000, 0x00000000c0b00000|100%| S|CS|TAMS 0x00000000c0a00000, 0x00000000c0a00000| Complete 
| 523|0x00000000c0b00000, 0x00000000c0c00000, 0x00000000c0c00000|100%| S|CS|TAMS 0x00000000c0b00000, 0x00000000c0b00000| Complete 
| 524|0x00000000c0c00000, 0x00000000c0c00000, 0x00000000c0d00000|  0%| F|  |TAMS 0x00000000c0c00000, 0x00000000c0c00000| Untracked 
| 525|0x00000000c0d00000, 0x00000000c0d00000, 0x00000000c0e00000|  0%| F|  |TAMS 0x00000000c0d00000, 0x00000000c0d00000| Untracked 
| 526|0x00000000c0e00000, 0x00000000c0e00000, 0x00000000c0f00000|  0%| F|  |TAMS 0x00000000c0e00000, 0x00000000c0e00000| Untracked 
| 527|0x00000000c0f00000, 0x00000000c0f00000, 0x00000000c1000000|  0%| F|  |TAMS 0x00000000c0f00000, 0x00000000c0f00000| Untracked 
| 528|0x00000000c1000000, 0x00000000c1000000, 0x00000000c1100000|  0%| F|  |TAMS 0x00000000c1000000, 0x00000000c1000000| Untracked 
| 529|0x00000000c1100000, 0x00000000c1100000, 0x00000000c1200000|  0%| F|  |TAMS 0x00000000c1100000, 0x00000000c1100000| Untracked 
| 530|0x00000000c1200000, 0x00000000c1200000, 0x00000000c1300000|  0%| F|  |TAMS 0x00000000c1200000, 0x00000000c1200000| Untracked 
| 531|0x00000000c1300000, 0x00000000c1300000, 0x00000000c1400000|  0%| F|  |TAMS 0x00000000c1300000, 0x00000000c1300000| Untracked 
| 532|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000, 0x00000000c1400000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000, 0x00000000c1500000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000, 0x00000000c1600000| Untracked 
| 535|0x00000000c1700000, 0x00000000c1700000, 0x00000000c1800000|  0%| F|  |TAMS 0x00000000c1700000, 0x00000000c1700000| Untracked 
| 536|0x00000000c1800000, 0x00000000c1800000, 0x00000000c1900000|  0%| F|  |TAMS 0x00000000c1800000, 0x00000000c1800000| Untracked 
| 537|0x00000000c1900000, 0x00000000c1900000, 0x00000000c1a00000|  0%| F|  |TAMS 0x00000000c1900000, 0x00000000c1900000| Untracked 
| 538|0x00000000c1a00000, 0x00000000c1a00000, 0x00000000c1b00000|  0%| F|  |TAMS 0x00000000c1a00000, 0x00000000c1a00000| Untracked 
| 539|0x00000000c1b00000, 0x00000000c1b00000, 0x00000000c1c00000|  0%| F|  |TAMS 0x00000000c1b00000, 0x00000000c1b00000| Untracked 
| 540|0x00000000c1c00000, 0x00000000c1c00000, 0x00000000c1d00000|  0%| F|  |TAMS 0x00000000c1c00000, 0x00000000c1c00000| Untracked 
| 541|0x00000000c1d00000, 0x00000000c1d00000, 0x00000000c1e00000|  0%| F|  |TAMS 0x00000000c1d00000, 0x00000000c1d00000| Untracked 
| 542|0x00000000c1e00000, 0x00000000c1e00000, 0x00000000c1f00000|  0%| F|  |TAMS 0x00000000c1e00000, 0x00000000c1e00000| Untracked 
| 543|0x00000000c1f00000, 0x00000000c1f00000, 0x00000000c2000000|  0%| F|  |TAMS 0x00000000c1f00000, 0x00000000c1f00000| Untracked 
| 544|0x00000000c2000000, 0x00000000c2000000, 0x00000000c2100000|  0%| F|  |TAMS 0x00000000c2000000, 0x00000000c2000000| Untracked 
| 545|0x00000000c2100000, 0x00000000c2100000, 0x00000000c2200000|  0%| F|  |TAMS 0x00000000c2100000, 0x00000000c2100000| Untracked 
| 546|0x00000000c2200000, 0x00000000c2200000, 0x00000000c2300000|  0%| F|  |TAMS 0x00000000c2200000, 0x00000000c2200000| Untracked 
| 547|0x00000000c2300000, 0x00000000c2300000, 0x00000000c2400000|  0%| F|  |TAMS 0x00000000c2300000, 0x00000000c2300000| Untracked 
| 548|0x00000000c2400000, 0x00000000c2400000, 0x00000000c2500000|  0%| F|  |TAMS 0x00000000c2400000, 0x00000000c2400000| Untracked 
| 549|0x00000000c2500000, 0x00000000c2500000, 0x00000000c2600000|  0%| F|  |TAMS 0x00000000c2500000, 0x00000000c2500000| Untracked 
| 550|0x00000000c2600000, 0x00000000c2600000, 0x00000000c2700000|  0%| F|  |TAMS 0x00000000c2600000, 0x00000000c2600000| Untracked 
| 551|0x00000000c2700000, 0x00000000c2700000, 0x00000000c2800000|  0%| F|  |TAMS 0x00000000c2700000, 0x00000000c2700000| Untracked 
| 552|0x00000000c2800000, 0x00000000c2800000, 0x00000000c2900000|  0%| F|  |TAMS 0x00000000c2800000, 0x00000000c2800000| Untracked 
| 553|0x00000000c2900000, 0x00000000c2900000, 0x00000000c2a00000|  0%| F|  |TAMS 0x00000000c2900000, 0x00000000c2900000| Untracked 
| 554|0x00000000c2a00000, 0x00000000c2a00000, 0x00000000c2b00000|  0%| F|  |TAMS 0x00000000c2a00000, 0x00000000c2a00000| Untracked 
| 555|0x00000000c2b00000, 0x00000000c2b00000, 0x00000000c2c00000|  0%| F|  |TAMS 0x00000000c2b00000, 0x00000000c2b00000| Untracked 
| 556|0x00000000c2c00000, 0x00000000c2c00000, 0x00000000c2d00000|  0%| F|  |TAMS 0x00000000c2c00000, 0x00000000c2c00000| Untracked 
| 557|0x00000000c2d00000, 0x00000000c2d00000, 0x00000000c2e00000|  0%| F|  |TAMS 0x00000000c2d00000, 0x00000000c2d00000| Untracked 
| 558|0x00000000c2e00000, 0x00000000c2e00000, 0x00000000c2f00000|  0%| F|  |TAMS 0x00000000c2e00000, 0x00000000c2e00000| Untracked 
| 559|0x00000000c2f00000, 0x00000000c2f00000, 0x00000000c3000000|  0%| F|  |TAMS 0x00000000c2f00000, 0x00000000c2f00000| Untracked 
| 560|0x00000000c3000000, 0x00000000c3000000, 0x00000000c3100000|  0%| F|  |TAMS 0x00000000c3000000, 0x00000000c3000000| Untracked 
| 561|0x00000000c3100000, 0x00000000c3100000, 0x00000000c3200000|  0%| F|  |TAMS 0x00000000c3100000, 0x00000000c3100000| Untracked 
| 562|0x00000000c3200000, 0x00000000c3200000, 0x00000000c3300000|  0%| F|  |TAMS 0x00000000c3200000, 0x00000000c3200000| Untracked 
| 563|0x00000000c3300000, 0x00000000c3300000, 0x00000000c3400000|  0%| F|  |TAMS 0x00000000c3300000, 0x00000000c3300000| Untracked 
| 564|0x00000000c3400000, 0x00000000c3400000, 0x00000000c3500000|  0%| F|  |TAMS 0x00000000c3400000, 0x00000000c3400000| Untracked 
| 565|0x00000000c3500000, 0x00000000c3500000, 0x00000000c3600000|  0%| F|  |TAMS 0x00000000c3500000, 0x00000000c3500000| Untracked 
| 566|0x00000000c3600000, 0x00000000c3600000, 0x00000000c3700000|  0%| F|  |TAMS 0x00000000c3600000, 0x00000000c3600000| Untracked 
| 567|0x00000000c3700000, 0x00000000c3700000, 0x00000000c3800000|  0%| F|  |TAMS 0x00000000c3700000, 0x00000000c3700000| Untracked 
| 568|0x00000000c3800000, 0x00000000c3800000, 0x00000000c3900000|  0%| F|  |TAMS 0x00000000c3800000, 0x00000000c3800000| Untracked 
| 569|0x00000000c3900000, 0x00000000c3900000, 0x00000000c3a00000|  0%| F|  |TAMS 0x00000000c3900000, 0x00000000c3900000| Untracked 
| 570|0x00000000c3a00000, 0x00000000c3a00000, 0x00000000c3b00000|  0%| F|  |TAMS 0x00000000c3a00000, 0x00000000c3a00000| Untracked 
| 571|0x00000000c3b00000, 0x00000000c3b00000, 0x00000000c3c00000|  0%| F|  |TAMS 0x00000000c3b00000, 0x00000000c3b00000| Untracked 
| 572|0x00000000c3c00000, 0x00000000c3c00000, 0x00000000c3d00000|  0%| F|  |TAMS 0x00000000c3c00000, 0x00000000c3c00000| Untracked 
| 573|0x00000000c3d00000, 0x00000000c3d00000, 0x00000000c3e00000|  0%| F|  |TAMS 0x00000000c3d00000, 0x00000000c3d00000| Untracked 
| 574|0x00000000c3e00000, 0x00000000c3e00000, 0x00000000c3f00000|  0%| F|  |TAMS 0x00000000c3e00000, 0x00000000c3e00000| Untracked 
| 575|0x00000000c3f00000, 0x00000000c3f00000, 0x00000000c4000000|  0%| F|  |TAMS 0x00000000c3f00000, 0x00000000c3f00000| Untracked 
| 576|0x00000000c4000000, 0x00000000c4000000, 0x00000000c4100000|  0%| F|  |TAMS 0x00000000c4000000, 0x00000000c4000000| Untracked 
| 577|0x00000000c4100000, 0x00000000c4100000, 0x00000000c4200000|  0%| F|  |TAMS 0x00000000c4100000, 0x00000000c4100000| Untracked 
| 578|0x00000000c4200000, 0x00000000c4200000, 0x00000000c4300000|  0%| F|  |TAMS 0x00000000c4200000, 0x00000000c4200000| Untracked 

Card table byte_map: [0x000001f873320000,0x000001f873620000] _byte_map_base: 0x000001f872e20000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001f8605493b0, (CMBitMap*) 0x000001f860549378
 Prev Bits: [0x000001f875120000, 0x000001f876920000)
 Next Bits: [0x000001f873920000, 0x000001f875120000)

Polling page: 0x000001f85fcf0000

Metaspace:

Usage:
  Non-class:    105.34 MB capacity,   103.63 MB ( 98%) used,     1.35 MB (  1%) free+waste,   367.75 KB ( <1%) overhead. 
      Class:     16.05 MB capacity,    15.00 MB ( 93%) used,   913.51 KB (  6%) free+waste,   161.06 KB ( <1%) overhead. 
       Both:    121.39 MB capacity,   118.63 MB ( 98%) used,     2.25 MB (  2%) free+waste,   528.81 KB ( <1%) overhead. 

Virtual space:
  Non-class space:      106.00 MB reserved,     105.42 MB (>99%) committed 
      Class space:        1.00 GB reserved,      16.13 MB (  2%) committed 
             Both:        1.10 GB reserved,     121.54 MB ( 11%) committed 

Chunk freelists:
   Non-Class:  16.00 KB
       Class:  0 bytes
        Both:  16.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 197.57 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=9140Kb max_used=9140Kb free=110923Kb
 bounds [0x000001f86bae0000, 0x000001f86c3d0000, 0x000001f873020000]
CodeHeap 'profiled nmethods': size=120000Kb used=36685Kb max_used=36685Kb free=83314Kb
 bounds [0x000001f8645b0000, 0x000001f866990000, 0x000001f86bae0000]
CodeHeap 'non-nmethods': size=5696Kb used=2442Kb max_used=2492Kb free=3254Kb
 bounds [0x000001f864020000, 0x000001f864290000, 0x000001f8645b0000]
 total_blobs=18592 nmethods=17628 adapters=875
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 41.737 Thread 0x000001f878e29800 18080       2       com.android.tools.r8.internal.hj::a (26 bytes)
Event: 41.738 Thread 0x000001f878e29800 nmethod 18080 0x000001f866980290 code [0x000001f866980460, 0x000001f8669805d8]
Event: 41.738 Thread 0x000001f878e29800 18081       2       com.android.tools.r8.graph.n1::y0 (4 bytes)
Event: 41.738 Thread 0x000001f878e29800 nmethod 18081 0x000001f866980710 code [0x000001f8669808c0, 0x000001f8669809b8]
Event: 41.738 Thread 0x000001f878e29800 18082       1       com.android.tools.r8.internal.jj::a (5 bytes)
Event: 41.738 Thread 0x000001f878e29800 nmethod 18082 0x000001f86c3cce10 code [0x000001f86c3ccfc0, 0x000001f86c3cd058]
Event: 41.738 Thread 0x000001f878e29800 18083       2       com.android.tools.r8.graph.e0::g (6 bytes)
Event: 41.739 Thread 0x000001f878e29800 nmethod 18083 0x000001f866980a90 code [0x000001f866980c40, 0x000001f866980d48]
Event: 41.739 Thread 0x000001f878e29800 18084       2       com.android.tools.r8.internal.GI::a (45 bytes)
Event: 41.739 Thread 0x000001f878e29800 nmethod 18084 0x000001f866980e10 code [0x000001f866980fc0, 0x000001f866981108]
Event: 41.739 Thread 0x000001f878e29800 18085       2       com.android.tools.r8.graph.e0::n (6 bytes)
Event: 41.739 Thread 0x000001f878e29800 nmethod 18085 0x000001f866981190 code [0x000001f866981340, 0x000001f866981438]
Event: 41.739 Thread 0x000001f878e29800 18086       2       com.android.tools.r8.graph.e0$$Lambda$1568/0x000000010100ac40::a (5 bytes)
Event: 41.739 Thread 0x000001f878e29800 nmethod 18086 0x000001f866981510 code [0x000001f866981720, 0x000001f866981b48]
Event: 41.740 Thread 0x000001f878e29800 18087       2       com.android.tools.r8.internal.iG::a (269 bytes)
Event: 41.740 Thread 0x000001f878e29800 nmethod 18087 0x000001f866981d90 code [0x000001f866981fe0, 0x000001f866982628]
Event: 41.740 Thread 0x000001f878e29800 18088       2       com.android.tools.r8.internal.zK::a (48 bytes)
Event: 41.741 Thread 0x000001f878e29800 nmethod 18088 0x000001f866982a10 code [0x000001f866982c00, 0x000001f866982e18]
Event: 41.741 Thread 0x000001f878e29800 18089       2       com.android.tools.r8.internal.KO::containsKey (74 bytes)
Event: 41.741 Thread 0x000001f878e29800 nmethod 18089 0x000001f866982f90 code [0x000001f866983160, 0x000001f866983408]

GC Heap History (20 events):
Event: 37.511 GC heap after
{Heap after GC invocations=61 (full 0):
 garbage-first heap   total 279552K, used 216318K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 116928K, capacity 119473K, committed 119724K, reserved 1153024K
  class space    used 14819K, capacity 15774K, committed 15872K, reserved 1048576K
}
Event: 38.097 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 279552K, used 230654K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 3 survivors (3072K)
 Metaspace       used 117304K, capacity 119847K, committed 119980K, reserved 1153024K
  class space    used 14848K, capacity 15792K, committed 15872K, reserved 1048576K
}
Event: 38.127 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 279552K, used 222884K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 117304K, capacity 119847K, committed 119980K, reserved 1153024K
  class space    used 14848K, capacity 15792K, committed 15872K, reserved 1048576K
}
Event: 38.175 GC heap before
{Heap before GC invocations=63 (full 0):
 garbage-first heap   total 279552K, used 234148K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 3 survivors (3072K)
 Metaspace       used 117329K, capacity 119847K, committed 119980K, reserved 1153024K
  class space    used 14848K, capacity 15792K, committed 15872K, reserved 1048576K
}
Event: 38.207 GC heap after
{Heap after GC invocations=64 (full 0):
 garbage-first heap   total 331776K, used 228318K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 117329K, capacity 119847K, committed 119980K, reserved 1153024K
  class space    used 14848K, capacity 15792K, committed 15872K, reserved 1048576K
}
Event: 38.325 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 331776K, used 244702K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 2 survivors (2048K)
 Metaspace       used 117440K, capacity 120043K, committed 120236K, reserved 1153024K
  class space    used 14851K, capacity 15829K, committed 15872K, reserved 1048576K
}
Event: 38.358 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 331776K, used 237126K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 117440K, capacity 120043K, committed 120236K, reserved 1153024K
  class space    used 14851K, capacity 15829K, committed 15872K, reserved 1048576K
}
Event: 38.491 GC heap before
{Heap before GC invocations=65 (full 0):
 garbage-first heap   total 331776K, used 263750K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 2 survivors (2048K)
 Metaspace       used 117492K, capacity 120048K, committed 120236K, reserved 1153024K
  class space    used 14854K, capacity 15830K, committed 15872K, reserved 1048576K
}
Event: 38.537 GC heap after
{Heap after GC invocations=66 (full 0):
 garbage-first heap   total 331776K, used 249816K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 117492K, capacity 120048K, committed 120236K, reserved 1153024K
  class space    used 14854K, capacity 15830K, committed 15872K, reserved 1048576K
}
Event: 38.671 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 331776K, used 270296K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 4 survivors (4096K)
 Metaspace       used 118134K, capacity 120727K, committed 120876K, reserved 1155072K
  class space    used 14950K, capacity 15905K, committed 16000K, reserved 1048576K
}
Event: 38.715 GC heap after
{Heap after GC invocations=67 (full 0):
 garbage-first heap   total 331776K, used 258753K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 118134K, capacity 120727K, committed 120876K, reserved 1155072K
  class space    used 14950K, capacity 15905K, committed 16000K, reserved 1048576K
}
Event: 38.795 GC heap before
{Heap before GC invocations=67 (full 0):
 garbage-first heap   total 331776K, used 276161K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 3 survivors (3072K)
 Metaspace       used 118185K, capacity 120792K, committed 121132K, reserved 1155072K
  class space    used 14953K, capacity 15905K, committed 16000K, reserved 1048576K
}
Event: 38.828 GC heap after
{Heap after GC invocations=68 (full 0):
 garbage-first heap   total 592896K, used 269312K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 118185K, capacity 120792K, committed 121132K, reserved 1155072K
  class space    used 14953K, capacity 15905K, committed 16000K, reserved 1048576K
}
Event: 40.017 GC heap before
{Heap before GC invocations=69 (full 0):
 garbage-first heap   total 592896K, used 353280K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 89 young (91136K), 3 survivors (3072K)
 Metaspace       used 119442K, capacity 122043K, committed 122284K, reserved 1155072K
  class space    used 15106K, capacity 16084K, committed 16128K, reserved 1048576K
}
Event: 40.106 GC heap after
{Heap after GC invocations=70 (full 0):
 garbage-first heap   total 592896K, used 312211K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 119442K, capacity 122043K, committed 122284K, reserved 1155072K
  class space    used 15106K, capacity 16084K, committed 16128K, reserved 1048576K
}
Event: 40.711 GC heap before
{Heap before GC invocations=70 (full 0):
 garbage-first heap   total 592896K, used 390035K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 88 young (90112K), 12 survivors (12288K)
 Metaspace       used 119655K, capacity 122234K, committed 122284K, reserved 1155072K
  class space    used 15132K, capacity 16101K, committed 16128K, reserved 1048576K
}
Event: 40.811 GC heap after
{Heap after GC invocations=71 (full 0):
 garbage-first heap   total 592896K, used 353792K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 119655K, capacity 122234K, committed 122284K, reserved 1155072K
  class space    used 15132K, capacity 16101K, committed 16128K, reserved 1048576K
}
Event: 41.111 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total 592896K, used 420352K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 77 young (78848K), 11 survivors (11264K)
 Metaspace       used 120155K, capacity 122835K, committed 122924K, reserved 1157120K
  class space    used 15199K, capacity 16194K, committed 16256K, reserved 1048576K
}
Event: 41.219 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total 592896K, used 390656K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 120155K, capacity 122835K, committed 122924K, reserved 1157120K
  class space    used 15199K, capacity 16194K, committed 16256K, reserved 1048576K
}
Event: 41.743 GC heap before
{Heap before GC invocations=72 (full 0):
 garbage-first heap   total 592896K, used 445952K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 65 young (66560K), 10 survivors (10240K)
 Metaspace       used 121472K, capacity 124302K, committed 124460K, reserved 1157120K
  class space    used 15359K, capacity 16434K, committed 16512K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 41.473 Thread 0x000001f801cae800 DEOPT PACKING pc=0x000001f8668e4bf9 sp=0x000000cf092fce70
Event: 41.473 Thread 0x000001f801cae800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf092fc328 mode 0
Event: 41.490 Thread 0x000001f801cb1000 DEOPT PACKING pc=0x000001f8668d5006 sp=0x000000cf099ff020
Event: 41.490 Thread 0x000001f801cb1000 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf099fe5e0 mode 0
Event: 41.542 Thread 0x000001f801cae800 DEOPT PACKING pc=0x000001f8669050bc sp=0x000000cf092fd4a0
Event: 41.542 Thread 0x000001f801cae800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf092fc9b0 mode 0
Event: 41.617 Thread 0x000001f801cb2000 DEOPT PACKING pc=0x000001f86680a9b8 sp=0x000000cf097fec60
Event: 41.617 Thread 0x000001f801cb2000 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf097fe138 mode 0
Event: 41.675 Thread 0x000001f801cae800 DEOPT PACKING pc=0x000001f866959977 sp=0x000000cf092fcc80
Event: 41.675 Thread 0x000001f801cae800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf092fc168 mode 0
Event: 41.679 Thread 0x000001f801cae800 DEOPT PACKING pc=0x000001f86695bd00 sp=0x000000cf092fcc70
Event: 41.679 Thread 0x000001f801cae800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf092fc160 mode 0
Event: 41.708 Thread 0x000001f801cb2000 DEOPT PACKING pc=0x000001f866826aa9 sp=0x000000cf097fec70
Event: 41.708 Thread 0x000001f801cb2000 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf097fe118 mode 0
Event: 41.709 Thread 0x000001f801cab800 DEOPT PACKING pc=0x000001f86573c7eb sp=0x000000cf08cfd840
Event: 41.709 Thread 0x000001f801cab800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf08cfcd18 mode 0
Event: 41.714 Thread 0x000001f801cb1000 DEOPT PACKING pc=0x000001f8668db295 sp=0x000000cf099fefc0
Event: 41.714 Thread 0x000001f801cb1000 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf099fe458 mode 0
Event: 41.737 Thread 0x000001f801cab800 DEOPT PACKING pc=0x000001f86692d1a2 sp=0x000000cf08cfd990
Event: 41.737 Thread 0x000001f801cab800 DEOPT UNPACKING pc=0x000001f86406a95e sp=0x000000cf08cfce48 mode 0

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 35.821 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab627b90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000ab627b90) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 35.821 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab62df10}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(long, java.lang.Object)'> (0x00000000ab62df10) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 35.824 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab684690}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000ab684690) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 35.825 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab68a828}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, long)'> (0x00000000ab68a828) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 35.825 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab6dbf38}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object)'> (0x00000000ab6dbf38) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 35.911 Thread 0x000001f801cab800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ab048480}> (0x00000000ab048480) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 36.195 Thread 0x000001f801cae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa126f48}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0
Event: 37.194 Thread 0x000001f879fdf800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000b0d8f5d8}> (0x00000000b0d8f5d8) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 37.343 Thread 0x000001f801cae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afe259b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int)'> (0x00000000afe259b0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 37.390 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000afdde768}: Found class java.lang.Object, but interface was expected> (0x00000000afdde768) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 37.401 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000afc8cdd8}: Found class java.lang.Object, but interface was expected> (0x00000000afc8cdd8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 37.402 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000afc96cb0}: Found class java.lang.Object, but interface was expected> (0x00000000afc96cb0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 37.751 Thread 0x000001f801caa800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b02547d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000b02547d0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 39.049 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000c2e95aa8}: Found class java.lang.Object, but interface was expected> (0x00000000c2e95aa8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 40.813 Thread 0x000001f801cae800 Implicit null exception at 0x000001f86c140a38 to 0x000001f86c145ffc
Event: 41.075 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000c0aaf130}: Found class java.lang.Object, but interface was expected> (0x00000000c0aaf130) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 41.085 Thread 0x000001f801cae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c09ddca0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000c09ddca0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 41.295 Thread 0x000001f801cae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c2f89540}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000c2f89540) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 41.385 Thread 0x000001f801cae800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c2399210}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000c2399210) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 41.606 Thread 0x000001f801cae800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000c1404b78}: Found class java.lang.Object, but interface was expected> (0x00000000c1404b78) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]

Events (20 events):
Event: 41.729 loading class com/android/tools/r8/graph/e0 done
Event: 41.729 loading class com/android/tools/r8/graph/e0
Event: 41.729 loading class com/android/tools/r8/graph/e0 done
Event: 41.729 loading class com/android/tools/r8/internal/jV
Event: 41.729 loading class com/android/tools/r8/internal/jV done
Event: 41.729 loading class com/android/tools/r8/internal/jV
Event: 41.729 loading class com/android/tools/r8/internal/jV done
Event: 41.729 loading class com/android/tools/r8/internal/iV
Event: 41.729 loading class com/android/tools/r8/internal/iV done
Event: 41.729 loading class com/android/tools/r8/internal/jV
Event: 41.729 loading class com/android/tools/r8/internal/jV done
Event: 41.729 loading class com/android/tools/r8/internal/jV$a
Event: 41.729 loading class com/android/tools/r8/internal/jV$a done
Event: 41.729 loading class com/android/tools/r8/graph/e0
Event: 41.729 loading class com/android/tools/r8/graph/e0 done
Event: 41.729 loading class com/android/tools/r8/graph/e0
Event: 41.729 loading class com/android/tools/r8/graph/e0 done
Event: 41.730 Executing VM operation: BulkRevokeBias
Event: 41.732 Executing VM operation: BulkRevokeBias done
Event: 41.742 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff690930000 - 0x00007ff69093a000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa67ec0000 - 0x00007ffa67ed7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa45770000 - 0x00007ffa4580d000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa420f0000 - 0x00007ffa42234000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa7e3d0000 - 0x00007ffa7e404000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:15 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (128M free)
TotalPageFile size 32602M (AvailPageFile size 47M)
current process WorkingSet (physical memory assigned to process): 739M, peak: 739M
current process commit charge ("private bytes"): 961M, peak: 1241M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
