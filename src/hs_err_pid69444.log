#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit area from 0x000001def5c20000 to 0x000001def5c30000 of length 65536.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3521), pid=69444, tid=73592
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 17:55:30 2022 India Standard Time elapsed time: 43.152972 seconds (0d 0h 0m 43s)

---------------  T H R E A D  ---------------

Current thread (0x000001defabcb000):  VMThread "VM Thread" [stack: 0x0000007068100000,0x0000007068200000] [id=73592]

Stack: [0x0000007068100000,0x0000007068200000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000001dee566112d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x733535]
V  [jvm.dll+0x7334eb]
V  [jvm.dll+0x5fb080]
V  [jvm.dll+0x5fb818]
C  [ntdll.dll+0xa8fcf]
C  [ntdll.dll+0x35e9a]
C  [ntdll.dll+0xa7fde]
C  0x000001dee566112d

VM_Operation (0x000000706ddfe070): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001de84b87000


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001de86b20790, length=91, elements={
0x000001dee034c000, 0x000001defac00000, 0x000001defac03800, 0x000001defb45d000,
0x000001defb461800, 0x000001defb465000, 0x000001defb46a000, 0x000001defb46d000,
0x000001defb481800, 0x000001defb5dd000, 0x000001defcd66000, 0x000001defd608800,
0x000001defc555800, 0x000001defcf53800, 0x000001defc0ee000, 0x000001defd594000,
0x000001defd5a4000, 0x000001defd5e0000, 0x000001defd66c000, 0x000001defb470800,
0x000001defb473800, 0x000001defb46e000, 0x000001defb472000, 0x000001defb471800,
0x000001defb473000, 0x000001defb46f000, 0x000001defb46f800, 0x000001defdcbf000,
0x000001defdcb9800, 0x000001defdcbd800, 0x000001defdcc0000, 0x000001defdcbb800,
0x000001defdcbe000, 0x000001defdcba000, 0x000001defdcc4000, 0x000001defdcc4800,
0x000001defdcc6800, 0x000001defdcc1800, 0x000001defdcc3000, 0x000001defdcc5800,
0x000001defdcc2800, 0x000001defdcc0800, 0x000001defdcc8000, 0x000001defdcc7000,
0x000001de80da5800, 0x000001de80da6000, 0x000001de80da2000, 0x000001de80da4800,
0x000001de80da1800, 0x000001de80da3800, 0x000001de80da8800, 0x000001de80da7000,
0x000001de80da3000, 0x000001de80dad800, 0x000001de80da8000, 0x000001de80da9800,
0x000001de80dac800, 0x000001de80daf000, 0x000001de80db0000, 0x000001de80dae800,
0x000001de80daa800, 0x000001de80dab000, 0x000001de80dac000, 0x000001de81071000,
0x000001de8106c000, 0x000001de8106b800, 0x000001de81072000, 0x000001de81072800,
0x000001de8106d000, 0x000001de81073800, 0x000001de8106d800, 0x000001de8106f800,
0x000001de81074800, 0x000001de8106e800, 0x000001de81075000, 0x000001de81070000,
0x000001de81077800, 0x000001de81078800, 0x000001de81076000, 0x000001de84b83800,
0x000001de84b81000, 0x000001de84b86000, 0x000001de84b88000, 0x000001de84b84800,
0x000001de84b83000, 0x000001de84b82000, 0x000001de84b85800, 0x000001de84b87000,
0x000001defbb35000, 0x000001defbb35800, 0x000001defbb32800
}

Java Threads: ( => current thread )
  0x000001dee034c000 JavaThread "main" [_thread_blocked, id=42260, stack(0x0000007067b00000,0x0000007067c00000)]
  0x000001defac00000 JavaThread "Reference Handler" daemon [_thread_blocked, id=70164, stack(0x0000007068200000,0x0000007068300000)]
  0x000001defac03800 JavaThread "Finalizer" daemon [_thread_blocked, id=71428, stack(0x0000007068300000,0x0000007068400000)]
  0x000001defb45d000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=71900, stack(0x0000007068400000,0x0000007068500000)]
  0x000001defb461800 JavaThread "Attach Listener" daemon [_thread_blocked, id=42312, stack(0x0000007068500000,0x0000007068600000)]
  0x000001defb465000 JavaThread "Service Thread" daemon [_thread_blocked, id=73168, stack(0x0000007068600000,0x0000007068700000)]
  0x000001defb46a000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=16228, stack(0x0000007068700000,0x0000007068800000)]
  0x000001defb46d000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=72804, stack(0x0000007068800000,0x0000007068900000)]
  0x000001defb481800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=71376, stack(0x0000007068900000,0x0000007068a00000)]
  0x000001defb5dd000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=53076, stack(0x0000007068a00000,0x0000007068b00000)]
  0x000001defcd66000 JavaThread "Daemon health stats" [_thread_blocked, id=70744, stack(0x0000007068e00000,0x0000007068f00000)]
  0x000001defd608800 JavaThread "Incoming local TCP Connector on port 51700" [_thread_in_native, id=72072, stack(0x0000007068f00000,0x0000007069000000)]
  0x000001defc555800 JavaThread "Daemon periodic checks" [_thread_blocked, id=52692, stack(0x0000007069000000,0x0000007069100000)]
  0x000001defcf53800 JavaThread "Daemon" [_thread_blocked, id=72632, stack(0x0000007069100000,0x0000007069200000)]
  0x000001defc0ee000 JavaThread "Handler for socket connection from /127.0.0.1:51700 to /127.0.0.1:51701" [_thread_in_native, id=72876, stack(0x0000007069200000,0x0000007069300000)]
  0x000001defd594000 JavaThread "Cancel handler" [_thread_blocked, id=68204, stack(0x0000007069300000,0x0000007069400000)]
  0x000001defd5a4000 JavaThread "Daemon worker" [_thread_blocked, id=62900, stack(0x0000007069400000,0x0000007069500000)]
  0x000001defd5e0000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51700 to /127.0.0.1:51701" [_thread_blocked, id=72036, stack(0x0000007069500000,0x0000007069600000)]
  0x000001defd66c000 JavaThread "Stdin handler" [_thread_blocked, id=62084, stack(0x0000007069600000,0x0000007069700000)]
  0x000001defb470800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=72924, stack(0x0000007069700000,0x0000007069800000)]
  0x000001defb473800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=72784, stack(0x0000007069900000,0x0000007069a00000)]
  0x000001defb46e000 JavaThread "File lock request listener" [_thread_in_native, id=64216, stack(0x0000007069a00000,0x0000007069b00000)]
  0x000001defb472000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=72980, stack(0x0000007069b00000,0x0000007069c00000)]
  0x000001defb471800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=52948, stack(0x0000007069c00000,0x0000007069d00000)]
  0x000001defb473000 JavaThread "File watcher server" daemon [_thread_blocked, id=73400, stack(0x0000007069d00000,0x0000007069e00000)]
  0x000001defb46f000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=72684, stack(0x0000007069e00000,0x0000007069f00000)]
  0x000001defb46f800 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=72200, stack(0x0000007069f00000,0x000000706a000000)]
  0x000001defdcbf000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=66832, stack(0x000000706a000000,0x000000706a100000)]
  0x000001defdcb9800 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=58964, stack(0x000000706a100000,0x000000706a200000)]
  0x000001defdcbd800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=73312, stack(0x000000706a200000,0x000000706a300000)]
  0x000001defdcc0000 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=8208, stack(0x000000706a300000,0x000000706a400000)]
  0x000001defdcbb800 JavaThread "jar transforms" [_thread_blocked, id=69300, stack(0x000000706a400000,0x000000706a500000)]
  0x000001defdcbe000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=52600, stack(0x000000706a500000,0x000000706a600000)]
  0x000001defdcba000 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=71528, stack(0x000000706a600000,0x000000706a700000)]
  0x000001defdcc4000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=72112, stack(0x000000706a700000,0x000000706a800000)]
  0x000001defdcc4800 JavaThread "jar transforms Thread 3" [_thread_blocked, id=72956, stack(0x000000706a800000,0x000000706a900000)]
  0x000001defdcc6800 JavaThread "Unconstrained build operations" [_thread_blocked, id=71852, stack(0x000000706a900000,0x000000706aa00000)]
  0x000001defdcc1800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=73364, stack(0x000000706aa00000,0x000000706ab00000)]
  0x000001defdcc3000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=72808, stack(0x000000706ab00000,0x000000706ac00000)]
  0x000001defdcc5800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=71712, stack(0x000000706ac00000,0x000000706ad00000)]
  0x000001defdcc2800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=42412, stack(0x000000706ad00000,0x000000706ae00000)]
  0x000001defdcc0800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=68268, stack(0x000000706ae00000,0x000000706af00000)]
  0x000001defdcc8000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=72908, stack(0x000000706af00000,0x000000706b000000)]
  0x000001defdcc7000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=72120, stack(0x000000706b000000,0x000000706b100000)]
  0x000001de80da5800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=72388, stack(0x000000706b100000,0x000000706b200000)]
  0x000001de80da6000 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=73044, stack(0x000000706b200000,0x000000706b300000)]
  0x000001de80da2000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=54964, stack(0x000000706b300000,0x000000706b400000)]
  0x000001de80da4800 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=72604, stack(0x000000706b400000,0x000000706b500000)]
  0x000001de80da1800 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=68348, stack(0x000000706b500000,0x000000706b600000)]
  0x000001de80da3800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=68388, stack(0x000000706b600000,0x000000706b700000)]
  0x000001de80da8800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=49412, stack(0x000000706b700000,0x000000706b800000)]
  0x000001de80da7000 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=71296, stack(0x000000706b800000,0x000000706b900000)]
  0x000001de80da3000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=35068, stack(0x000000706b900000,0x000000706ba00000)]
  0x000001de80dad800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=66860, stack(0x000000706ba00000,0x000000706bb00000)]
  0x000001de80da8000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=71060, stack(0x000000706bb00000,0x000000706bc00000)]
  0x000001de80da9800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=72296, stack(0x000000706bc00000,0x000000706bd00000)]
  0x000001de80dac800 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=56640, stack(0x000000706bd00000,0x000000706be00000)]
  0x000001de80daf000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=67616, stack(0x000000706be00000,0x000000706bf00000)]
  0x000001de80db0000 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=67008, stack(0x000000706bf00000,0x000000706c000000)]
  0x000001de80dae800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=70000, stack(0x000000706c000000,0x000000706c100000)]
  0x000001de80daa800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=69544, stack(0x000000706c100000,0x000000706c200000)]
  0x000001de80dab000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=73340, stack(0x000000706c200000,0x000000706c300000)]
  0x000001de80dac000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=73492, stack(0x000000706c300000,0x000000706c400000)]
  0x000001de81071000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=54080, stack(0x000000706c400000,0x000000706c500000)]
  0x000001de8106c000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=73516, stack(0x000000706c500000,0x000000706c600000)]
  0x000001de8106b800 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=53304, stack(0x000000706c600000,0x000000706c700000)]
  0x000001de81072000 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=71212, stack(0x000000706c700000,0x000000706c800000)]
  0x000001de81072800 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=73600, stack(0x000000706c800000,0x000000706c900000)]
  0x000001de8106d000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=71620, stack(0x000000706c900000,0x000000706ca00000)]
  0x000001de81073800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=44576, stack(0x000000706ca00000,0x000000706cb00000)]
  0x000001de8106d800 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=68004, stack(0x000000706cb00000,0x000000706cc00000)]
  0x000001de8106f800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=58212, stack(0x000000706cc00000,0x000000706cd00000)]
  0x000001de81074800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=65692, stack(0x000000706cd00000,0x000000706ce00000)]
  0x000001de8106e800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=62244, stack(0x000000706ce00000,0x000000706cf00000)]
  0x000001de81075000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=70968, stack(0x000000706cf00000,0x000000706d000000)]
  0x000001de81070000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=54952, stack(0x000000706d000000,0x000000706d100000)]
  0x000001de81077800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=49696, stack(0x000000706d100000,0x000000706d200000)]
  0x000001de81078800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=68644, stack(0x000000706d200000,0x000000706d300000)]
  0x000001de81076000 JavaThread "Memory manager" [_thread_blocked, id=73424, stack(0x000000706d300000,0x000000706d400000)]
  0x000001de84b83800 JavaThread "build event listener" [_thread_blocked, id=58236, stack(0x000000706d400000,0x000000706d500000)]
  0x000001de84b81000 JavaThread "Execution worker for ':'" [_thread_blocked, id=69124, stack(0x000000706d500000,0x000000706d600000)]
  0x000001de84b86000 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=72368, stack(0x000000706d600000,0x000000706d700000)]
  0x000001de84b88000 JavaThread "Execution worker for ':' Thread 3" [_thread_blocked, id=73180, stack(0x000000706d700000,0x000000706d800000)]
  0x000001de84b84800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=53732, stack(0x000000706d800000,0x000000706d900000)]
  0x000001de84b83000 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=21696, stack(0x000000706da00000,0x000000706db00000)]
  0x000001de84b82000 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=71592, stack(0x000000706db00000,0x000000706dc00000)]
  0x000001de84b85800 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=67388, stack(0x000000706dc00000,0x000000706dd00000)]
  0x000001de84b87000 JavaThread "ForkJoinPool-1-worker-3" daemon [_thread_blocked, id=70392, stack(0x000000706dd00000,0x000000706de00000)]
  0x000001defbb35000 JavaThread "ForkJoinPool-1-worker-5" daemon [_thread_blocked, id=68236, stack(0x000000706de00000,0x000000706df00000)]
  0x000001defbb35800 JavaThread "ForkJoinPool-1-worker-7" daemon [_thread_blocked, id=73624, stack(0x000000706df00000,0x000000706e000000)]
  0x000001defbb32800 JavaThread "ForkJoinPool-1-worker-1" daemon [_thread_blocked, id=67988, stack(0x000000706e000000,0x000000706e100000)]

Other Threads:
=>0x000001defabcb000 VMThread "VM Thread" [stack: 0x0000007068100000,0x0000007068200000] [id=73592]
  0x000001defb600000 WatcherThread [stack: 0x0000007068b00000,0x0000007068c00000] [id=56216]
  0x000001dee0366800 GCTaskThread "GC Thread#0" [stack: 0x0000007067c00000,0x0000007067d00000] [id=70732]
  0x000001defbe35800 GCTaskThread "GC Thread#1" [stack: 0x0000007068c00000,0x0000007068d00000] [id=73224]
  0x000001defbb00000 GCTaskThread "GC Thread#2" [stack: 0x0000007068d00000,0x0000007068e00000] [id=71436]
  0x000001defb665800 GCTaskThread "GC Thread#3" [stack: 0x0000007069800000,0x0000007069900000] [id=73540]
  0x000001dee038b800 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007067d00000,0x0000007067e00000] [id=71504]
  0x000001dee038f000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007067e00000,0x0000007067f00000] [id=69560]
  0x000001defaa35800 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000007067f00000,0x0000007068000000] [id=69848]
  0x000001defaa39800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x0000007068000000,0x0000007068100000] [id=71176]

Threads with active compile tasks:
C2 CompilerThread0  43211 12869 %     4       com.android.tools.r8.dex.j::a @ 313 (1042 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001dee0348250] Threads_lock - owner thread: 0x000001defabcb000
[0x000001dee0347f80] Heap_lock - owner thread: 0x000001de84b87000

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 564224K, used 244944K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 102656K, capacity 105032K, committed 105404K, reserved 1140736K
  class space    used 13428K, capacity 14313K, committed 14436K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Updating 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Updating 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Updating 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Updating 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Updating 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Updating 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Updating 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Updating 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Updating 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Updating 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Updating 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Updating 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Updating 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Updating 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Updating 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Updating 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Updating 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Updating 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%|HS|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Complete 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%|HS|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Complete 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%|HS|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Complete 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%|HS|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Complete 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%|HC|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Complete 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HC|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HS|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HC|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Updating 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Updating 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Updating 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Updating 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Updating 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Updating 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2300000| Updating 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Updating 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Updating 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Updating 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Updating 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Updating 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Updating 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Updating 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Updating 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Updating 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Updating 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Updating 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Updating 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3000000| Updating 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3100000| Updating 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3200000| Updating 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3300000| Updating 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3400000| Updating 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3500000| Updating 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3600000| Updating 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3700000| Updating 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3800000| Updating 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3900000| Updating 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3a00000| Updating 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3b00000| Updating 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3c00000| Updating 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3d00000| Updating 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3e00000| Updating 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a3f00000| Updating 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4000000| Updating 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4100000| Updating 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4200000| Updating 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4300000| Updating 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4400000| Updating 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4500000| Updating 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4600000| Updating 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4700000| Updating 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4800000| Updating 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4900000| Updating 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4a00000| Updating 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4b00000| Updating 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4c00000| Updating 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4d00000| Updating 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4e00000| Updating 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%|HS|  |TAMS 0x00000000a5000000, 0x00000000a4f00000| Complete 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000, 0x00000000a5000000| Updating 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%|HS|  |TAMS 0x00000000a5200000, 0x00000000a5100000| Complete 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%|HC|  |TAMS 0x00000000a5300000, 0x00000000a5200000| Complete 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%|HS|  |TAMS 0x00000000a5400000, 0x00000000a5300000| Complete 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%|HS|  |TAMS 0x00000000a5500000, 0x00000000a5400000| Complete 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%|HS|  |TAMS 0x00000000a5600000, 0x00000000a5500000| Complete 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%|HC|  |TAMS 0x00000000a5700000, 0x00000000a5600000| Complete 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%|HS|  |TAMS 0x00000000a5800000, 0x00000000a5700000| Complete 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%|HC|  |TAMS 0x00000000a5900000, 0x00000000a5800000| Complete 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%|HS|  |TAMS 0x00000000a5a00000, 0x00000000a5900000| Complete 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%|HS|  |TAMS 0x00000000a5b00000, 0x00000000a5a00000| Complete 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%|HS|  |TAMS 0x00000000a5c00000, 0x00000000a5b00000| Complete 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%|HC|  |TAMS 0x00000000a5d00000, 0x00000000a5c00000| Complete 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%|HS|  |TAMS 0x00000000a5e00000, 0x00000000a5d00000| Complete 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%|HC|  |TAMS 0x00000000a5f00000, 0x00000000a5e00000| Complete 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%|HS|  |TAMS 0x00000000a6000000, 0x00000000a5f00000| Complete 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%|HC|  |TAMS 0x00000000a6100000, 0x00000000a6000000| Complete 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%|HC|  |TAMS 0x00000000a6200000, 0x00000000a6100000| Complete 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%|HC|  |TAMS 0x00000000a6300000, 0x00000000a6200000| Complete 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6300000| Updating 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6400000| Updating 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6500000| Updating 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6700000, 0x00000000a6600000| Updating 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| O|  |TAMS 0x00000000a6800000, 0x00000000a6700000| Updating 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000, 0x00000000a6800000| Updating 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000, 0x00000000a6900000| Updating 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6b00000, 0x00000000a6a00000| Updating 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6c00000, 0x00000000a6b00000| Updating 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%|HS|  |TAMS 0x00000000a6d00000, 0x00000000a6c00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%|HS|  |TAMS 0x00000000a6e00000, 0x00000000a6d00000| Complete 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%|HC|  |TAMS 0x00000000a6f00000, 0x00000000a6e00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%|HS|  |TAMS 0x00000000a7000000, 0x00000000a6f00000| Complete 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%|HC|  |TAMS 0x00000000a7100000, 0x00000000a7000000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%|HC|  |TAMS 0x00000000a7200000, 0x00000000a7100000| Complete 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%|HS|  |TAMS 0x00000000a7300000, 0x00000000a7200000| Complete 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%|HS|  |TAMS 0x00000000a7400000, 0x00000000a7300000| Complete 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%|HS|  |TAMS 0x00000000a7500000, 0x00000000a7400000| Complete 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%|HS|  |TAMS 0x00000000a7600000, 0x00000000a7500000| Complete 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7700000, 0x00000000a7600000| Updating 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7800000, 0x00000000a7700000| Updating 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000, 0x00000000a7800000| Updating 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000, 0x00000000a7900000| Updating 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000, 0x00000000a7a00000| Updating 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000, 0x00000000a7b00000| Updating 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7d00000, 0x00000000a7c00000| Updating 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7e00000, 0x00000000a7d00000| Updating 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7f00000, 0x00000000a7e00000| Updating 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%|HS|  |TAMS 0x00000000a8000000, 0x00000000a7f00000| Complete 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8100000, 0x00000000a8000000| Updating 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%|HS|  |TAMS 0x00000000a8200000, 0x00000000a8100000| Complete 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%|HC|  |TAMS 0x00000000a8300000, 0x00000000a8200000| Complete 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HS|  |TAMS 0x00000000a8400000, 0x00000000a8300000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%|HS|  |TAMS 0x00000000a8500000, 0x00000000a8400000| Complete 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8600000, 0x00000000a8500000| Updating 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%|HS|  |TAMS 0x00000000a8700000, 0x00000000a8600000| Complete 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%|HS|  |TAMS 0x00000000a8800000, 0x00000000a8700000| Complete 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8800000| Updating 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%|HS|  |TAMS 0x00000000a8a00000, 0x00000000a8900000| Complete 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%|HS|  |TAMS 0x00000000a8b00000, 0x00000000a8a00000| Complete 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%|HC|  |TAMS 0x00000000a8c00000, 0x00000000a8b00000| Complete 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8c00000| Updating 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8d00000| Updating 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8f00000, 0x00000000a8e00000| Updating 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%|HS|  |TAMS 0x00000000a9000000, 0x00000000a8f00000| Complete 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%|HC|  |TAMS 0x00000000a9100000, 0x00000000a9000000| Complete 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| O|  |TAMS 0x00000000a9200000, 0x00000000a9100000| Updating 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%|HS|  |TAMS 0x00000000a9300000, 0x00000000a9200000| Complete 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| O|  |TAMS 0x00000000a9400000, 0x00000000a9300000| Updating 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%|HS|  |TAMS 0x00000000a9500000, 0x00000000a9400000| Complete 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9600000, 0x00000000a9500000| Updating 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|  |TAMS 0x00000000a9700000, 0x00000000a9600000| Updating 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| O|  |TAMS 0x00000000a9800000, 0x00000000a9700000| Updating 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| O|  |TAMS 0x00000000a9900000, 0x00000000a9800000| Updating 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9a00000, 0x00000000a9900000| Updating 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000, 0x00000000a9a00000| Updating 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9c00000, 0x00000000a9b00000| Updating 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| O|  |TAMS 0x00000000a9d00000, 0x00000000a9c00000| Updating 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| O|  |TAMS 0x00000000a9e00000, 0x00000000a9d00000| Updating 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9f00000, 0x00000000a9e00000| Updating 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000aa000000, 0x00000000a9f00000| Updating 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa100000, 0x00000000aa000000| Updating 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%|HS|  |TAMS 0x00000000aa200000, 0x00000000aa100000| Complete 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%|HC|  |TAMS 0x00000000aa300000, 0x00000000aa200000| Complete 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa400000, 0x00000000aa300000| Updating 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| O|  |TAMS 0x00000000aa500000, 0x00000000aa400000| Updating 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%|HS|  |TAMS 0x00000000aa600000, 0x00000000aa500000| Complete 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%|HC|  |TAMS 0x00000000aa700000, 0x00000000aa600000| Complete 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| O|  |TAMS 0x00000000aa800000, 0x00000000aa700000| Updating 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000, 0x00000000aa800000| Updating 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| O|  |TAMS 0x00000000aaa00000, 0x00000000aa900000| Updating 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aab00000, 0x00000000aaa00000| Updating 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aac00000, 0x00000000aab00000| Updating 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aad00000, 0x00000000aac00000| Updating 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%|HS|  |TAMS 0x00000000aae00000, 0x00000000aad00000| Complete 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%|HC|  |TAMS 0x00000000aaf00000, 0x00000000aae00000| Complete 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%|HC|  |TAMS 0x00000000ab000000, 0x00000000aaf00000| Complete 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%|HC|  |TAMS 0x00000000ab100000, 0x00000000ab000000| Complete 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab100000, 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%|HS|  |TAMS 0x00000000ab300000, 0x00000000ab200000| Complete 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab400000, 0x00000000ab300000| Updating 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab500000, 0x00000000ab400000| Updating 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| O|  |TAMS 0x00000000ab600000, 0x00000000ab500000| Updating 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%|HS|  |TAMS 0x00000000ab700000, 0x00000000ab600000| Complete 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%|HC|  |TAMS 0x00000000ab800000, 0x00000000ab700000| Complete 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab900000, 0x00000000ab800000| Updating 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000aba00000, 0x00000000ab900000| Updating 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000abb00000, 0x00000000aba00000| Updating 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000, 0x00000000abb00000| Updating 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%|HS|  |TAMS 0x00000000abd00000, 0x00000000abc00000| Complete 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%|HC|  |TAMS 0x00000000abe00000, 0x00000000abd00000| Complete 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%| O|  |TAMS 0x00000000abf00000, 0x00000000abe00000| Updating 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%| O|  |TAMS 0x00000000ac000000, 0x00000000abf00000| Updating 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|  |TAMS 0x00000000ac100000, 0x00000000ac000000| Updating 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%| O|  |TAMS 0x00000000ac200000, 0x00000000ac100000| Updating 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%| O|  |TAMS 0x00000000ac300000, 0x00000000ac200000| Updating 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%| O|  |TAMS 0x00000000ac400000, 0x00000000ac300000| Updating 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%| O|  |TAMS 0x00000000ac500000, 0x00000000ac400000| Updating 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%|HS|  |TAMS 0x00000000ac600000, 0x00000000ac500000| Complete 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%|HC|  |TAMS 0x00000000ac700000, 0x00000000ac600000| Complete 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac800000, 0x00000000ac700000| Updating 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%| O|  |TAMS 0x00000000ac900000, 0x00000000ac800000| Updating 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%| O|  |TAMS 0x00000000aca00000, 0x00000000ac900000| Updating 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000acb00000, 0x00000000aca00000| Updating 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acc00000, 0x00000000acb00000| Updating 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acd00000, 0x00000000acc00000| Updating 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| O|  |TAMS 0x00000000ace00000, 0x00000000acd00000| Updating 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000ace00000| Updating 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000ad000000, 0x00000000acf00000| Updating 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000, 0x00000000ad000000| Updating 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000, 0x00000000ad100000| Updating 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000, 0x00000000ad200000| Updating 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000, 0x00000000ad300000| Updating 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000, 0x00000000ad400000| Updating 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000, 0x00000000ad500000| Updating 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad6c1a08, 0x00000000ad600000| Updating 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad700000, 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad800000, 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ad900000, 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000ada00000, 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adb00000, 0x00000000adb00000| Untracked 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%|HS|  |TAMS 0x00000000add00000, 0x00000000adc00000| Complete 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%|HC|  |TAMS 0x00000000ade00000, 0x00000000add00000| Complete 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%|HC|  |TAMS 0x00000000adf00000, 0x00000000ade00000| Complete 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000adf00000, 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae100000, 0x00000000ae000000| Updating 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae200000, 0x00000000ae100000| Updating 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae300000, 0x00000000ae200000| Updating 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae400000, 0x00000000ae300000| Updating 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae500000, 0x00000000ae400000| Updating 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae600000, 0x00000000ae500000| Updating 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae700000, 0x00000000ae600000| Updating 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae800000, 0x00000000ae700000| Updating 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%| O|  |TAMS 0x00000000ae800000, 0x00000000ae800000| Untracked 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000ae900000, 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aea34200, 0x00000000aeb00000| 20%| O|  |TAMS 0x00000000aea00000, 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aeb00000, 0x00000000aec00000|  0%| F|  |TAMS 0x00000000aeb00000, 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aec00000, 0x00000000aed00000|  0%| F|  |TAMS 0x00000000aec00000, 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aed00000, 0x00000000aee00000|  0%| F|  |TAMS 0x00000000aed00000, 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aef00000, 0x00000000aee00000| Updating 
| 239|0x00000000aef00000, 0x00000000aef00000, 0x00000000af000000|  0%| F|  |TAMS 0x00000000aef00000, 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af000000, 0x00000000af100000|  0%| F|  |TAMS 0x00000000af000000, 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af100000, 0x00000000af200000|  0%| F|  |TAMS 0x00000000af100000, 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af200000, 0x00000000af300000|  0%| F|  |TAMS 0x00000000af200000, 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af300000, 0x00000000af400000|  0%| F|  |TAMS 0x00000000af300000, 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af400000, 0x00000000af500000|  0%| F|  |TAMS 0x00000000af400000, 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af500000, 0x00000000af600000|  0%| F|  |TAMS 0x00000000af500000, 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af600000, 0x00000000af700000|  0%| F|  |TAMS 0x00000000af600000, 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af700000, 0x00000000af800000|  0%| F|  |TAMS 0x00000000af700000, 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af800000, 0x00000000af900000|  0%| F|  |TAMS 0x00000000af800000, 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000af900000, 0x00000000afa00000|  0%| F|  |TAMS 0x00000000af900000, 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afa00000, 0x00000000afb00000|  0%| F|  |TAMS 0x00000000afa00000, 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afb00000, 0x00000000afc00000|  0%| F|  |TAMS 0x00000000afb00000, 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afc00000, 0x00000000afd00000|  0%| F|  |TAMS 0x00000000afc00000, 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afd00000, 0x00000000afe00000|  0%| F|  |TAMS 0x00000000afd00000, 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000afe00000, 0x00000000aff00000|  0%| F|  |TAMS 0x00000000afe00000, 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000aff00000, 0x00000000b0000000|  0%| F|  |TAMS 0x00000000aff00000, 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0000000, 0x00000000b0100000|  0%| F|  |TAMS 0x00000000b0000000, 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0100000, 0x00000000b0200000|  0%| F|  |TAMS 0x00000000b0100000, 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0200000, 0x00000000b0300000|  0%| F|  |TAMS 0x00000000b0200000, 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0300000, 0x00000000b0400000|  0%| F|  |TAMS 0x00000000b0300000, 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0400000, 0x00000000b0500000|  0%| F|  |TAMS 0x00000000b0400000, 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0500000, 0x00000000b0600000|  0%| F|  |TAMS 0x00000000b0500000, 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0600000, 0x00000000b0700000|  0%| F|  |TAMS 0x00000000b0600000, 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0700000, 0x00000000b0800000|  0%| F|  |TAMS 0x00000000b0700000, 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0800000, 0x00000000b0900000|  0%| F|  |TAMS 0x00000000b0800000, 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0900000, 0x00000000b0a00000|  0%| F|  |TAMS 0x00000000b0900000, 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0a00000, 0x00000000b0b00000|  0%| F|  |TAMS 0x00000000b0a00000, 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0b00000, 0x00000000b0c00000|  0%| F|  |TAMS 0x00000000b0b00000, 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0c00000, 0x00000000b0d00000|  0%| F|  |TAMS 0x00000000b0c00000, 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0d00000, 0x00000000b0e00000|  0%| F|  |TAMS 0x00000000b0d00000, 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0e00000, 0x00000000b0f00000|  0%| F|  |TAMS 0x00000000b0e00000, 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b0f00000, 0x00000000b1000000|  0%| F|  |TAMS 0x00000000b0f00000, 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1000000, 0x00000000b1100000|  0%| F|  |TAMS 0x00000000b1000000, 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1100000, 0x00000000b1200000|  0%| F|  |TAMS 0x00000000b1100000, 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1200000, 0x00000000b1300000|  0%| F|  |TAMS 0x00000000b1200000, 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1300000, 0x00000000b1400000|  0%| F|  |TAMS 0x00000000b1300000, 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1400000, 0x00000000b1500000|  0%| F|  |TAMS 0x00000000b1400000, 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| S|CS|TAMS 0x00000000b1500000, 0x00000000b1500000| Complete 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| S|CS|TAMS 0x00000000b1600000, 0x00000000b1600000| Complete 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| S|CS|TAMS 0x00000000b1700000, 0x00000000b1700000| Complete 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| S|CS|TAMS 0x00000000b1800000, 0x00000000b1800000| Complete 
| 281|0x00000000b1900000, 0x00000000b1900000, 0x00000000b1a00000|  0%| F|  |TAMS 0x00000000b1900000, 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1a00000, 0x00000000b1b00000|  0%| F|  |TAMS 0x00000000b1a00000, 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1b00000, 0x00000000b1c00000|  0%| F|  |TAMS 0x00000000b1b00000, 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1c00000, 0x00000000b1d00000|  0%| F|  |TAMS 0x00000000b1c00000, 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1d00000, 0x00000000b1e00000|  0%| F|  |TAMS 0x00000000b1d00000, 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1e00000, 0x00000000b1f00000|  0%| F|  |TAMS 0x00000000b1e00000, 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b1f00000, 0x00000000b2000000|  0%| F|  |TAMS 0x00000000b1f00000, 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2000000, 0x00000000b2100000|  0%| F|  |TAMS 0x00000000b2000000, 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2100000, 0x00000000b2200000|  0%| F|  |TAMS 0x00000000b2100000, 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2200000, 0x00000000b2300000|  0%| F|  |TAMS 0x00000000b2200000, 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2300000, 0x00000000b2400000|  0%| F|  |TAMS 0x00000000b2300000, 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2400000, 0x00000000b2500000|  0%| F|  |TAMS 0x00000000b2400000, 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2500000, 0x00000000b2600000|  0%| F|  |TAMS 0x00000000b2500000, 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2600000, 0x00000000b2700000|  0%| F|  |TAMS 0x00000000b2600000, 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2700000, 0x00000000b2800000|  0%| F|  |TAMS 0x00000000b2700000, 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2800000, 0x00000000b2900000|  0%| F|  |TAMS 0x00000000b2800000, 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2900000, 0x00000000b2a00000|  0%| F|  |TAMS 0x00000000b2900000, 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2a00000, 0x00000000b2b00000|  0%| F|  |TAMS 0x00000000b2a00000, 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2b00000, 0x00000000b2c00000|  0%| F|  |TAMS 0x00000000b2b00000, 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2c00000, 0x00000000b2d00000|  0%| F|  |TAMS 0x00000000b2c00000, 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2d00000, 0x00000000b2e00000|  0%| F|  |TAMS 0x00000000b2d00000, 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2e00000, 0x00000000b2f00000|  0%| F|  |TAMS 0x00000000b2e00000, 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b2f00000, 0x00000000b3000000|  0%| F|  |TAMS 0x00000000b2f00000, 0x00000000b2f00000| Untracked 

Card table byte_map: [0x000001def4f90000,0x000001def5290000] _byte_map_base: 0x000001def4a90000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001dee0384bb8, (CMBitMap*) 0x000001dee0384bf0
 Prev Bits: [0x000001def5590000, 0x000001def6d90000)
 Next Bits: [0x000001def6d90000, 0x000001def8590000)

Polling page: 0x000001dee0430000

Metaspace:

Usage:
  Non-class:     88.59 MB capacity,    87.14 MB ( 98%) used,     1.17 MB (  1%) free+waste,   291.88 KB ( <1%) overhead. 
      Class:     13.98 MB capacity,    13.11 MB ( 94%) used,   755.01 KB (  5%) free+waste,   129.25 KB ( <1%) overhead. 
       Both:    102.57 MB capacity,   100.25 MB ( 98%) used,     1.91 MB (  2%) free+waste,   421.13 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       90.00 MB reserved,      88.84 MB ( 99%) committed 
      Class space:        1.00 GB reserved,      14.10 MB (  1%) committed 
             Both:        1.09 GB reserved,     102.93 MB (  9%) committed 

Chunk freelists:
   Non-Class:  57.00 KB
       Class:  0 bytes
        Both:  57.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 171.56 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=7284Kb max_used=7284Kb free=112779Kb
 bounds [0x000001deed120000, 0x000001deed840000, 0x000001def4660000]
CodeHeap 'profiled nmethods': size=120000Kb used=29188Kb max_used=29188Kb free=90811Kb
 bounds [0x000001dee5bf0000, 0x000001dee7880000, 0x000001deed120000]
CodeHeap 'non-nmethods': size=5696Kb used=2403Kb max_used=2431Kb free=3292Kb
 bounds [0x000001dee5660000, 0x000001dee58d0000, 0x000001dee5bf0000]
 total_blobs=13735 nmethods=12810 adapters=837
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 43.026 Thread 0x000001defb46d000 12975       2       java.util.AbstractList$SubList::<init> (35 bytes)
Event: 43.026 Thread 0x000001defb46d000 nmethod 12975 0x000001dee786e110 code [0x000001dee786e2c0, 0x000001dee786e458]
Event: 43.026 Thread 0x000001defb46d000 12976       2       java.util.AbstractList$SubList$1::<init> (41 bytes)
Event: 43.026 Thread 0x000001defb46d000 nmethod 12976 0x000001dee786e510 code [0x000001dee786e6e0, 0x000001dee786e8e8]
Event: 43.053 Thread 0x000001defb46d000 12979       2       com.android.tools.r8.code.I0::hashCode (21 bytes)
Event: 43.053 Thread 0x000001defb46d000 nmethod 12979 0x000001dee786ea10 code [0x000001dee786ebc0, 0x000001dee786ed08]
Event: 43.057 Thread 0x000001defb46d000 12980       2       java.lang.Enum::compareTo (9 bytes)
Event: 43.057 Thread 0x000001defb46d000 nmethod 12980 0x000001dee786ed90 code [0x000001dee786ef40, 0x000001dee786f088]
Event: 43.065 Thread 0x000001defb46d000 12982       2       com.android.tools.r8.graph.d1::a (49 bytes)
Event: 43.065 Thread 0x000001defb46d000 nmethod 12982 0x000001dee786f190 code [0x000001dee786f360, 0x000001dee786f5a8]
Event: 43.079 Thread 0x000001defb46d000 12984       2       com.android.tools.r8.code.U2::<init> (58 bytes)
Event: 43.080 Thread 0x000001defb46d000 nmethod 12984 0x000001dee786f790 code [0x000001dee786f9a0, 0x000001dee786ff28]
Event: 43.089 Thread 0x000001defb46d000 12986       2       org.gradle.api.internal.provider.AbstractProperty$NonFinalizedValue::forUpstream (13 bytes)
Event: 43.089 Thread 0x000001defb46d000 nmethod 12986 0x000001dee7870390 code [0x000001dee7870540, 0x000001dee7870658]
Event: 43.097 Thread 0x000001defb46d000 12987       2       com.android.tools.r8.code.U0::hashCode (21 bytes)
Event: 43.097 Thread 0x000001defb46d000 nmethod 12987 0x000001dee7870710 code [0x000001dee78708c0, 0x000001dee7870a08]
Event: 43.099 Thread 0x000001defb46d000 12988       2       com.android.tools.r8.code.T0::hashCode (21 bytes)
Event: 43.099 Thread 0x000001defb46d000 nmethod 12988 0x000001dee7870a90 code [0x000001dee7870c40, 0x000001dee7870d88]
Event: 43.102 Thread 0x000001defb46d000 12989       2       com.android.tools.r8.graph.j1$j::v0 (2 bytes)
Event: 43.102 Thread 0x000001defb46d000 nmethod 12989 0x000001dee7870e10 code [0x000001dee7870fc0, 0x000001dee78710b8]

GC Heap History (20 events):
Event: 39.893 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 244736K, used 195558K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 100277K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12978K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 39.907 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 244736K, used 196582K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 3 survivors (3072K)
 Metaspace       used 100277K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12978K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 39.938 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 244736K, used 197933K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100277K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12978K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.481 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 244736K, used 207149K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.501 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 244736K, used 202357K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.532 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 244736K, used 210549K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.551 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 244736K, used 206550K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.669 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 244736K, used 217814K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.695 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 244736K, used 212505K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.854 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 244736K, used 220697K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.877 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 244736K, used 211672K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100294K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.917 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 244736K, used 219864K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 2 survivors (2048K)
 Metaspace       used 100297K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 40.946 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 311296K, used 214893K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100297K, capacity 102573K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 41.686 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 311296K, used 243565K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 29 young (29696K), 2 survivors (2048K)
 Metaspace       used 100328K, capacity 102575K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 41.758 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 311296K, used 226055K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 100328K, capacity 102575K, committed 102844K, reserved 1138688K
  class space    used 12979K, capacity 13836K, committed 13924K, reserved 1048576K
}
Event: 41.938 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 311296K, used 248582K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 27 young (27648K), 4 survivors (4096K)
 Metaspace       used 100620K, capacity 102936K, committed 103100K, reserved 1138688K
  class space    used 13016K, capacity 13871K, committed 13924K, reserved 1048576K
}
Event: 41.976 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 311296K, used 231697K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 100620K, capacity 102936K, committed 103100K, reserved 1138688K
  class space    used 13016K, capacity 13871K, committed 13924K, reserved 1048576K
}
Event: 42.557 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 311296K, used 253201K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 4 survivors (4096K)
 Metaspace       used 102407K, capacity 104753K, committed 104892K, reserved 1140736K
  class space    used 13409K, capacity 14311K, committed 14436K, reserved 1048576K
}
Event: 42.584 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 311296K, used 235454K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 102407K, capacity 104753K, committed 104892K, reserved 1140736K
  class space    used 13409K, capacity 14311K, committed 14436K, reserved 1048576K
}
Event: 43.103 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 311296K, used 255934K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 4 survivors (4096K)
 Metaspace       used 102656K, capacity 105032K, committed 105404K, reserved 1140736K
  class space    used 13428K, capacity 14313K, committed 14436K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 42.220 Thread 0x000001defdcbd800 DEOPT PACKING pc=0x000001dee764569d sp=0x000000706a2fe140
Event: 42.220 Thread 0x000001defdcbd800 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706a2fd608 mode 0
Event: 42.305 Thread 0x000001defbb35000 DEOPT PACKING pc=0x000001dee77020f7 sp=0x000000706defc740
Event: 42.305 Thread 0x000001defbb35000 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706defbc40 mode 0
Event: 42.308 Thread 0x000001defbb35000 DEOPT PACKING pc=0x000001dee77020f7 sp=0x000000706defc850
Event: 42.308 Thread 0x000001defbb35000 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706defbd50 mode 0
Event: 42.407 Thread 0x000001de84b87000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x000001deed7f1b28 relative=0x00000000000002a8
Event: 42.407 Thread 0x000001de84b87000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001deed7f1b28 method=com.android.tools.r8.graph.S0.g(Ljava/lang/Object;)Z @ 37 c2
Event: 42.407 Thread 0x000001de84b87000 DEOPT PACKING pc=0x000001deed7f1b28 sp=0x000000706ddfe810
Event: 42.407 Thread 0x000001de84b87000 DEOPT UNPACKING pc=0x000001dee56aa1af sp=0x000000706ddfe7b0 mode 2
Event: 42.706 Thread 0x000001defbb35000 DEOPT PACKING pc=0x000001dee77bd0fe sp=0x000000706defe260
Event: 42.706 Thread 0x000001defbb35000 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706defd7e8 mode 0
Event: 42.721 Thread 0x000001defbb35000 DEOPT PACKING pc=0x000001dee773ddb6 sp=0x000000706defe020
Event: 42.756 Thread 0x000001defbb35000 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706defdc80 mode 0
Event: 42.766 Thread 0x000001defbb35000 DEOPT PACKING pc=0x000001dee7750cfe sp=0x000000706defe520
Event: 42.766 Thread 0x000001defbb35000 DEOPT UNPACKING pc=0x000001dee56aa95e sp=0x000000706defdb90 mode 0
Event: 43.089 Thread 0x000001de84b81000 Uncommon trap: trap_request=0xffffffec fr.pc=0x000001deed803ae8 relative=0x0000000000003968
Event: 43.089 Thread 0x000001de84b81000 Uncommon trap: reason=null_assert_or_unreached0 action=make_not_entrant pc=0x000001deed803ae8 method=org.gradle.internal.snapshot.impl.DefaultValueSnapshotter.processValue(Ljava/lang/Object;Lorg/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueV
Event: 43.089 Thread 0x000001de84b81000 DEOPT PACKING pc=0x000001deed803ae8 sp=0x000000706d5fc760
Event: 43.090 Thread 0x000001de84b81000 DEOPT UNPACKING pc=0x000001dee56aa1af sp=0x000000706d5fc770 mode 2

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 26.093 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a94b6660}: groovy/util/slurpersupport/NodeChildCustomizer> (0x00000000a94b6660) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.104 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a93b20e0}: groovy/util/slurpersupport/AttributesBeanInfo> (0x00000000a93b20e0) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.104 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a93c7920}: groovy/util/slurpersupport/NodeChildrenBeanInfo> (0x00000000a93c7920) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.105 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a93dcf38}: groovy/util/slurpersupport/NodeChildrenCustomizer> (0x00000000a93dcf38) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.106 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a93f9b58}: groovy/util/slurpersupport/AttributesCustomizer> (0x00000000a93f9b58) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.112 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a92dbac8}: groovy/util/slurpersupport/AttributeBeanInfo> (0x00000000a92dbac8) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.113 Thread 0x000001de84b86000 Exception <a 'java/lang/ClassNotFoundException'{0x00000000a92f1250}: groovy/util/slurpersupport/AttributeCustomizer> (0x00000000a92f1250) thrown at [./src/hotspot/share/classfile/systemDictionary.cpp, line 231]
Event: 26.961 Thread 0x000001de84b86000 Implicit null exception at 0x000001deed459f94 to 0x000001deed45a558
Event: 27.821 Thread 0x000001de84b86000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a80b2558}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000a80b2558) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 27.826 Thread 0x000001de84b86000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a80c6718}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000a80c6718) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 28.168 Thread 0x000001de84b86000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a7e06220}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a7e06220) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 31.234 Thread 0x000001de80da3800 Implicit null exception at 0x000001deed65c424 to 0x000001deed65c5c4
Event: 33.168 Thread 0x000001de84b86000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa6f5438}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000aa6f5438) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 33.836 Thread 0x000001de80da1800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a8e7d848}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000a8e7d848) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 37.600 Thread 0x000001de84b82000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ac60cdf8}> (0x00000000ac60cdf8) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 37.947 Thread 0x000001de84b82000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa5172e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0
Event: 42.394 Thread 0x000001de84b87000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b22a5f48}: Found class java.lang.Object, but interface was expected> (0x00000000b22a5f48) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 42.402 Thread 0x000001defbb35000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b2121258}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int)'> (0x00000000b2121258) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 42.405 Thread 0x000001de84b87000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b214b7a0}: Found class java.lang.Object, but interface was expected> (0x00000000b214b7a0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 42.407 Thread 0x000001de84b87000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b2177510}: Found class java.lang.Object, but interface was expected> (0x00000000b2177510) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]

Events (20 events):
Event: 42.691 loading class com/android/tools/r8/graph/N2 done
Event: 42.692 loading class com/android/tools/r8/internal/XG
Event: 42.692 loading class com/android/tools/r8/internal/XG done
Event: 42.692 loading class com/android/tools/r8/internal/cH
Event: 42.692 loading class com/android/tools/r8/internal/cH done
Event: 42.798 loading class com/android/tools/r8/internal/vg
Event: 42.798 loading class com/android/tools/r8/internal/vg done
Event: 42.807 loading class com/android/tools/r8/internal/Dj
Event: 42.807 loading class com/android/tools/r8/internal/Dj done
Event: 43.031 Executing VM operation: CGC_Operation
Event: 43.048 Executing VM operation: CGC_Operation done
Event: 43.090 Executing VM operation: RevokeBias
Event: 43.092 Executing VM operation: RevokeBias done
Event: 43.093 Executing VM operation: RevokeBias
Event: 43.093 Executing VM operation: RevokeBias done
Event: 43.094 Executing VM operation: RevokeBias
Event: 43.094 Executing VM operation: RevokeBias done
Event: 43.095 Executing VM operation: RevokeBias
Event: 43.095 Executing VM operation: RevokeBias done
Event: 43.103 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff75e320000 - 0x00007ff75e32a000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa744b0000 - 0x00007ffa744c9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa414a0000 - 0x00007ffa4153d000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa3c770000 - 0x00007ffa3c8b4000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:17 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (77M free)
TotalPageFile size 32602M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 480M, peak: 482M
current process commit charge ("private bytes"): 860M, peak: 863M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
