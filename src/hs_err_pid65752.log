#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 197132288 bytes for Failed to commit area from 0x00000000c5400000 to 0x00000000d1000000 of length 197132288.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3521), pid=65752, tid=53700
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 17:57:59 2022 India Standard Time elapsed time: 31.185252 seconds (0d 0h 0m 31s)

---------------  T H R E A D  ---------------

Current thread (0x0000015d679ed000):  VMThread "VM Thread" [stack: 0x00000036e8500000,0x00000036e8600000] [id=53700]

Stack: [0x00000036e8500000,0x00000036e8600000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x0000015d52c3112d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x733535]
V  [jvm.dll+0x7334eb]
V  [jvm.dll+0x5fb080]
V  [jvm.dll+0x5fb818]
C  [ntdll.dll+0xa8fcf]
C  [ntdll.dll+0x35e9a]
C  [ntdll.dll+0xa7fde]
C  0x0000015d52c3112d

VM_Operation (0x00000036ee3fe600): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000015d6f9ee000


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000015d6b25b3c0, length=92, elements={
0x0000015d4f11f800, 0x0000015d67a10000, 0x0000015d67a1b000, 0x0000015d683a3800,
0x0000015d683a4800, 0x0000015d683a5800, 0x0000015d683a7000, 0x0000015d683ab800,
0x0000015d683af800, 0x0000015d684e4000, 0x0000015d69be2800, 0x0000015d6a9aa800,
0x0000015d6a9af800, 0x0000015d69465000, 0x0000015d6a4b2800, 0x0000015d6a5a0800,
0x0000015d69db8000, 0x0000015d6abdf000, 0x0000015d6979e000, 0x0000015d6a6a1000,
0x0000015d6a6a0800, 0x0000015d6a6a2000, 0x0000015d6a69f800, 0x0000015d6a6a3000,
0x0000015d6a6a3800, 0x0000015d6a6a4800, 0x0000015d6a69f000, 0x0000015d6a6a5800,
0x0000015d6bb48000, 0x0000015d6bb49800, 0x0000015d6bb44800, 0x0000015d6bb46800,
0x0000015d6bb49000, 0x0000015d6bb44000, 0x0000015d6bb4a800, 0x0000015d6bb4b800,
0x0000015d6bb4c000, 0x0000015d6bb4d000, 0x0000015d6bb47000, 0x0000015d6bb4f800,
0x0000015d6bb4d800, 0x0000015d6bb51000, 0x0000015d6bb50000, 0x0000015d6bb52000,
0x0000015d6bb4e800, 0x0000015d6bb52800, 0x0000015d6ac2f800, 0x0000015d6ac31000,
0x0000015d6ac32000, 0x0000015d6ac32800, 0x0000015d6ac30000, 0x0000015d6ac33800,
0x0000015d6ac34000, 0x0000015d6ac2e800, 0x0000015d6ac35000, 0x0000015d6ac37800,
0x0000015d6ac36800, 0x0000015d6ac39000, 0x0000015d6ac36000, 0x0000015d6ac3b800,
0x0000015d6ac3c800, 0x0000015d6ac38800, 0x0000015d6ac3d000, 0x0000015d6ac3a000,
0x0000015d6ac3a800, 0x0000015d6e1b5000, 0x0000015d6e1b3800, 0x0000015d6e1b3000,
0x0000015d6e1b4800, 0x0000015d6e1b1000, 0x0000015d6e1b6000, 0x0000015d6e1b0800,
0x0000015d6e1b7000, 0x0000015d6e1b7800, 0x0000015d6e1b2000, 0x0000015d6e1b9800,
0x0000015d6e1bc800, 0x0000015d6e1bd800, 0x0000015d6e1be000, 0x0000015d6e1b8800,
0x0000015d6e1bf000, 0x0000015d6e1bb000, 0x0000015d6f9e6800, 0x0000015d6f9e8000,
0x0000015d6f9eb800, 0x0000015d6f9ed000, 0x0000015d6f9e9000, 0x0000015d6f9e7800,
0x0000015d6f9ef800, 0x0000015d6f9ec800, 0x0000015d6f9ee000, 0x0000015d6f9ee800
}

Java Threads: ( => current thread )
  0x0000015d4f11f800 JavaThread "main" [_thread_blocked, id=72404, stack(0x00000036e7f00000,0x00000036e8000000)]
  0x0000015d67a10000 JavaThread "Reference Handler" daemon [_thread_blocked, id=49460, stack(0x00000036e8600000,0x00000036e8700000)]
  0x0000015d67a1b000 JavaThread "Finalizer" daemon [_thread_blocked, id=53704, stack(0x00000036e8700000,0x00000036e8800000)]
  0x0000015d683a3800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=72600, stack(0x00000036e8800000,0x00000036e8900000)]
  0x0000015d683a4800 JavaThread "Attach Listener" daemon [_thread_blocked, id=73148, stack(0x00000036e8900000,0x00000036e8a00000)]
  0x0000015d683a5800 JavaThread "Service Thread" daemon [_thread_blocked, id=6836, stack(0x00000036e8a00000,0x00000036e8b00000)]
  0x0000015d683a7000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=73612, stack(0x00000036e8b00000,0x00000036e8c00000)]
  0x0000015d683ab800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=67948, stack(0x00000036e8c00000,0x00000036e8d00000)]
  0x0000015d683af800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=69144, stack(0x00000036e8d00000,0x00000036e8e00000)]
  0x0000015d684e4000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=59596, stack(0x00000036e8e00000,0x00000036e8f00000)]
  0x0000015d69be2800 JavaThread "Daemon health stats" [_thread_blocked, id=70952, stack(0x00000036e9300000,0x00000036e9400000)]
  0x0000015d6a9aa800 JavaThread "Incoming local TCP Connector on port 51740" [_thread_in_native, id=71188, stack(0x00000036e9200000,0x00000036e9300000)]
  0x0000015d6a9af800 JavaThread "Daemon periodic checks" [_thread_blocked, id=73452, stack(0x00000036e9400000,0x00000036e9500000)]
  0x0000015d69465000 JavaThread "Daemon" [_thread_blocked, id=67460, stack(0x00000036e9500000,0x00000036e9600000)]
  0x0000015d6a4b2800 JavaThread "Handler for socket connection from /127.0.0.1:51740 to /127.0.0.1:51741" [_thread_in_native, id=32888, stack(0x00000036e9600000,0x00000036e9700000)]
  0x0000015d6a5a0800 JavaThread "Cancel handler" [_thread_blocked, id=73328, stack(0x00000036e9700000,0x00000036e9800000)]
  0x0000015d69db8000 JavaThread "Daemon worker" [_thread_blocked, id=72872, stack(0x00000036e9800000,0x00000036e9900000)]
  0x0000015d6abdf000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51740 to /127.0.0.1:51741" [_thread_blocked, id=60484, stack(0x00000036e9900000,0x00000036e9a00000)]
  0x0000015d6979e000 JavaThread "Stdin handler" [_thread_blocked, id=71892, stack(0x00000036e9a00000,0x00000036e9b00000)]
  0x0000015d6a6a1000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=58820, stack(0x00000036e9b00000,0x00000036e9c00000)]
  0x0000015d6a6a0800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=70772, stack(0x00000036e9d00000,0x00000036e9e00000)]
  0x0000015d6a6a2000 JavaThread "File lock request listener" [_thread_in_native, id=69856, stack(0x00000036e9e00000,0x00000036e9f00000)]
  0x0000015d6a69f800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=72460, stack(0x00000036e9f00000,0x00000036ea000000)]
  0x0000015d6a6a3000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=72248, stack(0x00000036ea100000,0x00000036ea200000)]
  0x0000015d6a6a3800 JavaThread "File watcher server" daemon [_thread_blocked, id=72424, stack(0x00000036ea200000,0x00000036ea300000)]
  0x0000015d6a6a4800 JavaThread "File watcher consumer" daemon [_thread_blocked, id=66788, stack(0x00000036ea300000,0x00000036ea400000)]
  0x0000015d6a69f000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=69392, stack(0x00000036ea400000,0x00000036ea500000)]
  0x0000015d6a6a5800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=70020, stack(0x00000036ea500000,0x00000036ea600000)]
  0x0000015d6bb48000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=71908, stack(0x00000036ea600000,0x00000036ea700000)]
  0x0000015d6bb49800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=39800, stack(0x00000036ea700000,0x00000036ea800000)]
  0x0000015d6bb44800 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=56312, stack(0x00000036ea800000,0x00000036ea900000)]
  0x0000015d6bb46800 JavaThread "jar transforms" [_thread_blocked, id=52184, stack(0x00000036ea900000,0x00000036eaa00000)]
  0x0000015d6bb49000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=72796, stack(0x00000036eaa00000,0x00000036eab00000)]
  0x0000015d6bb44000 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=71928, stack(0x00000036eab00000,0x00000036eac00000)]
  0x0000015d6bb4a800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=73300, stack(0x00000036eac00000,0x00000036ead00000)]
  0x0000015d6bb4b800 JavaThread "jar transforms Thread 3" [_thread_blocked, id=62288, stack(0x00000036ead00000,0x00000036eae00000)]
  0x0000015d6bb4c000 JavaThread "Unconstrained build operations" [_thread_blocked, id=64884, stack(0x00000036eae00000,0x00000036eaf00000)]
  0x0000015d6bb4d000 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=33836, stack(0x00000036eaf00000,0x00000036eb000000)]
  0x0000015d6bb47000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=72288, stack(0x00000036eb000000,0x00000036eb100000)]
  0x0000015d6bb4f800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=72916, stack(0x00000036eb100000,0x00000036eb200000)]
  0x0000015d6bb4d800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=72764, stack(0x00000036eb200000,0x00000036eb300000)]
  0x0000015d6bb51000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=72728, stack(0x00000036eb300000,0x00000036eb400000)]
  0x0000015d6bb50000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=71428, stack(0x00000036eb400000,0x00000036eb500000)]
  0x0000015d6bb52000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=69560, stack(0x00000036eb500000,0x00000036eb600000)]
  0x0000015d6bb4e800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=71176, stack(0x00000036eb600000,0x00000036eb700000)]
  0x0000015d6bb52800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=73168, stack(0x00000036eb700000,0x00000036eb800000)]
  0x0000015d6ac2f800 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=64860, stack(0x00000036eb800000,0x00000036eb900000)]
  0x0000015d6ac31000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=70632, stack(0x00000036eb900000,0x00000036eba00000)]
  0x0000015d6ac32000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=73164, stack(0x00000036eba00000,0x00000036ebb00000)]
  0x0000015d6ac32800 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=71348, stack(0x00000036ebb00000,0x00000036ebc00000)]
  0x0000015d6ac30000 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=44728, stack(0x00000036ebc00000,0x00000036ebd00000)]
  0x0000015d6ac33800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=20284, stack(0x00000036ebd00000,0x00000036ebe00000)]
  0x0000015d6ac34000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=73388, stack(0x00000036ebe00000,0x00000036ebf00000)]
  0x0000015d6ac2e800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=60096, stack(0x00000036ebf00000,0x00000036ec000000)]
  0x0000015d6ac35000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=26344, stack(0x00000036ec000000,0x00000036ec100000)]
  0x0000015d6ac37800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=66248, stack(0x00000036ec100000,0x00000036ec200000)]
  0x0000015d6ac36800 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=50296, stack(0x00000036ec200000,0x00000036ec300000)]
  0x0000015d6ac39000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=73324, stack(0x00000036ec300000,0x00000036ec400000)]
  0x0000015d6ac36000 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=69032, stack(0x00000036ec400000,0x00000036ec500000)]
  0x0000015d6ac3b800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=73120, stack(0x00000036ec500000,0x00000036ec600000)]
  0x0000015d6ac3c800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=73604, stack(0x00000036ec600000,0x00000036ec700000)]
  0x0000015d6ac38800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=72372, stack(0x00000036ec700000,0x00000036ec800000)]
  0x0000015d6ac3d000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=71376, stack(0x00000036ec800000,0x00000036ec900000)]
  0x0000015d6ac3a000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=69508, stack(0x00000036ec900000,0x00000036eca00000)]
  0x0000015d6ac3a800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=53076, stack(0x00000036eca00000,0x00000036ecb00000)]
  0x0000015d6e1b5000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=62900, stack(0x00000036ecb00000,0x00000036ecc00000)]
  0x0000015d6e1b3800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=72420, stack(0x00000036ecc00000,0x00000036ecd00000)]
  0x0000015d6e1b3000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=71756, stack(0x00000036ecd00000,0x00000036ece00000)]
  0x0000015d6e1b4800 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=73348, stack(0x00000036ece00000,0x00000036ecf00000)]
  0x0000015d6e1b1000 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=65128, stack(0x00000036ecf00000,0x00000036ed000000)]
  0x0000015d6e1b6000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=72684, stack(0x00000036ed000000,0x00000036ed100000)]
  0x0000015d6e1b0800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=73336, stack(0x00000036ed100000,0x00000036ed200000)]
  0x0000015d6e1b7000 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=53860, stack(0x00000036ed200000,0x00000036ed300000)]
  0x0000015d6e1b7800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=71852, stack(0x00000036ed300000,0x00000036ed400000)]
  0x0000015d6e1b2000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=72112, stack(0x00000036ed400000,0x00000036ed500000)]
  0x0000015d6e1b9800 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=72908, stack(0x00000036ed500000,0x00000036ed600000)]
  0x0000015d6e1bc800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=68268, stack(0x00000036ed600000,0x00000036ed700000)]
  0x0000015d6e1bd800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=72924, stack(0x00000036ea000000,0x00000036ea100000)]
  0x0000015d6e1be000 JavaThread "Memory manager" [_thread_blocked, id=72956, stack(0x00000036ed700000,0x00000036ed800000)]
  0x0000015d6e1b8800 JavaThread "build event listener" [_thread_blocked, id=50784, stack(0x00000036ed800000,0x00000036ed900000)]
  0x0000015d6e1bf000 JavaThread "Execution worker for ':'" [_thread_blocked, id=73652, stack(0x00000036ed900000,0x00000036eda00000)]
  0x0000015d6e1bb000 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=29748, stack(0x00000036eda00000,0x00000036edb00000)]
  0x0000015d6f9e6800 JavaThread "Execution worker for ':' Thread 3" [_thread_blocked, id=73712, stack(0x00000036edb00000,0x00000036edc00000)]
  0x0000015d6f9e8000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=61408, stack(0x00000036edc00000,0x00000036edd00000)]
  0x0000015d6f9eb800 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=20692, stack(0x00000036edd00000,0x00000036ede00000)]
  0x0000015d6f9ed000 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=71700, stack(0x00000036ede00000,0x00000036edf00000)]
  0x0000015d6f9e9000 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=68792, stack(0x00000036edf00000,0x00000036ee000000)]
  0x0000015d6f9e7800 JavaThread "ForkJoinPool-1-worker-3" daemon [_thread_blocked, id=43700, stack(0x00000036ee000000,0x00000036ee100000)]
  0x0000015d6f9ef800 JavaThread "ForkJoinPool-1-worker-5" daemon [_thread_blocked, id=58600, stack(0x00000036ee100000,0x00000036ee200000)]
  0x0000015d6f9ec800 JavaThread "ForkJoinPool-1-worker-7" daemon [_thread_blocked, id=58128, stack(0x00000036ee200000,0x00000036ee300000)]
  0x0000015d6f9ee000 JavaThread "ForkJoinPool-1-worker-1" daemon [_thread_blocked, id=60340, stack(0x00000036ee300000,0x00000036ee400000)]
  0x0000015d6f9ee800 JavaThread "WorkerExecutor Queue Thread 4" [_thread_blocked, id=70720, stack(0x00000036e7d00000,0x00000036e7e00000)]

Other Threads:
=>0x0000015d679ed000 VMThread "VM Thread" [stack: 0x00000036e8500000,0x00000036e8600000] [id=53700]
  0x0000015d68509800 WatcherThread [stack: 0x00000036e8f00000,0x00000036e9000000] [id=72264]
  0x0000015d4f136800 GCTaskThread "GC Thread#0" [stack: 0x00000036e8000000,0x00000036e8100000] [id=67504]
  0x0000015d68a04000 GCTaskThread "GC Thread#1" [stack: 0x00000036e9000000,0x00000036e9100000] [id=68472]
  0x0000015d68a05000 GCTaskThread "GC Thread#2" [stack: 0x00000036e9100000,0x00000036e9200000] [id=63664]
  0x0000015d6a56e800 GCTaskThread "GC Thread#3" [stack: 0x00000036e9c00000,0x00000036e9d00000] [id=71180]
  0x0000015d4f15d800 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000036e8100000,0x00000036e8200000] [id=70440]
  0x0000015d4f15f800 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000036e8200000,0x00000036e8300000] [id=67476]
  0x0000015d4f1f0000 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000036e8300000,0x00000036e8400000] [id=70080]
  0x0000015d4f1f4800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x00000036e8400000,0x00000036e8500000] [id=72780]

Threads with active compile tasks:
C2 CompilerThread0  31218 13336       4       com.android.tools.r8.internal.wt::hasNext (212 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000015d4f1140d0] Threads_lock - owner thread: 0x0000015d679ed000
[0x0000015d4f11cfa0] Heap_lock - owner thread: 0x0000015d6f9ee000

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 802816K, used 406528K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 105258K, capacity 107687K, committed 107904K, reserved 1142784K
  class space    used 13722K, capacity 14594K, committed 14720K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Untracked 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Untracked 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Untracked 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| O|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Untracked 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| O|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Untracked 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%|HS|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Complete 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HS|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HS|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HS|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%|HC|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Complete 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%|HC|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Complete 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%|HS|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Complete 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%|HC|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Complete 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3000000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3100000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3200000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3700000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4300000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4800000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4a00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%|HS|  |TAMS 0x00000000a5000000, 0x00000000a4f00000| Complete 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%|HC|  |TAMS 0x00000000a5100000, 0x00000000a5000000| Complete 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%|HS|  |TAMS 0x00000000a5200000, 0x00000000a5100000| Complete 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%|HS|  |TAMS 0x00000000a5300000, 0x00000000a5200000| Complete 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%|HS|  |TAMS 0x00000000a5400000, 0x00000000a5300000| Complete 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%|HC|  |TAMS 0x00000000a5500000, 0x00000000a5400000| Complete 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%|HS|  |TAMS 0x00000000a5600000, 0x00000000a5500000| Complete 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%|HC|  |TAMS 0x00000000a5700000, 0x00000000a5600000| Complete 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%|HS|  |TAMS 0x00000000a5800000, 0x00000000a5700000| Complete 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%|HS|  |TAMS 0x00000000a5900000, 0x00000000a5800000| Complete 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%|HS|  |TAMS 0x00000000a5a00000, 0x00000000a5900000| Complete 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%|HC|  |TAMS 0x00000000a5b00000, 0x00000000a5a00000| Complete 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%|HS|  |TAMS 0x00000000a5c00000, 0x00000000a5b00000| Complete 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%|HC|  |TAMS 0x00000000a5d00000, 0x00000000a5c00000| Complete 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%|HS|  |TAMS 0x00000000a5e00000, 0x00000000a5d00000| Complete 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%|HC|  |TAMS 0x00000000a5f00000, 0x00000000a5e00000| Complete 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%|HC|  |TAMS 0x00000000a6000000, 0x00000000a5f00000| Complete 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%|HC|  |TAMS 0x00000000a6100000, 0x00000000a6000000| Complete 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%|HS|  |TAMS 0x00000000a6200000, 0x00000000a6100000| Complete 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000, 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%|HS|  |TAMS 0x00000000a6700000, 0x00000000a6600000| Complete 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%|HC|  |TAMS 0x00000000a6800000, 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%|HS|  |TAMS 0x00000000a6900000, 0x00000000a6800000| Complete 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%|HC|  |TAMS 0x00000000a6a00000, 0x00000000a6900000| Complete 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%|HC|  |TAMS 0x00000000a6b00000, 0x00000000a6a00000| Complete 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%|HS|  |TAMS 0x00000000a6c00000, 0x00000000a6b00000| Complete 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%|HS|  |TAMS 0x00000000a6d00000, 0x00000000a6c00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%|HS|  |TAMS 0x00000000a6e00000, 0x00000000a6d00000| Complete 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|  |TAMS 0x00000000a6f00000, 0x00000000a6e00000| Untracked 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a7000000, 0x00000000a6f00000| Untracked 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%|HS|  |TAMS 0x00000000a7100000, 0x00000000a7000000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7200000, 0x00000000a7100000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7300000, 0x00000000a7200000| Untracked 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7400000, 0x00000000a7300000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7500000, 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|  |TAMS 0x00000000a7600000, 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7700000, 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7800000, 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000, 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000, 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000, 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000, 0x00000000a7b00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7d00000, 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7e00000, 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%|HS|  |TAMS 0x00000000a7f00000, 0x00000000a7e00000| Complete 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a8000000, 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%|HS|  |TAMS 0x00000000a8100000, 0x00000000a8000000| Complete 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%|HC|  |TAMS 0x00000000a8200000, 0x00000000a8100000| Complete 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%|HS|  |TAMS 0x00000000a8300000, 0x00000000a8200000| Complete 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%|HS|  |TAMS 0x00000000a8400000, 0x00000000a8300000| Complete 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|  |TAMS 0x00000000a8500000, 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%|HS|  |TAMS 0x00000000a8600000, 0x00000000a8500000| Complete 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8700000, 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8800000, 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%|HS|  |TAMS 0x00000000a8a00000, 0x00000000a8900000| Complete 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%|HC|  |TAMS 0x00000000a8b00000, 0x00000000a8a00000| Complete 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8c00000, 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8d00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%|HS|  |TAMS 0x00000000a8f00000, 0x00000000a8e00000| Complete 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%|HC|  |TAMS 0x00000000a9000000, 0x00000000a8f00000| Complete 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| O|  |TAMS 0x00000000a9100000, 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%|HS|  |TAMS 0x00000000a9200000, 0x00000000a9100000| Complete 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| O|  |TAMS 0x00000000a9300000, 0x00000000a9200000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%|HS|  |TAMS 0x00000000a9400000, 0x00000000a9300000| Complete 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| O|  |TAMS 0x00000000a9500000, 0x00000000a9400000| Untracked 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| O|  |TAMS 0x00000000a9600000, 0x00000000a9500000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| O|  |TAMS 0x00000000a9700000, 0x00000000a9600000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%|HS|  |TAMS 0x00000000a9800000, 0x00000000a9700000| Complete 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%|HC|  |TAMS 0x00000000a9900000, 0x00000000a9800000| Complete 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| O|  |TAMS 0x00000000a9a00000, 0x00000000a9900000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| O|  |TAMS 0x00000000a9b00000, 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| O|  |TAMS 0x00000000a9c00000, 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%|HS|  |TAMS 0x00000000a9d00000, 0x00000000a9c00000| Complete 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%|HC|  |TAMS 0x00000000a9e00000, 0x00000000a9d00000| Complete 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| O|  |TAMS 0x00000000a9f00000, 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| O|  |TAMS 0x00000000aa000000, 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| O|  |TAMS 0x00000000aa100000, 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| O|  |TAMS 0x00000000aa200000, 0x00000000aa100000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| O|  |TAMS 0x00000000aa300000, 0x00000000aa200000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| O|  |TAMS 0x00000000aa400000, 0x00000000aa300000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%|HS|  |TAMS 0x00000000aa500000, 0x00000000aa400000| Complete 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%|HC|  |TAMS 0x00000000aa600000, 0x00000000aa500000| Complete 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%|HC|  |TAMS 0x00000000aa700000, 0x00000000aa600000| Complete 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%|HC|  |TAMS 0x00000000aa800000, 0x00000000aa700000| Complete 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| O|  |TAMS 0x00000000aa900000, 0x00000000aa800000| Untracked 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%|HS|  |TAMS 0x00000000aaa00000, 0x00000000aa900000| Complete 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| O|  |TAMS 0x00000000aab00000, 0x00000000aaa00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| O|  |TAMS 0x00000000aac00000, 0x00000000aab00000| Untracked 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| O|  |TAMS 0x00000000aad00000, 0x00000000aac00000| Untracked 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| O|  |TAMS 0x00000000aae00000, 0x00000000aad00000| Untracked 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| O|  |TAMS 0x00000000aaf00000, 0x00000000aae00000| Untracked 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| O|  |TAMS 0x00000000ab000000, 0x00000000aaf00000| Untracked 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| O|  |TAMS 0x00000000ab100000, 0x00000000ab000000| Untracked 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| O|  |TAMS 0x00000000ab200000, 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| O|  |TAMS 0x00000000ab300000, 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| O|  |TAMS 0x00000000ab400000, 0x00000000ab300000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| O|  |TAMS 0x00000000ab500000, 0x00000000ab400000| Untracked 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%|HS|  |TAMS 0x00000000ab600000, 0x00000000ab500000| Complete 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%|HC|  |TAMS 0x00000000ab700000, 0x00000000ab600000| Complete 
| 183|0x00000000ab700000, 0x00000000ab800000, 0x00000000ab800000|100%| O|  |TAMS 0x00000000ab800000, 0x00000000ab700000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab900000, 0x00000000ab900000|100%| O|  |TAMS 0x00000000ab900000, 0x00000000ab800000| Untracked 
| 185|0x00000000ab900000, 0x00000000aba00000, 0x00000000aba00000|100%| O|  |TAMS 0x00000000aba00000, 0x00000000ab900000| Untracked 
| 186|0x00000000aba00000, 0x00000000abb00000, 0x00000000abb00000|100%| O|  |TAMS 0x00000000abb00000, 0x00000000aba00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abc00000, 0x00000000abc00000|100%| O|  |TAMS 0x00000000abc00000, 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abd00000, 0x00000000abd00000|100%| O|  |TAMS 0x00000000abd00000, 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abe00000, 0x00000000abe00000|100%|HS|  |TAMS 0x00000000abe00000, 0x00000000abd00000| Complete 
| 190|0x00000000abe00000, 0x00000000abf00000, 0x00000000abf00000|100%|HC|  |TAMS 0x00000000abf00000, 0x00000000abe00000| Complete 
| 191|0x00000000abf00000, 0x00000000ac000000, 0x00000000ac000000|100%|HC|  |TAMS 0x00000000ac000000, 0x00000000abf00000| Complete 
| 192|0x00000000ac000000, 0x00000000ac100000, 0x00000000ac100000|100%| O|  |TAMS 0x00000000ac100000, 0x00000000ac000000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac200000, 0x00000000ac200000|100%|HS|  |TAMS 0x00000000ac200000, 0x00000000ac100000| Complete 
| 194|0x00000000ac200000, 0x00000000ac300000, 0x00000000ac300000|100%| O|  |TAMS 0x00000000ac300000, 0x00000000ac200000| Untracked 
| 195|0x00000000ac300000, 0x00000000ac400000, 0x00000000ac400000|100%|HS|  |TAMS 0x00000000ac400000, 0x00000000ac300000| Complete 
| 196|0x00000000ac400000, 0x00000000ac500000, 0x00000000ac500000|100%|HS|  |TAMS 0x00000000ac500000, 0x00000000ac400000| Complete 
| 197|0x00000000ac500000, 0x00000000ac600000, 0x00000000ac600000|100%|HC|  |TAMS 0x00000000ac600000, 0x00000000ac500000| Complete 
| 198|0x00000000ac600000, 0x00000000ac700000, 0x00000000ac700000|100%| O|  |TAMS 0x00000000ac700000, 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac800000, 0x00000000ac800000|100%| O|  |TAMS 0x00000000ac800000, 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac900000, 0x00000000ac900000|100%|HS|  |TAMS 0x00000000ac900000, 0x00000000ac800000| Complete 
| 201|0x00000000ac900000, 0x00000000aca00000, 0x00000000aca00000|100%|HC|  |TAMS 0x00000000aca00000, 0x00000000ac900000| Complete 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| O|  |TAMS 0x00000000acb00000, 0x00000000aca00000| Untracked 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| O|  |TAMS 0x00000000acc00000, 0x00000000acb00000| Untracked 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| O|  |TAMS 0x00000000acd00000, 0x00000000acc00000| Untracked 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%|HS|  |TAMS 0x00000000ace00000, 0x00000000acd00000| Complete 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| O|  |TAMS 0x00000000acf00000, 0x00000000ace00000| Untracked 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| O|  |TAMS 0x00000000ad000000, 0x00000000acf00000| Untracked 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| O|  |TAMS 0x00000000ad100000, 0x00000000ad000000| Untracked 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| O|  |TAMS 0x00000000ad200000, 0x00000000ad100000| Untracked 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| O|  |TAMS 0x00000000ad300000, 0x00000000ad200000| Untracked 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| O|  |TAMS 0x00000000ad400000, 0x00000000ad300000| Untracked 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| O|  |TAMS 0x00000000ad500000, 0x00000000ad400000| Untracked 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| O|  |TAMS 0x00000000ad600000, 0x00000000ad500000| Untracked 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| O|  |TAMS 0x00000000ad700000, 0x00000000ad600000| Untracked 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| O|  |TAMS 0x00000000ad800000, 0x00000000ad700000| Untracked 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| O|  |TAMS 0x00000000ad900000, 0x00000000ad800000| Untracked 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| O|  |TAMS 0x00000000ada00000, 0x00000000ad900000| Untracked 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| O|  |TAMS 0x00000000adb00000, 0x00000000ada00000| Untracked 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| O|  |TAMS 0x00000000adc00000, 0x00000000adb00000| Untracked 
| 220|0x00000000adc00000, 0x00000000add00000, 0x00000000add00000|100%| O|  |TAMS 0x00000000add00000, 0x00000000adc00000| Untracked 
| 221|0x00000000add00000, 0x00000000ade00000, 0x00000000ade00000|100%| O|  |TAMS 0x00000000ade00000, 0x00000000add00000| Untracked 
| 222|0x00000000ade00000, 0x00000000adf00000, 0x00000000adf00000|100%| O|  |TAMS 0x00000000adf00000, 0x00000000ade00000| Untracked 
| 223|0x00000000adf00000, 0x00000000ae000000, 0x00000000ae000000|100%| O|  |TAMS 0x00000000ae000000, 0x00000000adf00000| Untracked 
| 224|0x00000000ae000000, 0x00000000ae100000, 0x00000000ae100000|100%| O|  |TAMS 0x00000000ae100000, 0x00000000ae000000| Untracked 
| 225|0x00000000ae100000, 0x00000000ae200000, 0x00000000ae200000|100%| O|  |TAMS 0x00000000ae200000, 0x00000000ae100000| Untracked 
| 226|0x00000000ae200000, 0x00000000ae300000, 0x00000000ae300000|100%| O|  |TAMS 0x00000000ae300000, 0x00000000ae200000| Untracked 
| 227|0x00000000ae300000, 0x00000000ae400000, 0x00000000ae400000|100%| O|  |TAMS 0x00000000ae400000, 0x00000000ae300000| Untracked 
| 228|0x00000000ae400000, 0x00000000ae500000, 0x00000000ae500000|100%| O|  |TAMS 0x00000000ae500000, 0x00000000ae400000| Untracked 
| 229|0x00000000ae500000, 0x00000000ae600000, 0x00000000ae600000|100%| O|  |TAMS 0x00000000ae600000, 0x00000000ae500000| Untracked 
| 230|0x00000000ae600000, 0x00000000ae700000, 0x00000000ae700000|100%| O|  |TAMS 0x00000000ae700000, 0x00000000ae600000| Untracked 
| 231|0x00000000ae700000, 0x00000000ae800000, 0x00000000ae800000|100%| O|  |TAMS 0x00000000ae800000, 0x00000000ae700000| Untracked 
| 232|0x00000000ae800000, 0x00000000ae900000, 0x00000000ae900000|100%| O|  |TAMS 0x00000000ae900000, 0x00000000ae800000| Untracked 
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| O|  |TAMS 0x00000000aea00000, 0x00000000ae900000| Untracked 
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| O|  |TAMS 0x00000000aeb00000, 0x00000000aea00000| Untracked 
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| O|  |TAMS 0x00000000aec00000, 0x00000000aeb00000| Untracked 
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| O|  |TAMS 0x00000000aed00000, 0x00000000aec00000| Untracked 
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| O|  |TAMS 0x00000000aee00000, 0x00000000aed00000| Untracked 
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| O|  |TAMS 0x00000000aef00000, 0x00000000aee00000| Untracked 
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| O|  |TAMS 0x00000000af000000, 0x00000000aef00000| Untracked 
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| O|  |TAMS 0x00000000af100000, 0x00000000af000000| Untracked 
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| O|  |TAMS 0x00000000af200000, 0x00000000af100000| Untracked 
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| O|  |TAMS 0x00000000af300000, 0x00000000af200000| Untracked 
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| O|  |TAMS 0x00000000af400000, 0x00000000af300000| Untracked 
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| O|  |TAMS 0x00000000af500000, 0x00000000af400000| Untracked 
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| O|  |TAMS 0x00000000af600000, 0x00000000af500000| Untracked 
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| O|  |TAMS 0x00000000af700000, 0x00000000af600000| Untracked 
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| O|  |TAMS 0x00000000af800000, 0x00000000af700000| Untracked 
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| O|  |TAMS 0x00000000af900000, 0x00000000af800000| Untracked 
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| O|  |TAMS 0x00000000afa00000, 0x00000000af900000| Untracked 
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| O|  |TAMS 0x00000000afb00000, 0x00000000afa00000| Untracked 
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| O|  |TAMS 0x00000000afc00000, 0x00000000afb00000| Untracked 
| 252|0x00000000afc00000, 0x00000000afd00000, 0x00000000afd00000|100%| O|  |TAMS 0x00000000afd00000, 0x00000000afc00000| Untracked 
| 253|0x00000000afd00000, 0x00000000afe00000, 0x00000000afe00000|100%| O|  |TAMS 0x00000000afe00000, 0x00000000afd00000| Untracked 
| 254|0x00000000afe00000, 0x00000000aff00000, 0x00000000aff00000|100%| O|  |TAMS 0x00000000aff00000, 0x00000000afe00000| Untracked 
| 255|0x00000000aff00000, 0x00000000b0000000, 0x00000000b0000000|100%| O|  |TAMS 0x00000000b0000000, 0x00000000aff00000| Untracked 
| 256|0x00000000b0000000, 0x00000000b0100000, 0x00000000b0100000|100%| O|  |TAMS 0x00000000b0100000, 0x00000000b0000000| Untracked 
| 257|0x00000000b0100000, 0x00000000b0200000, 0x00000000b0200000|100%| O|  |TAMS 0x00000000b0200000, 0x00000000b0100000| Untracked 
| 258|0x00000000b0200000, 0x00000000b0300000, 0x00000000b0300000|100%| O|  |TAMS 0x00000000b0300000, 0x00000000b0200000| Untracked 
| 259|0x00000000b0300000, 0x00000000b0400000, 0x00000000b0400000|100%| O|  |TAMS 0x00000000b0400000, 0x00000000b0300000| Untracked 
| 260|0x00000000b0400000, 0x00000000b0500000, 0x00000000b0500000|100%| O|  |TAMS 0x00000000b0500000, 0x00000000b0400000| Untracked 
| 261|0x00000000b0500000, 0x00000000b0600000, 0x00000000b0600000|100%| O|  |TAMS 0x00000000b0600000, 0x00000000b0500000| Untracked 
| 262|0x00000000b0600000, 0x00000000b0700000, 0x00000000b0700000|100%| O|  |TAMS 0x00000000b0700000, 0x00000000b0600000| Untracked 
| 263|0x00000000b0700000, 0x00000000b0800000, 0x00000000b0800000|100%| O|  |TAMS 0x00000000b0800000, 0x00000000b0700000| Untracked 
| 264|0x00000000b0800000, 0x00000000b0900000, 0x00000000b0900000|100%| O|  |TAMS 0x00000000b0900000, 0x00000000b0800000| Untracked 
| 265|0x00000000b0900000, 0x00000000b0a00000, 0x00000000b0a00000|100%| O|  |TAMS 0x00000000b0a00000, 0x00000000b0900000| Untracked 
| 266|0x00000000b0a00000, 0x00000000b0b00000, 0x00000000b0b00000|100%| O|  |TAMS 0x00000000b0b00000, 0x00000000b0a00000| Untracked 
| 267|0x00000000b0b00000, 0x00000000b0c00000, 0x00000000b0c00000|100%| O|  |TAMS 0x00000000b0c00000, 0x00000000b0b00000| Untracked 
| 268|0x00000000b0c00000, 0x00000000b0d00000, 0x00000000b0d00000|100%| O|  |TAMS 0x00000000b0d00000, 0x00000000b0c00000| Untracked 
| 269|0x00000000b0d00000, 0x00000000b0e00000, 0x00000000b0e00000|100%| O|  |TAMS 0x00000000b0e00000, 0x00000000b0d00000| Untracked 
| 270|0x00000000b0e00000, 0x00000000b0f00000, 0x00000000b0f00000|100%| O|  |TAMS 0x00000000b0f00000, 0x00000000b0e00000| Untracked 
| 271|0x00000000b0f00000, 0x00000000b1000000, 0x00000000b1000000|100%| O|  |TAMS 0x00000000b1000000, 0x00000000b0f00000| Untracked 
| 272|0x00000000b1000000, 0x00000000b1100000, 0x00000000b1100000|100%| O|  |TAMS 0x00000000b1100000, 0x00000000b1000000| Untracked 
| 273|0x00000000b1100000, 0x00000000b1200000, 0x00000000b1200000|100%| O|  |TAMS 0x00000000b1200000, 0x00000000b1100000| Untracked 
| 274|0x00000000b1200000, 0x00000000b1300000, 0x00000000b1300000|100%| O|  |TAMS 0x00000000b1300000, 0x00000000b1200000| Untracked 
| 275|0x00000000b1300000, 0x00000000b1400000, 0x00000000b1400000|100%| O|  |TAMS 0x00000000b1400000, 0x00000000b1300000| Untracked 
| 276|0x00000000b1400000, 0x00000000b1500000, 0x00000000b1500000|100%| O|  |TAMS 0x00000000b1500000, 0x00000000b1400000| Untracked 
| 277|0x00000000b1500000, 0x00000000b1600000, 0x00000000b1600000|100%| O|  |TAMS 0x00000000b1600000, 0x00000000b1500000| Untracked 
| 278|0x00000000b1600000, 0x00000000b1700000, 0x00000000b1700000|100%| O|  |TAMS 0x00000000b1700000, 0x00000000b1600000| Untracked 
| 279|0x00000000b1700000, 0x00000000b1800000, 0x00000000b1800000|100%| O|  |TAMS 0x00000000b1800000, 0x00000000b1700000| Untracked 
| 280|0x00000000b1800000, 0x00000000b1900000, 0x00000000b1900000|100%| O|  |TAMS 0x00000000b1900000, 0x00000000b1800000| Untracked 
| 281|0x00000000b1900000, 0x00000000b1a00000, 0x00000000b1a00000|100%| O|  |TAMS 0x00000000b1a00000, 0x00000000b1900000| Untracked 
| 282|0x00000000b1a00000, 0x00000000b1b00000, 0x00000000b1b00000|100%| O|  |TAMS 0x00000000b1b00000, 0x00000000b1a00000| Untracked 
| 283|0x00000000b1b00000, 0x00000000b1c00000, 0x00000000b1c00000|100%| O|  |TAMS 0x00000000b1c00000, 0x00000000b1b00000| Untracked 
| 284|0x00000000b1c00000, 0x00000000b1d00000, 0x00000000b1d00000|100%| O|  |TAMS 0x00000000b1d00000, 0x00000000b1c00000| Untracked 
| 285|0x00000000b1d00000, 0x00000000b1e00000, 0x00000000b1e00000|100%| O|  |TAMS 0x00000000b1e00000, 0x00000000b1d00000| Untracked 
| 286|0x00000000b1e00000, 0x00000000b1f00000, 0x00000000b1f00000|100%| O|  |TAMS 0x00000000b1f00000, 0x00000000b1e00000| Untracked 
| 287|0x00000000b1f00000, 0x00000000b2000000, 0x00000000b2000000|100%| O|  |TAMS 0x00000000b2000000, 0x00000000b1f00000| Untracked 
| 288|0x00000000b2000000, 0x00000000b2100000, 0x00000000b2100000|100%| O|  |TAMS 0x00000000b2100000, 0x00000000b2000000| Untracked 
| 289|0x00000000b2100000, 0x00000000b2200000, 0x00000000b2200000|100%| O|  |TAMS 0x00000000b2200000, 0x00000000b2100000| Untracked 
| 290|0x00000000b2200000, 0x00000000b2300000, 0x00000000b2300000|100%| O|  |TAMS 0x00000000b2300000, 0x00000000b2200000| Untracked 
| 291|0x00000000b2300000, 0x00000000b2400000, 0x00000000b2400000|100%| O|  |TAMS 0x00000000b2400000, 0x00000000b2300000| Untracked 
| 292|0x00000000b2400000, 0x00000000b2500000, 0x00000000b2500000|100%| O|  |TAMS 0x00000000b2500000, 0x00000000b2400000| Untracked 
| 293|0x00000000b2500000, 0x00000000b2600000, 0x00000000b2600000|100%| O|  |TAMS 0x00000000b2600000, 0x00000000b2500000| Untracked 
| 294|0x00000000b2600000, 0x00000000b2700000, 0x00000000b2700000|100%| O|  |TAMS 0x00000000b2700000, 0x00000000b2600000| Untracked 
| 295|0x00000000b2700000, 0x00000000b2800000, 0x00000000b2800000|100%| O|  |TAMS 0x00000000b2800000, 0x00000000b2700000| Untracked 
| 296|0x00000000b2800000, 0x00000000b2900000, 0x00000000b2900000|100%| O|  |TAMS 0x00000000b2900000, 0x00000000b2800000| Untracked 
| 297|0x00000000b2900000, 0x00000000b2a00000, 0x00000000b2a00000|100%| O|  |TAMS 0x00000000b2a00000, 0x00000000b2900000| Untracked 
| 298|0x00000000b2a00000, 0x00000000b2b00000, 0x00000000b2b00000|100%| O|  |TAMS 0x00000000b2b00000, 0x00000000b2a00000| Untracked 
| 299|0x00000000b2b00000, 0x00000000b2c00000, 0x00000000b2c00000|100%| O|  |TAMS 0x00000000b2c00000, 0x00000000b2b00000| Untracked 
| 300|0x00000000b2c00000, 0x00000000b2d00000, 0x00000000b2d00000|100%| O|  |TAMS 0x00000000b2d00000, 0x00000000b2c00000| Untracked 
| 301|0x00000000b2d00000, 0x00000000b2e00000, 0x00000000b2e00000|100%| O|  |TAMS 0x00000000b2e00000, 0x00000000b2d00000| Untracked 
| 302|0x00000000b2e00000, 0x00000000b2f00000, 0x00000000b2f00000|100%| O|  |TAMS 0x00000000b2f00000, 0x00000000b2e00000| Untracked 
| 303|0x00000000b2f00000, 0x00000000b3000000, 0x00000000b3000000|100%| O|  |TAMS 0x00000000b3000000, 0x00000000b2f00000| Untracked 
| 304|0x00000000b3000000, 0x00000000b3100000, 0x00000000b3100000|100%| O|  |TAMS 0x00000000b3100000, 0x00000000b3000000| Untracked 
| 305|0x00000000b3100000, 0x00000000b3200000, 0x00000000b3200000|100%| O|  |TAMS 0x00000000b3200000, 0x00000000b3100000| Untracked 
| 306|0x00000000b3200000, 0x00000000b3300000, 0x00000000b3300000|100%| O|  |TAMS 0x00000000b3300000, 0x00000000b3200000| Untracked 
| 307|0x00000000b3300000, 0x00000000b3400000, 0x00000000b3400000|100%| O|  |TAMS 0x00000000b3400000, 0x00000000b3300000| Untracked 
| 308|0x00000000b3400000, 0x00000000b3500000, 0x00000000b3500000|100%| O|  |TAMS 0x00000000b3500000, 0x00000000b3400000| Untracked 
| 309|0x00000000b3500000, 0x00000000b3600000, 0x00000000b3600000|100%| O|  |TAMS 0x00000000b3600000, 0x00000000b3500000| Untracked 
| 310|0x00000000b3600000, 0x00000000b3700000, 0x00000000b3700000|100%| O|  |TAMS 0x00000000b3700000, 0x00000000b3600000| Untracked 
| 311|0x00000000b3700000, 0x00000000b3800000, 0x00000000b3800000|100%| O|  |TAMS 0x00000000b3800000, 0x00000000b3700000| Untracked 
| 312|0x00000000b3800000, 0x00000000b3900000, 0x00000000b3900000|100%| O|  |TAMS 0x00000000b3900000, 0x00000000b3800000| Untracked 
| 313|0x00000000b3900000, 0x00000000b3a00000, 0x00000000b3a00000|100%| O|  |TAMS 0x00000000b3a00000, 0x00000000b3900000| Untracked 
| 314|0x00000000b3a00000, 0x00000000b3b00000, 0x00000000b3b00000|100%| O|  |TAMS 0x00000000b3b00000, 0x00000000b3a00000| Untracked 
| 315|0x00000000b3b00000, 0x00000000b3c00000, 0x00000000b3c00000|100%| O|  |TAMS 0x00000000b3c00000, 0x00000000b3b00000| Untracked 
| 316|0x00000000b3c00000, 0x00000000b3d00000, 0x00000000b3d00000|100%| O|  |TAMS 0x00000000b3d00000, 0x00000000b3c00000| Untracked 
| 317|0x00000000b3d00000, 0x00000000b3e00000, 0x00000000b3e00000|100%| O|  |TAMS 0x00000000b3e00000, 0x00000000b3d00000| Untracked 
| 318|0x00000000b3e00000, 0x00000000b3f00000, 0x00000000b3f00000|100%| O|  |TAMS 0x00000000b3f00000, 0x00000000b3e00000| Untracked 
| 319|0x00000000b3f00000, 0x00000000b4000000, 0x00000000b4000000|100%| O|  |TAMS 0x00000000b4000000, 0x00000000b3f00000| Untracked 
| 320|0x00000000b4000000, 0x00000000b4100000, 0x00000000b4100000|100%| O|  |TAMS 0x00000000b4100000, 0x00000000b4000000| Untracked 
| 321|0x00000000b4100000, 0x00000000b4200000, 0x00000000b4200000|100%| O|  |TAMS 0x00000000b4200000, 0x00000000b4100000| Untracked 
| 322|0x00000000b4200000, 0x00000000b4300000, 0x00000000b4300000|100%| O|  |TAMS 0x00000000b4300000, 0x00000000b4200000| Untracked 
| 323|0x00000000b4300000, 0x00000000b4400000, 0x00000000b4400000|100%| O|  |TAMS 0x00000000b4400000, 0x00000000b4300000| Untracked 
| 324|0x00000000b4400000, 0x00000000b4500000, 0x00000000b4500000|100%| O|  |TAMS 0x00000000b4500000, 0x00000000b4400000| Untracked 
| 325|0x00000000b4500000, 0x00000000b4600000, 0x00000000b4600000|100%| O|  |TAMS 0x00000000b4600000, 0x00000000b4500000| Untracked 
| 326|0x00000000b4600000, 0x00000000b4700000, 0x00000000b4700000|100%| O|  |TAMS 0x00000000b4700000, 0x00000000b4600000| Untracked 
| 327|0x00000000b4700000, 0x00000000b4800000, 0x00000000b4800000|100%| O|  |TAMS 0x00000000b4800000, 0x00000000b4700000| Untracked 
| 328|0x00000000b4800000, 0x00000000b4900000, 0x00000000b4900000|100%| O|  |TAMS 0x00000000b4900000, 0x00000000b4800000| Untracked 
| 329|0x00000000b4900000, 0x00000000b4a00000, 0x00000000b4a00000|100%| O|  |TAMS 0x00000000b4a00000, 0x00000000b4900000| Untracked 
| 330|0x00000000b4a00000, 0x00000000b4b00000, 0x00000000b4b00000|100%| O|  |TAMS 0x00000000b4b00000, 0x00000000b4a00000| Untracked 
| 331|0x00000000b4b00000, 0x00000000b4c00000, 0x00000000b4c00000|100%| O|  |TAMS 0x00000000b4c00000, 0x00000000b4b00000| Untracked 
| 332|0x00000000b4c00000, 0x00000000b4d00000, 0x00000000b4d00000|100%| O|  |TAMS 0x00000000b4d00000, 0x00000000b4c00000| Untracked 
| 333|0x00000000b4d00000, 0x00000000b4e00000, 0x00000000b4e00000|100%| O|  |TAMS 0x00000000b4e00000, 0x00000000b4d00000| Untracked 
| 334|0x00000000b4e00000, 0x00000000b4f00000, 0x00000000b4f00000|100%| O|  |TAMS 0x00000000b4f00000, 0x00000000b4e00000| Untracked 
| 335|0x00000000b4f00000, 0x00000000b5000000, 0x00000000b5000000|100%| O|  |TAMS 0x00000000b5000000, 0x00000000b4f00000| Untracked 
| 336|0x00000000b5000000, 0x00000000b5100000, 0x00000000b5100000|100%| O|  |TAMS 0x00000000b5100000, 0x00000000b5000000| Untracked 
| 337|0x00000000b5100000, 0x00000000b5200000, 0x00000000b5200000|100%| O|  |TAMS 0x00000000b5200000, 0x00000000b5100000| Untracked 
| 338|0x00000000b5200000, 0x00000000b5300000, 0x00000000b5300000|100%| O|  |TAMS 0x00000000b5300000, 0x00000000b5200000| Untracked 
| 339|0x00000000b5300000, 0x00000000b5400000, 0x00000000b5400000|100%| O|  |TAMS 0x00000000b5400000, 0x00000000b5300000| Untracked 
| 340|0x00000000b5400000, 0x00000000b5500000, 0x00000000b5500000|100%| O|  |TAMS 0x00000000b5500000, 0x00000000b5400000| Untracked 
| 341|0x00000000b5500000, 0x00000000b5600000, 0x00000000b5600000|100%| O|  |TAMS 0x00000000b5580000, 0x00000000b5500000| Untracked 
| 342|0x00000000b5600000, 0x00000000b5700000, 0x00000000b5700000|100%| O|  |TAMS 0x00000000b5600000, 0x00000000b5600000| Untracked 
| 343|0x00000000b5700000, 0x00000000b5800000, 0x00000000b5800000|100%| O|  |TAMS 0x00000000b5700000, 0x00000000b5700000| Untracked 
| 344|0x00000000b5800000, 0x00000000b5900000, 0x00000000b5900000|100%| O|  |TAMS 0x00000000b5800000, 0x00000000b5800000| Untracked 
| 345|0x00000000b5900000, 0x00000000b5a00000, 0x00000000b5a00000|100%| O|  |TAMS 0x00000000b5900000, 0x00000000b5900000| Untracked 
| 346|0x00000000b5a00000, 0x00000000b5b00000, 0x00000000b5b00000|100%| O|  |TAMS 0x00000000b5a00000, 0x00000000b5a00000| Untracked 
| 347|0x00000000b5b00000, 0x00000000b5c00000, 0x00000000b5c00000|100%| O|  |TAMS 0x00000000b5b00000, 0x00000000b5b00000| Untracked 
| 348|0x00000000b5c00000, 0x00000000b5d00000, 0x00000000b5d00000|100%| O|  |TAMS 0x00000000b5c00000, 0x00000000b5c00000| Untracked 
| 349|0x00000000b5d00000, 0x00000000b5e00000, 0x00000000b5e00000|100%| O|  |TAMS 0x00000000b5d00000, 0x00000000b5d00000| Untracked 
| 350|0x00000000b5e00000, 0x00000000b5f00000, 0x00000000b5f00000|100%| O|  |TAMS 0x00000000b5e00000, 0x00000000b5e00000| Untracked 
| 351|0x00000000b5f00000, 0x00000000b6000000, 0x00000000b6000000|100%| O|  |TAMS 0x00000000b5f00000, 0x00000000b5f00000| Untracked 
| 352|0x00000000b6000000, 0x00000000b6100000, 0x00000000b6100000|100%| O|  |TAMS 0x00000000b6000000, 0x00000000b6000000| Untracked 
| 353|0x00000000b6100000, 0x00000000b6200000, 0x00000000b6200000|100%| O|  |TAMS 0x00000000b6100000, 0x00000000b6100000| Untracked 
| 354|0x00000000b6200000, 0x00000000b6300000, 0x00000000b6300000|100%| O|  |TAMS 0x00000000b6200000, 0x00000000b6200000| Untracked 
| 355|0x00000000b6300000, 0x00000000b6400000, 0x00000000b6400000|100%| O|  |TAMS 0x00000000b6300000, 0x00000000b6300000| Untracked 
| 356|0x00000000b6400000, 0x00000000b6500000, 0x00000000b6500000|100%| O|  |TAMS 0x00000000b6400000, 0x00000000b6400000| Untracked 
| 357|0x00000000b6500000, 0x00000000b6600000, 0x00000000b6600000|100%| O|  |TAMS 0x00000000b6500000, 0x00000000b6500000| Untracked 
| 358|0x00000000b6600000, 0x00000000b6700000, 0x00000000b6700000|100%| O|  |TAMS 0x00000000b6600000, 0x00000000b6600000| Untracked 
| 359|0x00000000b6700000, 0x00000000b6800000, 0x00000000b6800000|100%| O|  |TAMS 0x00000000b6700000, 0x00000000b6700000| Untracked 
| 360|0x00000000b6800000, 0x00000000b6900000, 0x00000000b6900000|100%| O|  |TAMS 0x00000000b6800000, 0x00000000b6800000| Untracked 
| 361|0x00000000b6900000, 0x00000000b6a00000, 0x00000000b6a00000|100%| O|  |TAMS 0x00000000b6900000, 0x00000000b6900000| Untracked 
| 362|0x00000000b6a00000, 0x00000000b6b00000, 0x00000000b6b00000|100%| O|  |TAMS 0x00000000b6a00000, 0x00000000b6a00000| Untracked 
| 363|0x00000000b6b00000, 0x00000000b6c00000, 0x00000000b6c00000|100%| O|  |TAMS 0x00000000b6b00000, 0x00000000b6b00000| Untracked 
| 364|0x00000000b6c00000, 0x00000000b6d00000, 0x00000000b6d00000|100%| O|  |TAMS 0x00000000b6c00000, 0x00000000b6c00000| Untracked 
| 365|0x00000000b6d00000, 0x00000000b6e00000, 0x00000000b6e00000|100%| O|  |TAMS 0x00000000b6d00000, 0x00000000b6d00000| Untracked 
| 366|0x00000000b6e00000, 0x00000000b6f00000, 0x00000000b6f00000|100%| O|  |TAMS 0x00000000b6e00000, 0x00000000b6e00000| Untracked 
| 367|0x00000000b6f00000, 0x00000000b7000000, 0x00000000b7000000|100%| O|  |TAMS 0x00000000b6f00000, 0x00000000b6f00000| Untracked 
| 368|0x00000000b7000000, 0x00000000b7100000, 0x00000000b7100000|100%| O|  |TAMS 0x00000000b7000000, 0x00000000b7000000| Untracked 
| 369|0x00000000b7100000, 0x00000000b7200000, 0x00000000b7200000|100%| O|  |TAMS 0x00000000b7100000, 0x00000000b7100000| Untracked 
| 370|0x00000000b7200000, 0x00000000b7300000, 0x00000000b7300000|100%| O|  |TAMS 0x00000000b7200000, 0x00000000b7200000| Untracked 
| 371|0x00000000b7300000, 0x00000000b7400000, 0x00000000b7400000|100%| O|  |TAMS 0x00000000b7300000, 0x00000000b7300000| Untracked 
| 372|0x00000000b7400000, 0x00000000b7500000, 0x00000000b7500000|100%| O|  |TAMS 0x00000000b7400000, 0x00000000b7400000| Untracked 
| 373|0x00000000b7500000, 0x00000000b7600000, 0x00000000b7600000|100%| O|  |TAMS 0x00000000b7500000, 0x00000000b7500000| Untracked 
| 374|0x00000000b7600000, 0x00000000b7700000, 0x00000000b7700000|100%| O|  |TAMS 0x00000000b7600000, 0x00000000b7600000| Untracked 
| 375|0x00000000b7700000, 0x00000000b7800000, 0x00000000b7800000|100%| O|  |TAMS 0x00000000b7700000, 0x00000000b7700000| Untracked 
| 376|0x00000000b7800000, 0x00000000b7900000, 0x00000000b7900000|100%| O|  |TAMS 0x00000000b7800000, 0x00000000b7800000| Untracked 
| 377|0x00000000b7900000, 0x00000000b7a00000, 0x00000000b7a00000|100%| O|  |TAMS 0x00000000b7900000, 0x00000000b7900000| Untracked 
| 378|0x00000000b7a00000, 0x00000000b7b00000, 0x00000000b7b00000|100%| O|  |TAMS 0x00000000b7a00000, 0x00000000b7a00000| Untracked 
| 379|0x00000000b7b00000, 0x00000000b7c00000, 0x00000000b7c00000|100%| O|  |TAMS 0x00000000b7b00000, 0x00000000b7b00000| Untracked 
| 380|0x00000000b7c00000, 0x00000000b7d00000, 0x00000000b7d00000|100%| O|  |TAMS 0x00000000b7c00000, 0x00000000b7c00000| Untracked 
| 381|0x00000000b7d00000, 0x00000000b7e00000, 0x00000000b7e00000|100%| O|  |TAMS 0x00000000b7d00000, 0x00000000b7d00000| Untracked 
| 382|0x00000000b7e00000, 0x00000000b7f00000, 0x00000000b7f00000|100%| O|  |TAMS 0x00000000b7e00000, 0x00000000b7e00000| Untracked 
| 383|0x00000000b7f00000, 0x00000000b8000000, 0x00000000b8000000|100%| O|  |TAMS 0x00000000b7f00000, 0x00000000b7f00000| Untracked 
| 384|0x00000000b8000000, 0x00000000b8100000, 0x00000000b8100000|100%| O|  |TAMS 0x00000000b8000000, 0x00000000b8000000| Untracked 
| 385|0x00000000b8100000, 0x00000000b8200000, 0x00000000b8200000|100%| O|  |TAMS 0x00000000b8100000, 0x00000000b8100000| Untracked 
| 386|0x00000000b8200000, 0x00000000b8300000, 0x00000000b8300000|100%| O|  |TAMS 0x00000000b8200000, 0x00000000b8200000| Untracked 
| 387|0x00000000b8300000, 0x00000000b8300000, 0x00000000b8400000|  0%| F|  |TAMS 0x00000000b8300000, 0x00000000b8300000| Untracked 
| 388|0x00000000b8400000, 0x00000000b8400000, 0x00000000b8500000|  0%| F|  |TAMS 0x00000000b8400000, 0x00000000b8400000| Untracked 
| 389|0x00000000b8500000, 0x00000000b8500000, 0x00000000b8600000|  0%| F|  |TAMS 0x00000000b8500000, 0x00000000b8500000| Untracked 
| 390|0x00000000b8600000, 0x00000000b8600000, 0x00000000b8700000|  0%| F|  |TAMS 0x00000000b8600000, 0x00000000b8600000| Untracked 
| 391|0x00000000b8700000, 0x00000000b8700000, 0x00000000b8800000|  0%| F|  |TAMS 0x00000000b8700000, 0x00000000b8700000| Untracked 
| 392|0x00000000b8800000, 0x00000000b8800000, 0x00000000b8900000|  0%| F|  |TAMS 0x00000000b8800000, 0x00000000b8800000| Untracked 
| 393|0x00000000b8900000, 0x00000000b8900000, 0x00000000b8a00000|  0%| F|  |TAMS 0x00000000b8900000, 0x00000000b8900000| Untracked 
| 394|0x00000000b8a00000, 0x00000000b8a00000, 0x00000000b8b00000|  0%| F|  |TAMS 0x00000000b8a00000, 0x00000000b8a00000| Untracked 
| 395|0x00000000b8b00000, 0x00000000b8b00000, 0x00000000b8c00000|  0%| F|  |TAMS 0x00000000b8b00000, 0x00000000b8b00000| Untracked 
| 396|0x00000000b8c00000, 0x00000000b8c00000, 0x00000000b8d00000|  0%| F|  |TAMS 0x00000000b8c00000, 0x00000000b8c00000| Untracked 
| 397|0x00000000b8d00000, 0x00000000b8d00000, 0x00000000b8e00000|  0%| F|  |TAMS 0x00000000b8d00000, 0x00000000b8d00000| Untracked 
| 398|0x00000000b8e00000, 0x00000000b8e00000, 0x00000000b8f00000|  0%| F|  |TAMS 0x00000000b8e00000, 0x00000000b8e00000| Untracked 
| 399|0x00000000b8f00000, 0x00000000b8f00000, 0x00000000b9000000|  0%| F|  |TAMS 0x00000000b8f00000, 0x00000000b8f00000| Untracked 
| 400|0x00000000b9000000, 0x00000000b9000000, 0x00000000b9100000|  0%| F|  |TAMS 0x00000000b9000000, 0x00000000b9000000| Untracked 
| 401|0x00000000b9100000, 0x00000000b9100000, 0x00000000b9200000|  0%| F|  |TAMS 0x00000000b9100000, 0x00000000b9100000| Untracked 
| 402|0x00000000b9200000, 0x00000000b9200000, 0x00000000b9300000|  0%| F|  |TAMS 0x00000000b9200000, 0x00000000b9200000| Untracked 
| 403|0x00000000b9300000, 0x00000000b9300000, 0x00000000b9400000|  0%| F|  |TAMS 0x00000000b9300000, 0x00000000b9300000| Untracked 
| 404|0x00000000b9400000, 0x00000000b9400000, 0x00000000b9500000|  0%| F|  |TAMS 0x00000000b9400000, 0x00000000b9400000| Untracked 
| 405|0x00000000b9500000, 0x00000000b9500000, 0x00000000b9600000|  0%| F|  |TAMS 0x00000000b9500000, 0x00000000b9500000| Untracked 
| 406|0x00000000b9600000, 0x00000000b9600000, 0x00000000b9700000|  0%| F|  |TAMS 0x00000000b9600000, 0x00000000b9600000| Untracked 
| 407|0x00000000b9700000, 0x00000000b9700000, 0x00000000b9800000|  0%| F|  |TAMS 0x00000000b9700000, 0x00000000b9700000| Untracked 
| 408|0x00000000b9800000, 0x00000000b9800000, 0x00000000b9900000|  0%| F|  |TAMS 0x00000000b9800000, 0x00000000b9800000| Untracked 
| 409|0x00000000b9900000, 0x00000000b9900000, 0x00000000b9a00000|  0%| F|  |TAMS 0x00000000b9900000, 0x00000000b9900000| Untracked 
| 410|0x00000000b9a00000, 0x00000000b9a00000, 0x00000000b9b00000|  0%| F|  |TAMS 0x00000000b9a00000, 0x00000000b9a00000| Untracked 
| 411|0x00000000b9b00000, 0x00000000b9b00000, 0x00000000b9c00000|  0%| F|  |TAMS 0x00000000b9b00000, 0x00000000b9b00000| Untracked 
| 412|0x00000000b9c00000, 0x00000000b9c00000, 0x00000000b9d00000|  0%| F|  |TAMS 0x00000000b9c00000, 0x00000000b9c00000| Untracked 
| 413|0x00000000b9d00000, 0x00000000b9d00000, 0x00000000b9e00000|  0%| F|  |TAMS 0x00000000b9d00000, 0x00000000b9d00000| Untracked 
| 414|0x00000000b9e00000, 0x00000000b9e00000, 0x00000000b9f00000|  0%| F|  |TAMS 0x00000000b9e00000, 0x00000000b9e00000| Untracked 
| 415|0x00000000b9f00000, 0x00000000b9f00000, 0x00000000ba000000|  0%| F|  |TAMS 0x00000000b9f00000, 0x00000000b9f00000| Untracked 
| 416|0x00000000ba000000, 0x00000000ba000000, 0x00000000ba100000|  0%| F|  |TAMS 0x00000000ba000000, 0x00000000ba000000| Untracked 
| 417|0x00000000ba100000, 0x00000000ba100000, 0x00000000ba200000|  0%| F|  |TAMS 0x00000000ba100000, 0x00000000ba100000| Untracked 
| 418|0x00000000ba200000, 0x00000000ba200000, 0x00000000ba300000|  0%| F|  |TAMS 0x00000000ba200000, 0x00000000ba200000| Untracked 
| 419|0x00000000ba300000, 0x00000000ba300000, 0x00000000ba400000|  0%| F|  |TAMS 0x00000000ba300000, 0x00000000ba300000| Untracked 
| 420|0x00000000ba400000, 0x00000000ba400000, 0x00000000ba500000|  0%| F|  |TAMS 0x00000000ba400000, 0x00000000ba400000| Untracked 
| 421|0x00000000ba500000, 0x00000000ba500000, 0x00000000ba600000|  0%| F|  |TAMS 0x00000000ba500000, 0x00000000ba500000| Untracked 
| 422|0x00000000ba600000, 0x00000000ba600000, 0x00000000ba700000|  0%| F|  |TAMS 0x00000000ba600000, 0x00000000ba600000| Untracked 
| 423|0x00000000ba700000, 0x00000000ba700000, 0x00000000ba800000|  0%| F|  |TAMS 0x00000000ba700000, 0x00000000ba700000| Untracked 
| 424|0x00000000ba800000, 0x00000000ba800000, 0x00000000ba900000|  0%| F|  |TAMS 0x00000000ba800000, 0x00000000ba800000| Untracked 
| 425|0x00000000ba900000, 0x00000000ba900000, 0x00000000baa00000|  0%| F|  |TAMS 0x00000000ba900000, 0x00000000ba900000| Untracked 
| 426|0x00000000baa00000, 0x00000000baa00000, 0x00000000bab00000|  0%| F|  |TAMS 0x00000000baa00000, 0x00000000baa00000| Untracked 
| 427|0x00000000bab00000, 0x00000000bab00000, 0x00000000bac00000|  0%| F|  |TAMS 0x00000000bab00000, 0x00000000bab00000| Untracked 
| 428|0x00000000bac00000, 0x00000000bac00000, 0x00000000bad00000|  0%| F|  |TAMS 0x00000000bac00000, 0x00000000bac00000| Untracked 
| 429|0x00000000bad00000, 0x00000000bad00000, 0x00000000bae00000|  0%| F|  |TAMS 0x00000000bad00000, 0x00000000bad00000| Untracked 
| 430|0x00000000bae00000, 0x00000000bae00000, 0x00000000baf00000|  0%| F|  |TAMS 0x00000000bae00000, 0x00000000bae00000| Untracked 
| 431|0x00000000baf00000, 0x00000000baf00000, 0x00000000bb000000|  0%| F|  |TAMS 0x00000000baf00000, 0x00000000baf00000| Untracked 
| 432|0x00000000bb000000, 0x00000000bb000000, 0x00000000bb100000|  0%| F|  |TAMS 0x00000000bb000000, 0x00000000bb000000| Untracked 
| 433|0x00000000bb100000, 0x00000000bb100000, 0x00000000bb200000|  0%| F|  |TAMS 0x00000000bb100000, 0x00000000bb100000| Untracked 
| 434|0x00000000bb200000, 0x00000000bb200000, 0x00000000bb300000|  0%| F|  |TAMS 0x00000000bb200000, 0x00000000bb200000| Untracked 
| 435|0x00000000bb300000, 0x00000000bb300000, 0x00000000bb400000|  0%| F|  |TAMS 0x00000000bb300000, 0x00000000bb300000| Untracked 
| 436|0x00000000bb400000, 0x00000000bb400000, 0x00000000bb500000|  0%| F|  |TAMS 0x00000000bb400000, 0x00000000bb400000| Untracked 
| 437|0x00000000bb500000, 0x00000000bb500000, 0x00000000bb600000|  0%| F|  |TAMS 0x00000000bb500000, 0x00000000bb500000| Untracked 
| 438|0x00000000bb600000, 0x00000000bb600000, 0x00000000bb700000|  0%| F|  |TAMS 0x00000000bb600000, 0x00000000bb600000| Untracked 
| 439|0x00000000bb700000, 0x00000000bb700000, 0x00000000bb800000|  0%| F|  |TAMS 0x00000000bb700000, 0x00000000bb700000| Untracked 
| 440|0x00000000bb800000, 0x00000000bb800000, 0x00000000bb900000|  0%| F|  |TAMS 0x00000000bb800000, 0x00000000bb800000| Untracked 
| 441|0x00000000bb900000, 0x00000000bb900000, 0x00000000bba00000|  0%| F|  |TAMS 0x00000000bb900000, 0x00000000bb900000| Untracked 
| 442|0x00000000bba00000, 0x00000000bba00000, 0x00000000bbb00000|  0%| F|  |TAMS 0x00000000bba00000, 0x00000000bba00000| Untracked 
| 443|0x00000000bbb00000, 0x00000000bbb00000, 0x00000000bbc00000|  0%| F|  |TAMS 0x00000000bbb00000, 0x00000000bbb00000| Untracked 
| 444|0x00000000bbc00000, 0x00000000bbc00000, 0x00000000bbd00000|  0%| F|  |TAMS 0x00000000bbc00000, 0x00000000bbc00000| Untracked 
| 445|0x00000000bbd00000, 0x00000000bbd00000, 0x00000000bbe00000|  0%| F|  |TAMS 0x00000000bbd00000, 0x00000000bbd00000| Untracked 
| 446|0x00000000bbe00000, 0x00000000bbe00000, 0x00000000bbf00000|  0%| F|  |TAMS 0x00000000bbe00000, 0x00000000bbe00000| Untracked 
| 447|0x00000000bbf00000, 0x00000000bbf00000, 0x00000000bc000000|  0%| F|  |TAMS 0x00000000bbf00000, 0x00000000bbf00000| Untracked 
| 448|0x00000000bc000000, 0x00000000bc000000, 0x00000000bc100000|  0%| F|  |TAMS 0x00000000bc000000, 0x00000000bc000000| Untracked 
| 449|0x00000000bc100000, 0x00000000bc100000, 0x00000000bc200000|  0%| F|  |TAMS 0x00000000bc100000, 0x00000000bc100000| Untracked 
| 450|0x00000000bc200000, 0x00000000bc200000, 0x00000000bc300000|  0%| F|  |TAMS 0x00000000bc200000, 0x00000000bc200000| Untracked 
| 451|0x00000000bc300000, 0x00000000bc300000, 0x00000000bc400000|  0%| F|  |TAMS 0x00000000bc300000, 0x00000000bc300000| Untracked 
| 452|0x00000000bc400000, 0x00000000bc400000, 0x00000000bc500000|  0%| F|  |TAMS 0x00000000bc400000, 0x00000000bc400000| Untracked 
| 453|0x00000000bc500000, 0x00000000bc500000, 0x00000000bc600000|  0%| F|  |TAMS 0x00000000bc500000, 0x00000000bc500000| Untracked 
| 454|0x00000000bc600000, 0x00000000bc600000, 0x00000000bc700000|  0%| F|  |TAMS 0x00000000bc600000, 0x00000000bc600000| Untracked 
| 455|0x00000000bc700000, 0x00000000bc700000, 0x00000000bc800000|  0%| F|  |TAMS 0x00000000bc700000, 0x00000000bc700000| Untracked 
| 456|0x00000000bc800000, 0x00000000bc800000, 0x00000000bc900000|  0%| F|  |TAMS 0x00000000bc800000, 0x00000000bc800000| Untracked 
| 457|0x00000000bc900000, 0x00000000bc900000, 0x00000000bca00000|  0%| F|  |TAMS 0x00000000bc900000, 0x00000000bc900000| Untracked 
| 458|0x00000000bca00000, 0x00000000bca00000, 0x00000000bcb00000|  0%| F|  |TAMS 0x00000000bca00000, 0x00000000bca00000| Untracked 
| 459|0x00000000bcb00000, 0x00000000bcb00000, 0x00000000bcc00000|  0%| F|  |TAMS 0x00000000bcb00000, 0x00000000bcb00000| Untracked 
| 460|0x00000000bcc00000, 0x00000000bcc00000, 0x00000000bcd00000|  0%| F|  |TAMS 0x00000000bcc00000, 0x00000000bcc00000| Untracked 
| 461|0x00000000bcd00000, 0x00000000bcd00000, 0x00000000bce00000|  0%| F|  |TAMS 0x00000000bcd00000, 0x00000000bcd00000| Untracked 
| 462|0x00000000bce00000, 0x00000000bce00000, 0x00000000bcf00000|  0%| F|  |TAMS 0x00000000bce00000, 0x00000000bce00000| Untracked 
| 463|0x00000000bcf00000, 0x00000000bcf00000, 0x00000000bd000000|  0%| F|  |TAMS 0x00000000bcf00000, 0x00000000bcf00000| Untracked 
| 464|0x00000000bd000000, 0x00000000bd000000, 0x00000000bd100000|  0%| F|  |TAMS 0x00000000bd000000, 0x00000000bd000000| Untracked 
| 465|0x00000000bd100000, 0x00000000bd100000, 0x00000000bd200000|  0%| F|  |TAMS 0x00000000bd100000, 0x00000000bd100000| Untracked 
| 466|0x00000000bd200000, 0x00000000bd200000, 0x00000000bd300000|  0%| F|  |TAMS 0x00000000bd200000, 0x00000000bd200000| Untracked 
| 467|0x00000000bd300000, 0x00000000bd300000, 0x00000000bd400000|  0%| F|  |TAMS 0x00000000bd300000, 0x00000000bd300000| Untracked 
| 468|0x00000000bd400000, 0x00000000bd400000, 0x00000000bd500000|  0%| F|  |TAMS 0x00000000bd400000, 0x00000000bd400000| Untracked 
| 469|0x00000000bd500000, 0x00000000bd500000, 0x00000000bd600000|  0%| F|  |TAMS 0x00000000bd500000, 0x00000000bd500000| Untracked 
| 470|0x00000000bd600000, 0x00000000bd600000, 0x00000000bd700000|  0%| F|  |TAMS 0x00000000bd600000, 0x00000000bd600000| Untracked 
| 471|0x00000000bd700000, 0x00000000bd700000, 0x00000000bd800000|  0%| F|  |TAMS 0x00000000bd700000, 0x00000000bd700000| Untracked 
| 472|0x00000000bd800000, 0x00000000bd800000, 0x00000000bd900000|  0%| F|  |TAMS 0x00000000bd800000, 0x00000000bd800000| Untracked 
| 473|0x00000000bd900000, 0x00000000bd900000, 0x00000000bda00000|  0%| F|  |TAMS 0x00000000bd900000, 0x00000000bd900000| Untracked 
| 474|0x00000000bda00000, 0x00000000bda00000, 0x00000000bdb00000|  0%| F|  |TAMS 0x00000000bda00000, 0x00000000bda00000| Untracked 
| 475|0x00000000bdb00000, 0x00000000bdb00000, 0x00000000bdc00000|  0%| F|  |TAMS 0x00000000bdb00000, 0x00000000bdb00000| Untracked 
| 476|0x00000000bdc00000, 0x00000000bdc00000, 0x00000000bdd00000|  0%| F|  |TAMS 0x00000000bdc00000, 0x00000000bdc00000| Untracked 
| 477|0x00000000bdd00000, 0x00000000bdd00000, 0x00000000bde00000|  0%| F|  |TAMS 0x00000000bdd00000, 0x00000000bdd00000| Untracked 
| 478|0x00000000bde00000, 0x00000000bde00000, 0x00000000bdf00000|  0%| F|  |TAMS 0x00000000bde00000, 0x00000000bde00000| Untracked 
| 479|0x00000000bdf00000, 0x00000000bdf00000, 0x00000000be000000|  0%| F|  |TAMS 0x00000000bdf00000, 0x00000000bdf00000| Untracked 
| 480|0x00000000be000000, 0x00000000be000000, 0x00000000be100000|  0%| F|  |TAMS 0x00000000be000000, 0x00000000be000000| Untracked 
| 481|0x00000000be100000, 0x00000000be100000, 0x00000000be200000|  0%| F|  |TAMS 0x00000000be100000, 0x00000000be100000| Untracked 
| 482|0x00000000be200000, 0x00000000be200000, 0x00000000be300000|  0%| F|  |TAMS 0x00000000be200000, 0x00000000be200000| Untracked 
| 483|0x00000000be300000, 0x00000000be300000, 0x00000000be400000|  0%| F|  |TAMS 0x00000000be300000, 0x00000000be300000| Untracked 
| 484|0x00000000be400000, 0x00000000be400000, 0x00000000be500000|  0%| F|  |TAMS 0x00000000be400000, 0x00000000be400000| Untracked 
| 485|0x00000000be500000, 0x00000000be500000, 0x00000000be600000|  0%| F|  |TAMS 0x00000000be500000, 0x00000000be500000| Untracked 
| 486|0x00000000be600000, 0x00000000be600000, 0x00000000be700000|  0%| F|  |TAMS 0x00000000be600000, 0x00000000be600000| Untracked 
| 487|0x00000000be700000, 0x00000000be700000, 0x00000000be800000|  0%| F|  |TAMS 0x00000000be700000, 0x00000000be700000| Untracked 
| 488|0x00000000be800000, 0x00000000be800000, 0x00000000be900000|  0%| F|  |TAMS 0x00000000be800000, 0x00000000be800000| Untracked 
| 489|0x00000000be900000, 0x00000000be900000, 0x00000000bea00000|  0%| F|  |TAMS 0x00000000be900000, 0x00000000be900000| Untracked 
| 490|0x00000000bea00000, 0x00000000bea00000, 0x00000000beb00000|  0%| F|  |TAMS 0x00000000bea00000, 0x00000000bea00000| Untracked 
| 491|0x00000000beb00000, 0x00000000beb00000, 0x00000000bec00000|  0%| F|  |TAMS 0x00000000beb00000, 0x00000000beb00000| Untracked 
| 492|0x00000000bec00000, 0x00000000bec00000, 0x00000000bed00000|  0%| F|  |TAMS 0x00000000bec00000, 0x00000000bec00000| Untracked 
| 493|0x00000000bed00000, 0x00000000bed00000, 0x00000000bee00000|  0%| F|  |TAMS 0x00000000bed00000, 0x00000000bed00000| Untracked 
| 494|0x00000000bee00000, 0x00000000bee00000, 0x00000000bef00000|  0%| F|  |TAMS 0x00000000bee00000, 0x00000000bee00000| Untracked 
| 495|0x00000000bef00000, 0x00000000bef00000, 0x00000000bf000000|  0%| F|  |TAMS 0x00000000bef00000, 0x00000000bef00000| Untracked 
| 496|0x00000000bf000000, 0x00000000bf000000, 0x00000000bf100000|  0%| F|  |TAMS 0x00000000bf000000, 0x00000000bf000000| Untracked 
| 497|0x00000000bf100000, 0x00000000bf100000, 0x00000000bf200000|  0%| F|  |TAMS 0x00000000bf100000, 0x00000000bf100000| Untracked 
| 498|0x00000000bf200000, 0x00000000bf200000, 0x00000000bf300000|  0%| F|  |TAMS 0x00000000bf200000, 0x00000000bf200000| Untracked 
| 499|0x00000000bf300000, 0x00000000bf300000, 0x00000000bf400000|  0%| F|  |TAMS 0x00000000bf300000, 0x00000000bf300000| Untracked 
| 500|0x00000000bf400000, 0x00000000bf400000, 0x00000000bf500000|  0%| F|  |TAMS 0x00000000bf400000, 0x00000000bf400000| Untracked 
| 501|0x00000000bf500000, 0x00000000bf500000, 0x00000000bf600000|  0%| F|  |TAMS 0x00000000bf500000, 0x00000000bf500000| Untracked 
| 502|0x00000000bf600000, 0x00000000bf600000, 0x00000000bf700000|  0%| F|  |TAMS 0x00000000bf600000, 0x00000000bf600000| Untracked 
| 503|0x00000000bf700000, 0x00000000bf700000, 0x00000000bf800000|  0%| F|  |TAMS 0x00000000bf700000, 0x00000000bf700000| Untracked 
| 504|0x00000000bf800000, 0x00000000bf800000, 0x00000000bf900000|  0%| F|  |TAMS 0x00000000bf800000, 0x00000000bf800000| Untracked 
| 505|0x00000000bf900000, 0x00000000bf900000, 0x00000000bfa00000|  0%| F|  |TAMS 0x00000000bf900000, 0x00000000bf900000| Untracked 
| 506|0x00000000bfa00000, 0x00000000bfa00000, 0x00000000bfb00000|  0%| F|  |TAMS 0x00000000bfa00000, 0x00000000bfa00000| Untracked 
| 507|0x00000000bfb00000, 0x00000000bfb00000, 0x00000000bfc00000|  0%| F|  |TAMS 0x00000000bfb00000, 0x00000000bfb00000| Untracked 
| 508|0x00000000bfc00000, 0x00000000bfc00000, 0x00000000bfd00000|  0%| F|  |TAMS 0x00000000bfc00000, 0x00000000bfc00000| Untracked 
| 509|0x00000000bfd00000, 0x00000000bfd00000, 0x00000000bfe00000|  0%| F|  |TAMS 0x00000000bfd00000, 0x00000000bfd00000| Untracked 
| 510|0x00000000bfe00000, 0x00000000bfe00000, 0x00000000bff00000|  0%| F|  |TAMS 0x00000000bfe00000, 0x00000000bfe00000| Untracked 
| 511|0x00000000bff00000, 0x00000000c0000000, 0x00000000c0000000|100%| S|CS|TAMS 0x00000000bff00000, 0x00000000bff00000| Complete 
| 512|0x00000000c0000000, 0x00000000c0100000, 0x00000000c0100000|100%| S|CS|TAMS 0x00000000c0000000, 0x00000000c0000000| Complete 
| 513|0x00000000c0100000, 0x00000000c0100000, 0x00000000c0200000|  0%| F|  |TAMS 0x00000000c0100000, 0x00000000c0100000| Untracked 
| 514|0x00000000c0200000, 0x00000000c0200000, 0x00000000c0300000|  0%| F|  |TAMS 0x00000000c0200000, 0x00000000c0200000| Untracked 
| 515|0x00000000c0300000, 0x00000000c0300000, 0x00000000c0400000|  0%| F|  |TAMS 0x00000000c0300000, 0x00000000c0300000| Untracked 
| 516|0x00000000c0400000, 0x00000000c0400000, 0x00000000c0500000|  0%| F|  |TAMS 0x00000000c0400000, 0x00000000c0400000| Untracked 
| 517|0x00000000c0500000, 0x00000000c0500000, 0x00000000c0600000|  0%| F|  |TAMS 0x00000000c0500000, 0x00000000c0500000| Untracked 
| 518|0x00000000c0600000, 0x00000000c0600000, 0x00000000c0700000|  0%| F|  |TAMS 0x00000000c0600000, 0x00000000c0600000| Untracked 
| 519|0x00000000c0700000, 0x00000000c0700000, 0x00000000c0800000|  0%| F|  |TAMS 0x00000000c0700000, 0x00000000c0700000| Untracked 
| 520|0x00000000c0800000, 0x00000000c0800000, 0x00000000c0900000|  0%| F|  |TAMS 0x00000000c0800000, 0x00000000c0800000| Untracked 
| 521|0x00000000c0900000, 0x00000000c0900000, 0x00000000c0a00000|  0%| F|  |TAMS 0x00000000c0900000, 0x00000000c0900000| Untracked 
| 522|0x00000000c0a00000, 0x00000000c0a00000, 0x00000000c0b00000|  0%| F|  |TAMS 0x00000000c0a00000, 0x00000000c0a00000| Untracked 
| 523|0x00000000c0b00000, 0x00000000c0b00000, 0x00000000c0c00000|  0%| F|  |TAMS 0x00000000c0b00000, 0x00000000c0b00000| Untracked 
| 524|0x00000000c0c00000, 0x00000000c0d00000, 0x00000000c0d00000|100%| S|CS|TAMS 0x00000000c0c00000, 0x00000000c0c00000| Complete 
| 525|0x00000000c0d00000, 0x00000000c0e00000, 0x00000000c0e00000|100%| S|CS|TAMS 0x00000000c0d00000, 0x00000000c0d00000| Complete 
| 526|0x00000000c0e00000, 0x00000000c0f00000, 0x00000000c0f00000|100%| S|CS|TAMS 0x00000000c0e00000, 0x00000000c0e00000| Complete 
| 527|0x00000000c0f00000, 0x00000000c1000000, 0x00000000c1000000|100%| S|CS|TAMS 0x00000000c0f00000, 0x00000000c0f00000| Complete 
| 528|0x00000000c1000000, 0x00000000c1100000, 0x00000000c1100000|100%| S|CS|TAMS 0x00000000c1000000, 0x00000000c1000000| Complete 
| 529|0x00000000c1100000, 0x00000000c1200000, 0x00000000c1200000|100%| S|CS|TAMS 0x00000000c1100000, 0x00000000c1100000| Complete 
| 530|0x00000000c1200000, 0x00000000c1300000, 0x00000000c1300000|100%| S|CS|TAMS 0x00000000c1200000, 0x00000000c1200000| Complete 
| 531|0x00000000c1300000, 0x00000000c1400000, 0x00000000c1400000|100%| S|CS|TAMS 0x00000000c1300000, 0x00000000c1300000| Complete 
| 532|0x00000000c1400000, 0x00000000c1400000, 0x00000000c1500000|  0%| F|  |TAMS 0x00000000c1400000, 0x00000000c1400000| Untracked 
| 533|0x00000000c1500000, 0x00000000c1500000, 0x00000000c1600000|  0%| F|  |TAMS 0x00000000c1500000, 0x00000000c1500000| Untracked 
| 534|0x00000000c1600000, 0x00000000c1600000, 0x00000000c1700000|  0%| F|  |TAMS 0x00000000c1600000, 0x00000000c1600000| Untracked 
| 535|0x00000000c1700000, 0x00000000c1700000, 0x00000000c1800000|  0%| F|  |TAMS 0x00000000c1700000, 0x00000000c1700000| Untracked 
| 536|0x00000000c1800000, 0x00000000c1800000, 0x00000000c1900000|  0%| F|  |TAMS 0x00000000c1800000, 0x00000000c1800000| Untracked 
| 537|0x00000000c1900000, 0x00000000c1900000, 0x00000000c1a00000|  0%| F|  |TAMS 0x00000000c1900000, 0x00000000c1900000| Untracked 
| 538|0x00000000c1a00000, 0x00000000c1a00000, 0x00000000c1b00000|  0%| F|  |TAMS 0x00000000c1a00000, 0x00000000c1a00000| Untracked 
| 539|0x00000000c1b00000, 0x00000000c1b00000, 0x00000000c1c00000|  0%| F|  |TAMS 0x00000000c1b00000, 0x00000000c1b00000| Untracked 
| 540|0x00000000c1c00000, 0x00000000c1c00000, 0x00000000c1d00000|  0%| F|  |TAMS 0x00000000c1c00000, 0x00000000c1c00000| Untracked 
| 541|0x00000000c1d00000, 0x00000000c1d00000, 0x00000000c1e00000|  0%| F|  |TAMS 0x00000000c1d00000, 0x00000000c1d00000| Untracked 
| 542|0x00000000c1e00000, 0x00000000c1e00000, 0x00000000c1f00000|  0%| F|  |TAMS 0x00000000c1e00000, 0x00000000c1e00000| Untracked 
| 543|0x00000000c1f00000, 0x00000000c1f00000, 0x00000000c2000000|  0%| F|  |TAMS 0x00000000c1f00000, 0x00000000c1f00000| Untracked 
| 544|0x00000000c2000000, 0x00000000c2000000, 0x00000000c2100000|  0%| F|  |TAMS 0x00000000c2000000, 0x00000000c2000000| Untracked 
| 545|0x00000000c2100000, 0x00000000c2100000, 0x00000000c2200000|  0%| F|  |TAMS 0x00000000c2100000, 0x00000000c2100000| Untracked 
| 546|0x00000000c2200000, 0x00000000c2200000, 0x00000000c2300000|  0%| F|  |TAMS 0x00000000c2200000, 0x00000000c2200000| Untracked 
| 547|0x00000000c2300000, 0x00000000c2300000, 0x00000000c2400000|  0%| F|  |TAMS 0x00000000c2300000, 0x00000000c2300000| Untracked 
| 548|0x00000000c2400000, 0x00000000c2400000, 0x00000000c2500000|  0%| F|  |TAMS 0x00000000c2400000, 0x00000000c2400000| Untracked 
| 549|0x00000000c2500000, 0x00000000c2500000, 0x00000000c2600000|  0%| F|  |TAMS 0x00000000c2500000, 0x00000000c2500000| Untracked 
| 550|0x00000000c2600000, 0x00000000c2600000, 0x00000000c2700000|  0%| F|  |TAMS 0x00000000c2600000, 0x00000000c2600000| Untracked 
| 551|0x00000000c2700000, 0x00000000c2700000, 0x00000000c2800000|  0%| F|  |TAMS 0x00000000c2700000, 0x00000000c2700000| Untracked 
| 552|0x00000000c2800000, 0x00000000c2800000, 0x00000000c2900000|  0%| F|  |TAMS 0x00000000c2800000, 0x00000000c2800000| Untracked 
| 553|0x00000000c2900000, 0x00000000c2900000, 0x00000000c2a00000|  0%| F|  |TAMS 0x00000000c2900000, 0x00000000c2900000| Untracked 
| 554|0x00000000c2a00000, 0x00000000c2a00000, 0x00000000c2b00000|  0%| F|  |TAMS 0x00000000c2a00000, 0x00000000c2a00000| Untracked 
| 555|0x00000000c2b00000, 0x00000000c2b00000, 0x00000000c2c00000|  0%| F|  |TAMS 0x00000000c2b00000, 0x00000000c2b00000| Untracked 
| 556|0x00000000c2c00000, 0x00000000c2c00000, 0x00000000c2d00000|  0%| F|  |TAMS 0x00000000c2c00000, 0x00000000c2c00000| Untracked 
| 557|0x00000000c2d00000, 0x00000000c2d00000, 0x00000000c2e00000|  0%| F|  |TAMS 0x00000000c2d00000, 0x00000000c2d00000| Untracked 
| 558|0x00000000c2e00000, 0x00000000c2e00000, 0x00000000c2f00000|  0%| F|  |TAMS 0x00000000c2e00000, 0x00000000c2e00000| Untracked 
| 559|0x00000000c2f00000, 0x00000000c2f00000, 0x00000000c3000000|  0%| F|  |TAMS 0x00000000c2f00000, 0x00000000c2f00000| Untracked 
| 560|0x00000000c3000000, 0x00000000c3000000, 0x00000000c3100000|  0%| F|  |TAMS 0x00000000c3000000, 0x00000000c3000000| Untracked 
| 561|0x00000000c3100000, 0x00000000c3100000, 0x00000000c3200000|  0%| F|  |TAMS 0x00000000c3100000, 0x00000000c3100000| Untracked 
| 562|0x00000000c3200000, 0x00000000c3200000, 0x00000000c3300000|  0%| F|  |TAMS 0x00000000c3200000, 0x00000000c3200000| Untracked 
| 563|0x00000000c3300000, 0x00000000c3300000, 0x00000000c3400000|  0%| F|  |TAMS 0x00000000c3300000, 0x00000000c3300000| Untracked 
| 564|0x00000000c3400000, 0x00000000c3400000, 0x00000000c3500000|  0%| F|  |TAMS 0x00000000c3400000, 0x00000000c3400000| Untracked 
| 565|0x00000000c3500000, 0x00000000c3500000, 0x00000000c3600000|  0%| F|  |TAMS 0x00000000c3500000, 0x00000000c3500000| Untracked 
| 566|0x00000000c3600000, 0x00000000c3600000, 0x00000000c3700000|  0%| F|  |TAMS 0x00000000c3600000, 0x00000000c3600000| Untracked 
| 567|0x00000000c3700000, 0x00000000c3700000, 0x00000000c3800000|  0%| F|  |TAMS 0x00000000c3700000, 0x00000000c3700000| Untracked 
| 568|0x00000000c3800000, 0x00000000c3800000, 0x00000000c3900000|  0%| F|  |TAMS 0x00000000c3800000, 0x00000000c3800000| Untracked 
| 569|0x00000000c3900000, 0x00000000c3900000, 0x00000000c3a00000|  0%| F|  |TAMS 0x00000000c3900000, 0x00000000c3900000| Untracked 
| 570|0x00000000c3a00000, 0x00000000c3a00000, 0x00000000c3b00000|  0%| F|  |TAMS 0x00000000c3a00000, 0x00000000c3a00000| Untracked 
| 571|0x00000000c3b00000, 0x00000000c3b00000, 0x00000000c3c00000|  0%| F|  |TAMS 0x00000000c3b00000, 0x00000000c3b00000| Untracked 
| 572|0x00000000c3c00000, 0x00000000c3c00000, 0x00000000c3d00000|  0%| F|  |TAMS 0x00000000c3c00000, 0x00000000c3c00000| Untracked 
| 573|0x00000000c3d00000, 0x00000000c3d00000, 0x00000000c3e00000|  0%| F|  |TAMS 0x00000000c3d00000, 0x00000000c3d00000| Untracked 
| 574|0x00000000c3e00000, 0x00000000c3e00000, 0x00000000c3f00000|  0%| F|  |TAMS 0x00000000c3e00000, 0x00000000c3e00000| Untracked 
| 575|0x00000000c3f00000, 0x00000000c3f00000, 0x00000000c4000000|  0%| F|  |TAMS 0x00000000c3f00000, 0x00000000c3f00000| Untracked 
| 576|0x00000000c4000000, 0x00000000c4000000, 0x00000000c4100000|  0%| F|  |TAMS 0x00000000c4000000, 0x00000000c4000000| Untracked 
| 577|0x00000000c4100000, 0x00000000c4100000, 0x00000000c4200000|  0%| F|  |TAMS 0x00000000c4100000, 0x00000000c4100000| Untracked 
| 578|0x00000000c4200000, 0x00000000c4200000, 0x00000000c4300000|  0%| F|  |TAMS 0x00000000c4200000, 0x00000000c4200000| Untracked 
| 579|0x00000000c4300000, 0x00000000c4300000, 0x00000000c4400000|  0%| F|  |TAMS 0x00000000c4300000, 0x00000000c4300000| Untracked 
| 580|0x00000000c4400000, 0x00000000c4400000, 0x00000000c4500000|  0%| F|  |TAMS 0x00000000c4400000, 0x00000000c4400000| Untracked 
| 581|0x00000000c4500000, 0x00000000c4500000, 0x00000000c4600000|  0%| F|  |TAMS 0x00000000c4500000, 0x00000000c4500000| Untracked 
| 582|0x00000000c4600000, 0x00000000c4600000, 0x00000000c4700000|  0%| F|  |TAMS 0x00000000c4600000, 0x00000000c4600000| Untracked 
| 583|0x00000000c4700000, 0x00000000c4700000, 0x00000000c4800000|  0%| F|  |TAMS 0x00000000c4700000, 0x00000000c4700000| Untracked 
| 584|0x00000000c4800000, 0x00000000c4800000, 0x00000000c4900000|  0%| F|  |TAMS 0x00000000c4800000, 0x00000000c4800000| Untracked 
| 585|0x00000000c4900000, 0x00000000c4900000, 0x00000000c4a00000|  0%| F|  |TAMS 0x00000000c4900000, 0x00000000c4900000| Untracked 
| 586|0x00000000c4a00000, 0x00000000c4a00000, 0x00000000c4b00000|  0%| F|  |TAMS 0x00000000c4a00000, 0x00000000c4a00000| Untracked 
| 587|0x00000000c4b00000, 0x00000000c4b00000, 0x00000000c4c00000|  0%| F|  |TAMS 0x00000000c4b00000, 0x00000000c4b00000| Untracked 
| 588|0x00000000c4c00000, 0x00000000c4c00000, 0x00000000c4d00000|  0%| F|  |TAMS 0x00000000c4c00000, 0x00000000c4c00000| Untracked 
| 589|0x00000000c4d00000, 0x00000000c4d00000, 0x00000000c4e00000|  0%| F|  |TAMS 0x00000000c4d00000, 0x00000000c4d00000| Untracked 
| 590|0x00000000c4e00000, 0x00000000c4e00000, 0x00000000c4f00000|  0%| F|  |TAMS 0x00000000c4e00000, 0x00000000c4e00000| Untracked 
| 591|0x00000000c4f00000, 0x00000000c4f00000, 0x00000000c5000000|  0%| F|  |TAMS 0x00000000c4f00000, 0x00000000c4f00000| Untracked 
| 592|0x00000000c5000000, 0x00000000c5000000, 0x00000000c5100000|  0%| F|  |TAMS 0x00000000c5000000, 0x00000000c5000000| Untracked 
| 593|0x00000000c5100000, 0x00000000c5100000, 0x00000000c5200000|  0%| F|  |TAMS 0x00000000c5100000, 0x00000000c5100000| Untracked 
| 594|0x00000000c5200000, 0x00000000c5200000, 0x00000000c5300000|  0%| F|  |TAMS 0x00000000c5200000, 0x00000000c5200000| Untracked 
| 595|0x00000000c5300000, 0x00000000c5300000, 0x00000000c5400000|  0%| F|  |TAMS 0x00000000c5300000, 0x00000000c5300000| Untracked 

Card table byte_map: [0x0000015d61f30000,0x0000015d62230000] _byte_map_base: 0x0000015d61a30000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000015d4f156d68, (CMBitMap*) 0x0000015d4f156da0
 Prev Bits: [0x0000015d62530000, 0x0000015d63d30000)
 Next Bits: [0x0000015d63d30000, 0x0000015d65530000)

Polling page: 0x0000015d4d110000

Metaspace:

Usage:
  Non-class:     90.91 MB capacity,    89.39 MB ( 98%) used,     1.22 MB (  1%) free+waste,   303.38 KB ( <1%) overhead. 
      Class:     14.25 MB capacity,    13.40 MB ( 94%) used,   738.40 KB (  5%) free+waste,   133.25 KB ( <1%) overhead. 
       Both:    105.16 MB capacity,   102.79 MB ( 98%) used,     1.95 MB (  2%) free+waste,   436.63 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       92.00 MB reserved,      91.00 MB ( 99%) committed 
      Class space:        1.00 GB reserved,      14.38 MB (  1%) committed 
             Both:        1.09 GB reserved,     105.38 MB (  9%) committed 

Chunk freelists:
   Non-Class:  27.00 KB
       Class:  0 bytes
        Both:  27.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 171.88 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=6822Kb max_used=6822Kb free=113241Kb
 bounds [0x0000015d5a6f0000, 0x0000015d5ada0000, 0x0000015d61c30000]
CodeHeap 'profiled nmethods': size=120000Kb used=26806Kb max_used=26806Kb free=93194Kb
 bounds [0x0000015d531c0000, 0x0000015d54bf0000, 0x0000015d5a6f0000]
CodeHeap 'non-nmethods': size=5696Kb used=2409Kb max_used=2456Kb free=3286Kb
 bounds [0x0000015d52c30000, 0x0000015d52ea0000, 0x0000015d531c0000]
 total_blobs=13947 nmethods=13019 adapters=840
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 31.065 Thread 0x0000015d683ab800 13331       3       com.android.tools.r8.graph.d1::a (44 bytes)
Event: 31.065 Thread 0x0000015d683ab800 nmethod 13331 0x0000015d54bd9b90 code [0x0000015d54bd9d60, 0x0000015d54bd9ff8]
Event: 31.065 Thread 0x0000015d683a7000 nmethod 12719 0x0000015d5ad97b10 code [0x0000015d5ad97ca0, 0x0000015d5ad97d38]
Event: 31.065 Thread 0x0000015d683ab800 13332 %     3       com.android.tools.r8.internal.Hj::b @ 5 (36 bytes)
Event: 31.065 Thread 0x0000015d683a7000 12762       4       com.android.tools.r8.graph.V0::c (8 bytes)
Event: 31.065 Thread 0x0000015d683ab800 nmethod 13332% 0x0000015d54bda110 code [0x0000015d54bda300, 0x0000015d54bda7d8]
Event: 31.065 Thread 0x0000015d683ab800 13329 %     3       com.android.tools.r8.ir.conversion.r::a @ 63 (162 bytes)
Event: 31.065 Thread 0x0000015d683a7000 nmethod 12762 0x0000015d5ad97e10 code [0x0000015d5ad97fa0, 0x0000015d5ad98018]
Event: 31.065 Thread 0x0000015d683a7000 12748       4       com.android.tools.r8.graph.O::d0 (86 bytes)
Event: 31.069 Thread 0x0000015d683ab800 nmethod 13329% 0x0000015d54bdaa10 code [0x0000015d54bdb060, 0x0000015d54bdf2a8]
Event: 31.069 Thread 0x0000015d683ab800 13333       3       com.android.tools.r8.ir.conversion.r::a (450 bytes)
Event: 31.074 Thread 0x0000015d683ab800 nmethod 13333 0x0000015d54be0990 code [0x0000015d54be1100, 0x0000015d54be5778]
Event: 31.074 Thread 0x0000015d683ab800 13334       3       com.android.tools.r8.internal.Hj::b (36 bytes)
Event: 31.074 Thread 0x0000015d683ab800 nmethod 13334 0x0000015d54be6e90 code [0x0000015d54be7080, 0x0000015d54be74d8]
Event: 31.076 Thread 0x0000015d683ab800 13335       3       com.android.tools.r8.ir.conversion.r::a (162 bytes)
Event: 31.080 Thread 0x0000015d683ab800 nmethod 13335 0x0000015d54be7690 code [0x0000015d54be7ce0, 0x0000015d54bebe18]
Event: 31.091 Thread 0x0000015d683a7000 nmethod 12748 0x0000015d5ad98110 code [0x0000015d5ad98300, 0x0000015d5ad98f28]
Event: 31.091 Thread 0x0000015d683a7000 13336       4       com.android.tools.r8.internal.wt::hasNext (212 bytes)
Event: 31.106 Thread 0x0000015d683ab800 13337       2       com.android.tools.r8.graph.N2::d (10 bytes)
Event: 31.107 Thread 0x0000015d683ab800 nmethod 13337 0x0000015d54bed490 code [0x0000015d54bed640, 0x0000015d54bed768]

GC Heap History (20 events):
Event: 23.977 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 292864K, used 208450K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 100212K, capacity 102562K, committed 102784K, reserved 1138688K
  class space    used 12967K, capacity 13834K, committed 13952K, reserved 1048576K
}
Event: 24.025 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 292864K, used 211522K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 5 survivors (5120K)
 Metaspace       used 100212K, capacity 102562K, committed 102784K, reserved 1138688K
  class space    used 12967K, capacity 13834K, committed 13952K, reserved 1048576K
}
Event: 24.054 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 292864K, used 210942K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 100212K, capacity 102562K, committed 102784K, reserved 1138688K
  class space    used 12967K, capacity 13834K, committed 13952K, reserved 1048576K
}
Event: 24.466 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 292864K, used 233470K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 2 survivors (2048K)
 Metaspace       used 100219K, capacity 102565K, committed 102784K, reserved 1138688K
  class space    used 12969K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 24.511 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 368640K, used 218925K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 100219K, capacity 102565K, committed 102784K, reserved 1138688K
  class space    used 12969K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 24.866 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 368640K, used 247597K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 31 young (31744K), 3 survivors (3072K)
 Metaspace       used 100245K, capacity 102565K, committed 102784K, reserved 1138688K
  class space    used 12969K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 24.907 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 368640K, used 227328K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 100245K, capacity 102565K, committed 102784K, reserved 1138688K
  class space    used 12969K, capacity 13835K, committed 13952K, reserved 1048576K
}
Event: 25.651 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 368640K, used 268288K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 48 young (49152K), 6 survivors (6144K)
 Metaspace       used 101070K, capacity 103320K, committed 103424K, reserved 1138688K
  class space    used 13130K, capacity 13981K, committed 14080K, reserved 1048576K
}
Event: 25.719 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 368640K, used 234816K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 101070K, capacity 103320K, committed 103424K, reserved 1138688K
  class space    used 13130K, capacity 13981K, committed 14080K, reserved 1048576K
}
Event: 27.108 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 368640K, used 276800K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 48 young (49152K), 6 survivors (6144K)
 Metaspace       used 102628K, capacity 104986K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 27.171 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 368640K, used 252596K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 102628K, capacity 104986K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 27.609 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 368640K, used 288436K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 6 survivors (6144K)
 Metaspace       used 102677K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 27.669 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 610304K, used 271872K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 102677K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 28.303 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 610304K, used 351744K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 84 young (86016K), 6 survivors (6144K)
 Metaspace       used 102679K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 28.400 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 610304K, used 314880K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 102679K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 28.944 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 610304K, used 401920K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 97 young (99328K), 11 survivors (11264K)
 Metaspace       used 102687K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 29.072 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 610304K, used 363008K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 102687K, capacity 104990K, committed 105216K, reserved 1140736K
  class space    used 13425K, capacity 14293K, committed 14336K, reserved 1048576K
}
Event: 29.829 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 610304K, used 435712K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 13 survivors (13312K)
 Metaspace       used 102727K, capacity 105124K, committed 105216K, reserved 1140736K
  class space    used 13427K, capacity 14294K, committed 14336K, reserved 1048576K
}
Event: 29.914 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 610304K, used 395264K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 102727K, capacity 105124K, committed 105216K, reserved 1140736K
  class space    used 13427K, capacity 14294K, committed 14336K, reserved 1048576K
}
Event: 31.115 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 610304K, used 459776K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 11 survivors (11264K)
 Metaspace       used 105258K, capacity 107687K, committed 107904K, reserved 1142784K
  class space    used 13722K, capacity 14594K, committed 14720K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 30.742 Thread 0x0000015d6a6a4800 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000015d5abc7a80 relative=0x00000000000002a0
Event: 30.742 Thread 0x0000015d6a6a4800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000015d5abc7a80 method=org.gradle.internal.file.FileHierarchySet$Node.isChildOfOrThis(Ljava/lang/String;I)Z @ 35 c2
Event: 30.742 Thread 0x0000015d6a6a4800 DEOPT PACKING pc=0x0000015d5abc7a80 sp=0x00000036ea3fea70
Event: 30.742 Thread 0x0000015d6a6a4800 DEOPT UNPACKING pc=0x0000015d52c7a1af sp=0x00000036ea3fea10 mode 2
Event: 30.756 Thread 0x0000015d6e1bf000 Uncommon trap: trap_request=0xffffff7e fr.pc=0x0000015d5ad6d2ac relative=0x00000000000023ac
Event: 30.756 Thread 0x0000015d6e1bf000 Uncommon trap: reason=predicate action=maybe_recompile pc=0x0000015d5ad6d2ac method=java.util.TimSort.binarySort([Ljava/lang/Object;IIILjava/util/Comparator;)V @ 34 c2
Event: 30.756 Thread 0x0000015d6e1bf000 DEOPT PACKING pc=0x0000015d5ad6d2ac sp=0x00000036ed9f7a60
Event: 30.756 Thread 0x0000015d6e1bf000 DEOPT UNPACKING pc=0x0000015d52c7a1af sp=0x00000036ed9f7998 mode 2
Event: 30.820 Thread 0x0000015d6f9e9000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000015d5ac8bad4 relative=0x0000000000000814
Event: 30.820 Thread 0x0000015d6f9e9000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000015d5ac8bad4 method=java.util.concurrent.ConcurrentHashMap.replaceNode(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 136 c2
Event: 30.820 Thread 0x0000015d6f9e9000 DEOPT PACKING pc=0x0000015d5ac8bad4 sp=0x00000036edffc610
Event: 30.820 Thread 0x0000015d6f9e9000 DEOPT UNPACKING pc=0x0000015d52c7a1af sp=0x00000036edffc598 mode 2
Event: 30.867 Thread 0x0000015d6bb49800 DEOPT PACKING pc=0x0000015d548d241d sp=0x00000036ea7fe400
Event: 30.867 Thread 0x0000015d6bb49800 DEOPT UNPACKING pc=0x0000015d52c7a95e sp=0x00000036ea7fd8c8 mode 0
Event: 30.902 Thread 0x0000015d6f9ef800 DEOPT PACKING pc=0x0000015d54a958a9 sp=0x00000036ee1ff110
Event: 30.902 Thread 0x0000015d6f9ef800 DEOPT UNPACKING pc=0x0000015d52c7a95e sp=0x00000036ee1fe5b8 mode 0
Event: 30.962 Thread 0x0000015d6f9ed000 DEOPT PACKING pc=0x0000015d543a3feb sp=0x00000036edefd6e0
Event: 30.962 Thread 0x0000015d6f9ed000 DEOPT UNPACKING pc=0x0000015d52c7a95e sp=0x00000036edefcbb8 mode 0
Event: 31.079 Thread 0x0000015d6f9e7800 DEOPT PACKING pc=0x0000015d54bc3f24 sp=0x00000036ee0fed90
Event: 31.079 Thread 0x0000015d6f9e7800 DEOPT UNPACKING pc=0x0000015d52c7a95e sp=0x00000036ee0fe3f0 mode 0

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 21.968 Thread 0x0000015d6f9ed000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab8a4210}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0
Event: 24.667 Thread 0x0000015d6f9ed000 Implicit null exception at 0x0000015d5ac78c53 to 0x0000015d5ac7d1ac
Event: 26.280 Thread 0x0000015d6f9e7800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b622f0a0}: Found class java.lang.Object, but interface was expected> (0x00000000b622f0a0) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 26.283 Thread 0x0000015d6f9ef800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b6238f60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int)'> (0x00000000b6238f60) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 26.376 Thread 0x0000015d6f9e7800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b6063a80}: Found class java.lang.Object, but interface was expected> (0x00000000b6063a80) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 26.377 Thread 0x0000015d6f9e7800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000b606cbf8}: Found class java.lang.Object, but interface was expected> (0x00000000b606cbf8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 27.008 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b50268d0}: <clinit>> (0x00000000b50268d0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 27.008 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b5027ed8}: <clinit>> (0x00000000b5027ed8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 27.008 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b502aad8}: <clinit>> (0x00000000b502aad8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 27.009 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b5062e58}: <clinit>> (0x00000000b5062e58) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 27.010 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000b5064090}: <clinit>> (0x00000000b5064090) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 30.539 Thread 0x0000015d6f9ed000 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000c41ecc50}: Found class java.lang.Object, but interface was expected> (0x00000000c41ecc50) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 839]
Event: 30.722 Thread 0x0000015d6e1bf000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c3b41060}> (0x00000000c3b41060) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.723 Thread 0x0000015d6e1bf000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c3b41f58}> (0x00000000c3b41f58) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.741 Thread 0x0000015d6e1bf000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c3b43118}> (0x00000000c3b43118) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.741 Thread 0x0000015d6e1bf000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000c3b44008}> (0x00000000c3b44008) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 30.756 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c39375f8}: <clinit>> (0x00000000c39375f8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 30.760 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c396b740}: <clinit>> (0x00000000c396b740) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 30.763 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c39a4200}: <clinit>> (0x00000000c39a4200) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 30.763 Thread 0x0000015d6e1bf000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000c39b6e80}: <clinit>> (0x00000000c39b6e80) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]

Events (20 events):
Event: 31.005 Executing VM operation: RevokeBias done
Event: 31.008 Executing VM operation: RevokeBias
Event: 31.008 Executing coalesced safepoint VM operation: RevokeBias
Event: 31.008 Executing coalesced safepoint VM operation: RevokeBias done
Event: 31.008 Executing VM operation: RevokeBias done
Event: 31.011 Executing VM operation: RevokeBias
Event: 31.011 Executing coalesced safepoint VM operation: RevokeBias
Event: 31.011 Executing coalesced safepoint VM operation: RevokeBias done
Event: 31.011 Executing coalesced safepoint VM operation: RevokeBias
Event: 31.011 Executing coalesced safepoint VM operation: RevokeBias done
Event: 31.011 Executing VM operation: RevokeBias done
Event: 31.013 loading class com/android/build/gradle/internal/scope/InternalArtifactType$MICRO_APK_MANIFEST_FILE
Event: 31.013 loading class com/android/build/gradle/internal/scope/InternalArtifactType$MICRO_APK_MANIFEST_FILE done
Event: 31.032 loading class com/android/build/api/variant/impl/BuiltArtifactTypeAdapter$read$1
Event: 31.032 loading class com/android/build/api/variant/impl/BuiltArtifactTypeAdapter$read$1 done
Event: 31.051 Executing VM operation: BulkRevokeBias
Event: 31.053 Executing coalesced safepoint VM operation: RevokeBias
Event: 31.053 Executing coalesced safepoint VM operation: RevokeBias done
Event: 31.053 Executing VM operation: BulkRevokeBias done
Event: 31.113 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff6aed50000 - 0x00007ff6aed5a000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa744b0000 - 0x00007ffa744c7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa414a0000 - 0x00007ffa4153d000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa3c770000 - 0x00007ffa3c8b4000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:19 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (337M free)
TotalPageFile size 32602M (AvailPageFile size 19M)
current process WorkingSet (physical memory assigned to process): 773M, peak: 784M
current process commit charge ("private bytes"): 923M, peak: 1111M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
