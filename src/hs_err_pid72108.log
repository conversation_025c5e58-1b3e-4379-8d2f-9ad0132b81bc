#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 801552 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/share/memory/arena.cpp:197), pid=72108, tid=72564
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 18:04:14 2022 India Standard Time elapsed time: 97.161774 seconds (0d 0h 1m 37s)

---------------  T H R E A D  ---------------

Current thread (0x00000264ff3d3800):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=72564, stack(0x000000d66fa00000,0x000000d66fb00000)]


Current CompileTask:
C2:  97161 17219       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (560 bytes)

Stack: [0x000000d66fa00000,0x000000d66fb00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x7335d3]
V  [jvm.dll+0x247bf8]
V  [jvm.dll+0xc018c]
V  [jvm.dll+0xc06cc]
V  [jvm.dll+0x1ffe21]
V  [jvm.dll+0x592ec5]
V  [jvm.dll+0x591c6e]
V  [jvm.dll+0x2004a2]
V  [jvm.dll+0x1ff794]
V  [jvm.dll+0x18868c]
V  [jvm.dll+0x20e0d7]
V  [jvm.dll+0x20c901]
V  [jvm.dll+0x6f9b7f]
V  [jvm.dll+0x6f26a5]
V  [jvm.dll+0x5fabf6]
C  [ucrtbase.dll+0x26c0c]
C  [KERNEL32.DLL+0x154e0]
C  [ntdll.dll+0x485b]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002649069b3f0, length=87, elements={
0x00000264f5abf800, 0x00000264ff3ac800, 0x00000264ff3af800, 0x00000264ff3d1000,
0x00000264ff3d1800, 0x00000264ff3d2800, 0x00000264ff3d3800, 0x000002648f07e000,
0x000002648f093000, 0x000002648f185000, 0x000002648fc74800, 0x000002649141c800,
0x00000264914ad800, 0x00000264915e2800, 0x000002648fc54000, 0x00000264912a3800,
0x00000264912a4000, 0x00000264912a5000, 0x00000264912a1800, 0x00000264912a5800,
0x0000026492b30800, 0x0000026492b31000, 0x0000026492b2b800, 0x0000026492b2c000,
0x0000026492b32000, 0x0000026494d1d800, 0x0000026494d1e800, 0x0000026494d1f800,
0x0000026494d17000, 0x0000026494d12000, 0x0000026494d15800, 0x0000026494d14000,
0x0000026494d14800, 0x0000026494d16800, 0x0000026494d11800, 0x0000026494d1d000,
0x0000026494d18000, 0x0000026494d1b000, 0x0000026494d18800, 0x0000026494d19800,
0x0000026494d1a800, 0x0000026492b33000, 0x0000026492b2d000, 0x0000026492b33800,
0x0000026492b2e000, 0x0000026492b34800, 0x0000026492b35000, 0x0000026492b2f800,
0x0000026492b37800, 0x0000026492b39800, 0x0000026492b37000, 0x0000026492b36000,
0x0000026492b38800, 0x0000026492b3a000, 0x000002649129f000, 0x00000264912a0000,
0x00000264912a1000, 0x00000264912a2800, 0x0000026496a9c000, 0x0000026496a9c800,
0x0000026496a98800, 0x0000026496a9d800, 0x0000026496a9b000, 0x0000026496a98000,
0x0000026496a9e800, 0x0000026496a9f000, 0x0000026496a99800, 0x0000026496a9a800,
0x0000026496aa5800, 0x0000026496aa0000, 0x0000026496aa1000, 0x0000026496aa6800,
0x0000026496aa4000, 0x0000026496aa1800, 0x0000026496aa2800, 0x0000026496aa5000,
0x0000026496aa3000, 0x0000026495f12000, 0x0000026495f14800, 0x0000026495f15000,
0x0000026495f16000, 0x0000026495f11000, 0x0000026495f13800, 0x0000026495f19800,
0x0000026495f1b800, 0x0000026495f1a000, 0x0000026495f1c800
}

Java Threads: ( => current thread )
  0x00000264f5abf800 JavaThread "main" [_thread_blocked, id=57156, stack(0x000000d66ee00000,0x000000d66ef00000)]
  0x00000264ff3ac800 JavaThread "Reference Handler" daemon [_thread_blocked, id=73088, stack(0x000000d66f500000,0x000000d66f600000)]
  0x00000264ff3af800 JavaThread "Finalizer" daemon [_thread_blocked, id=47952, stack(0x000000d66f600000,0x000000d66f700000)]
  0x00000264ff3d1000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=73204, stack(0x000000d66f700000,0x000000d66f800000)]
  0x00000264ff3d1800 JavaThread "Attach Listener" daemon [_thread_blocked, id=69304, stack(0x000000d66f800000,0x000000d66f900000)]
  0x00000264ff3d2800 JavaThread "Service Thread" daemon [_thread_blocked, id=68536, stack(0x000000d66f900000,0x000000d66fa00000)]
=>0x00000264ff3d3800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=72564, stack(0x000000d66fa00000,0x000000d66fb00000)]
  0x000002648f07e000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=42408, stack(0x000000d66fb00000,0x000000d66fc00000)]
  0x000002648f093000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=72796, stack(0x000000d66fc00000,0x000000d66fd00000)]
  0x000002648f185000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=34228, stack(0x000000d66fd00000,0x000000d66fe00000)]
  0x000002648fc74800 JavaThread "Daemon health stats" [_thread_blocked, id=72740, stack(0x000000d670200000,0x000000d670300000)]
  0x000002649141c800 JavaThread "Incoming local TCP Connector on port 51851" [_thread_in_native, id=64324, stack(0x000000d670100000,0x000000d670200000)]
  0x00000264914ad800 JavaThread "Daemon periodic checks" [_thread_blocked, id=31988, stack(0x000000d670300000,0x000000d670400000)]
  0x00000264915e2800 JavaThread "Daemon" [_thread_blocked, id=73084, stack(0x000000d670400000,0x000000d670500000)]
  0x000002648fc54000 JavaThread "Daemon worker" [_thread_blocked, id=45032, stack(0x000000d670700000,0x000000d670800000)]
  0x00000264912a3800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=52080, stack(0x000000d670c00000,0x000000d670d00000)]
  0x00000264912a4000 JavaThread "File lock request listener" [_thread_in_native, id=69740, stack(0x000000d670d00000,0x000000d670e00000)]
  0x00000264912a5000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=69260, stack(0x000000d670e00000,0x000000d670f00000)]
  0x00000264912a1800 JavaThread "File watcher server" daemon [_thread_in_native, id=44484, stack(0x000000d671000000,0x000000d671100000)]
  0x00000264912a5800 JavaThread "File watcher consumer" daemon [_thread_blocked, id=20976, stack(0x000000d671100000,0x000000d671200000)]
  0x0000026492b30800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=70100, stack(0x000000d671600000,0x000000d671700000)]
  0x0000026492b31000 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=69760, stack(0x000000d671700000,0x000000d671800000)]
  0x0000026492b2b800 JavaThread "jar transforms" [_thread_blocked, id=70500, stack(0x000000d671800000,0x000000d671900000)]
  0x0000026492b2c000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=55088, stack(0x000000d671400000,0x000000d671500000)]
  0x0000026492b32000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=67580, stack(0x000000d671c00000,0x000000d671d00000)]
  0x0000026494d1d800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=67536, stack(0x000000d674500000,0x000000d674600000)]
  0x0000026494d1e800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=72196, stack(0x000000d671900000,0x000000d671a00000)]
  0x0000026494d1f800 JavaThread "Memory manager" [_thread_blocked, id=52196, stack(0x000000d674600000,0x000000d674700000)]
  0x0000026494d17000 JavaThread "Handler for socket connection from /127.0.0.1:51851 to /127.0.0.1:51857" [_thread_in_native, id=69836, stack(0x000000d670600000,0x000000d670700000)]
  0x0000026494d12000 JavaThread "Cancel handler" [_thread_blocked, id=73288, stack(0x000000d670800000,0x000000d670900000)]
  0x0000026494d15800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51851 to /127.0.0.1:51857" [_thread_blocked, id=56640, stack(0x000000d670900000,0x000000d670a00000)]
  0x0000026494d14000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=55516, stack(0x000000d670f00000,0x000000d671000000)]
  0x0000026494d14800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=68992, stack(0x000000d671200000,0x000000d671300000)]
  0x0000026494d16800 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=71484, stack(0x000000d671300000,0x000000d671400000)]
  0x0000026494d11800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=65316, stack(0x000000d671500000,0x000000d671600000)]
  0x0000026494d1d000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=73540, stack(0x000000d671a00000,0x000000d671b00000)]
  0x0000026494d18000 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=58964, stack(0x000000d671b00000,0x000000d671c00000)]
  0x0000026494d1b000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=59628, stack(0x000000d671d00000,0x000000d671e00000)]
  0x0000026494d18800 JavaThread "Unconstrained build operations" [_thread_blocked, id=67488, stack(0x000000d671e00000,0x000000d671f00000)]
  0x0000026494d19800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=58680, stack(0x000000d671f00000,0x000000d672000000)]
  0x0000026494d1a800 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=70788, stack(0x000000d672000000,0x000000d672100000)]
  0x0000026492b33000 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=55352, stack(0x000000d672100000,0x000000d672200000)]
  0x0000026492b2d000 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=65744, stack(0x000000d672200000,0x000000d672300000)]
  0x0000026492b33800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=68120, stack(0x000000d672300000,0x000000d672400000)]
  0x0000026492b2e000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=73148, stack(0x000000d672400000,0x000000d672500000)]
  0x0000026492b34800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=6836, stack(0x000000d672500000,0x000000d672600000)]
  0x0000026492b35000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=71380, stack(0x000000d672600000,0x000000d672700000)]
  0x0000026492b2f800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=71196, stack(0x000000d672700000,0x000000d672800000)]
  0x0000026492b37800 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=73112, stack(0x000000d672800000,0x000000d672900000)]
  0x0000026492b39800 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=25604, stack(0x000000d672900000,0x000000d672a00000)]
  0x0000026492b37000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=53568, stack(0x000000d672a00000,0x000000d672b00000)]
  0x0000026492b36000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=44244, stack(0x000000d672b00000,0x000000d672c00000)]
  0x0000026492b38800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=73104, stack(0x000000d672c00000,0x000000d672d00000)]
  0x0000026492b3a000 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=16468, stack(0x000000d672d00000,0x000000d672e00000)]
  0x000002649129f000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=29968, stack(0x000000d672e00000,0x000000d672f00000)]
  0x00000264912a0000 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=70256, stack(0x000000d672f00000,0x000000d673000000)]
  0x00000264912a1000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=73424, stack(0x000000d673000000,0x000000d673100000)]
  0x00000264912a2800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=50140, stack(0x000000d673100000,0x000000d673200000)]
  0x0000026496a9c000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=72252, stack(0x000000d673200000,0x000000d673300000)]
  0x0000026496a9c800 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=55140, stack(0x000000d673300000,0x000000d673400000)]
  0x0000026496a98800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=56932, stack(0x000000d673400000,0x000000d673500000)]
  0x0000026496a9d800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=73668, stack(0x000000d673500000,0x000000d673600000)]
  0x0000026496a9b000 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=73472, stack(0x000000d673600000,0x000000d673700000)]
  0x0000026496a98000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=72152, stack(0x000000d673700000,0x000000d673800000)]
  0x0000026496a9e800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=71428, stack(0x000000d673800000,0x000000d673900000)]
  0x0000026496a9f000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=70080, stack(0x000000d673900000,0x000000d673a00000)]
  0x0000026496a99800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=70952, stack(0x000000d673a00000,0x000000d673b00000)]
  0x0000026496a9a800 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=72652, stack(0x000000d673b00000,0x000000d673c00000)]
  0x0000026496aa5800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=57224, stack(0x000000d673c00000,0x000000d673d00000)]
  0x0000026496aa0000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=67448, stack(0x000000d673d00000,0x000000d673e00000)]
  0x0000026496aa1000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=65676, stack(0x000000d673e00000,0x000000d673f00000)]
  0x0000026496aa6800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=70180, stack(0x000000d673f00000,0x000000d674000000)]
  0x0000026496aa4000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=68396, stack(0x000000d674000000,0x000000d674100000)]
  0x0000026496aa1800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=73100, stack(0x000000d674100000,0x000000d674200000)]
  0x0000026496aa2800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=50004, stack(0x000000d674200000,0x000000d674300000)]
  0x0000026496aa5000 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=59596, stack(0x000000d674300000,0x000000d674400000)]
  0x0000026496aa3000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=67516, stack(0x000000d674400000,0x000000d674500000)]
  0x0000026495f12000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=49012, stack(0x000000d674700000,0x000000d674800000)]
  0x0000026495f14800 JavaThread "build event listener" [_thread_blocked, id=73308, stack(0x000000d674800000,0x000000d674900000)]
  0x0000026495f15000 JavaThread "Execution worker for ':'" [_thread_blocked, id=71064, stack(0x000000d674900000,0x000000d674a00000)]
  0x0000026495f16000 JavaThread "Execution worker for ':' Thread 2" [_thread_blocked, id=73020, stack(0x000000d674a00000,0x000000d674b00000)]
  0x0000026495f11000 JavaThread "Execution worker for ':' Thread 3" [_thread_in_Java, id=52744, stack(0x000000d674b00000,0x000000d674c00000)]
  0x0000026495f13800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\executionHistory)" [_thread_blocked, id=22020, stack(0x000000d674c00000,0x000000d674d00000)]
  0x0000026495f19800 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=72688, stack(0x000000d674e00000,0x000000d674f00000)]
  0x0000026495f1b800 JavaThread "WorkerExecutor Queue Thread 3" [_thread_blocked, id=73300, stack(0x000000d674f00000,0x000000d675000000)]
  0x0000026495f1a000 JavaThread "WorkerExecutor Queue Thread 4" [_thread_in_Java, id=72052, stack(0x000000d66ed00000,0x000000d66ee00000)]
  0x0000026495f1c800 JavaThread "WorkerExecutor Queue Thread 6" [_thread_blocked, id=72824, stack(0x000000d675000000,0x000000d675100000)]

Other Threads:
  0x00000264ff388800 VMThread "VM Thread" [stack: 0x000000d66f400000,0x000000d66f500000] [id=67528]
  0x000002648f196800 WatcherThread [stack: 0x000000d66fe00000,0x000000d66ff00000] [id=73060]
  0x00000264f5ad8000 GCTaskThread "GC Thread#0" [stack: 0x000000d66ef00000,0x000000d66f000000] [id=63744]
  0x000002648f6bd800 GCTaskThread "GC Thread#1" [stack: 0x000000d66ff00000,0x000000d670000000] [id=67332]
  0x000002648f9a2000 GCTaskThread "GC Thread#2" [stack: 0x000000d670000000,0x000000d670100000] [id=57928]
  0x000002649263e800 GCTaskThread "GC Thread#3" [stack: 0x000000d670b00000,0x000000d670c00000] [id=73456]
  0x00000264f5aff000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000d66f000000,0x000000d66f100000] [id=32568]
  0x00000264f5b02000 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000d66f100000,0x000000d66f200000] [id=61408]
  0x00000264f5b93000 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000d66f200000,0x000000d66f300000] [id=72168]
  0x00000264f5b97000 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x000000d66f300000,0x000000d66f400000] [id=73416]

Threads with active compile tasks:
C2 CompilerThread0  97271 17219       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (560 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 225280K, used 171582K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 6 survivors (6144K)
 Metaspace       used 111774K, capacity 114227K, committed 114472K, reserved 1148928K
  class space    used 13897K, capacity 14822K, committed 14936K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Untracked 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Untracked 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Untracked 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| O|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Untracked 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| O|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Untracked 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%|HS|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Complete 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%|HS|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Complete 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%|HS|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Complete 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HS|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HC|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HC|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%|HS|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Complete 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%|HC|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Complete 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Untracked 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Untracked 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3000000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3100000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3200000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3700000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4300000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4700000, 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| O|  |TAMS 0x00000000a4800000, 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| O|  |TAMS 0x00000000a4900000, 0x00000000a4800000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| O|  |TAMS 0x00000000a4a00000, 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| O|  |TAMS 0x00000000a4b00000, 0x00000000a4a00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| O|  |TAMS 0x00000000a4c00000, 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| O|  |TAMS 0x00000000a4d00000, 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| O|  |TAMS 0x00000000a4e00000, 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| O|  |TAMS 0x00000000a4f00000, 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| O|  |TAMS 0x00000000a5000000, 0x00000000a4f00000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| O|  |TAMS 0x00000000a5100000, 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| O|  |TAMS 0x00000000a5200000, 0x00000000a5100000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| O|  |TAMS 0x00000000a5300000, 0x00000000a5200000| Untracked 
|  83|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| O|  |TAMS 0x00000000a5400000, 0x00000000a5300000| Untracked 
|  84|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| O|  |TAMS 0x00000000a5500000, 0x00000000a5400000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| O|  |TAMS 0x00000000a5600000, 0x00000000a5500000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| O|  |TAMS 0x00000000a5700000, 0x00000000a5600000| Untracked 
|  87|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| O|  |TAMS 0x00000000a5800000, 0x00000000a5700000| Untracked 
|  88|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| O|  |TAMS 0x00000000a5900000, 0x00000000a5800000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| O|  |TAMS 0x00000000a5a00000, 0x00000000a5900000| Untracked 
|  90|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| O|  |TAMS 0x00000000a5b00000, 0x00000000a5a00000| Untracked 
|  91|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| O|  |TAMS 0x00000000a5c00000, 0x00000000a5b00000| Untracked 
|  92|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| O|  |TAMS 0x00000000a5d00000, 0x00000000a5c00000| Untracked 
|  93|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| O|  |TAMS 0x00000000a5e00000, 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| O|  |TAMS 0x00000000a5f00000, 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| O|  |TAMS 0x00000000a6000000, 0x00000000a5f00000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| O|  |TAMS 0x00000000a6100000, 0x00000000a6000000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| O|  |TAMS 0x00000000a6200000, 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| O|  |TAMS 0x00000000a6300000, 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| O|  |TAMS 0x00000000a6400000, 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| O|  |TAMS 0x00000000a6500000, 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| O|  |TAMS 0x00000000a6600000, 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| O|  |TAMS 0x00000000a6700000, 0x00000000a6600000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%|HS|  |TAMS 0x00000000a6800000, 0x00000000a6700000| Complete 
| 104|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| O|  |TAMS 0x00000000a6900000, 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| O|  |TAMS 0x00000000a6a00000, 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| O|  |TAMS 0x00000000a6b00000, 0x00000000a6a00000| Untracked 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| O|  |TAMS 0x00000000a6c00000, 0x00000000a6b00000| Untracked 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| O|  |TAMS 0x00000000a6d00000, 0x00000000a6c00000| Untracked 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| O|  |TAMS 0x00000000a6e00000, 0x00000000a6d00000| Untracked 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| O|  |TAMS 0x00000000a6f00000, 0x00000000a6e00000| Untracked 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| O|  |TAMS 0x00000000a7000000, 0x00000000a6f00000| Untracked 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| O|  |TAMS 0x00000000a7100000, 0x00000000a7000000| Untracked 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| O|  |TAMS 0x00000000a7200000, 0x00000000a7100000| Untracked 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| O|  |TAMS 0x00000000a7300000, 0x00000000a7200000| Untracked 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| O|  |TAMS 0x00000000a7400000, 0x00000000a7300000| Untracked 
| 116|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| O|  |TAMS 0x00000000a7500000, 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7600000, 0x00000000a7600000|100%| O|  |TAMS 0x00000000a7600000, 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7700000, 0x00000000a7700000|100%| O|  |TAMS 0x00000000a7700000, 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7800000, 0x00000000a7800000|100%| O|  |TAMS 0x00000000a7800000, 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7900000, 0x00000000a7900000|100%| O|  |TAMS 0x00000000a7900000, 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7a00000, 0x00000000a7a00000|100%| O|  |TAMS 0x00000000a7a00000, 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7b00000, 0x00000000a7b00000|100%| O|  |TAMS 0x00000000a7b00000, 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7c00000, 0x00000000a7c00000|100%| O|  |TAMS 0x00000000a7c00000, 0x00000000a7b00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7d00000, 0x00000000a7d00000|100%| O|  |TAMS 0x00000000a7c00000, 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7e00000, 0x00000000a7e00000|100%| O|  |TAMS 0x00000000a7d00000, 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7f00000, 0x00000000a7f00000|100%| O|  |TAMS 0x00000000a7e00000, 0x00000000a7e00000| Untracked 
| 127|0x00000000a7f00000, 0x00000000a8000000, 0x00000000a8000000|100%| O|  |TAMS 0x00000000a7f00000, 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8100000, 0x00000000a8100000|100%| O|  |TAMS 0x00000000a8100000, 0x00000000a8000000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8200000, 0x00000000a8200000|100%| O|  |TAMS 0x00000000a8100000, 0x00000000a8100000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8300000, 0x00000000a8300000|100%| O|  |TAMS 0x00000000a8200000, 0x00000000a8200000| Untracked 
| 131|0x00000000a8300000, 0x00000000a8400000, 0x00000000a8400000|100%| O|  |TAMS 0x00000000a8300000, 0x00000000a8300000| Untracked 
| 132|0x00000000a8400000, 0x00000000a8500000, 0x00000000a8500000|100%| O|  |TAMS 0x00000000a8400000, 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8600000, 0x00000000a8600000|100%| O|  |TAMS 0x00000000a8500000, 0x00000000a8500000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8700000, 0x00000000a8700000|100%| O|  |TAMS 0x00000000a8600000, 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8800000, 0x00000000a8800000|100%| O|  |TAMS 0x00000000a8700000, 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8900000, 0x00000000a8900000|100%| O|  |TAMS 0x00000000a8800000, 0x00000000a8800000| Untracked 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| O|  |TAMS 0x00000000a8900000, 0x00000000a8900000| Untracked 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| O|  |TAMS 0x00000000a8a00000, 0x00000000a8a00000| Untracked 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| O|  |TAMS 0x00000000a8b00000, 0x00000000a8b00000| Untracked 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| O|  |TAMS 0x00000000a8c00000, 0x00000000a8c00000| Untracked 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| O|  |TAMS 0x00000000a8d00000, 0x00000000a8d00000| Untracked 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| O|  |TAMS 0x00000000a8e00000, 0x00000000a8e00000| Untracked 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| O|  |TAMS 0x00000000a8f00000, 0x00000000a8f00000| Untracked 
| 144|0x00000000a9000000, 0x00000000a908fa00, 0x00000000a9100000| 56%| O|  |TAMS 0x00000000a9000000, 0x00000000a9000000| Untracked 
| 145|0x00000000a9100000, 0x00000000a9100000, 0x00000000a9200000|  0%| F|  |TAMS 0x00000000a9100000, 0x00000000a9100000| Untracked 
| 146|0x00000000a9200000, 0x00000000a9200000, 0x00000000a9300000|  0%| F|  |TAMS 0x00000000a9200000, 0x00000000a9200000| Untracked 
| 147|0x00000000a9300000, 0x00000000a9300000, 0x00000000a9400000|  0%| F|  |TAMS 0x00000000a9300000, 0x00000000a9300000| Untracked 
| 148|0x00000000a9400000, 0x00000000a9400000, 0x00000000a9500000|  0%| F|  |TAMS 0x00000000a9400000, 0x00000000a9400000| Untracked 
| 149|0x00000000a9500000, 0x00000000a9500000, 0x00000000a9600000|  0%| F|  |TAMS 0x00000000a9500000, 0x00000000a9500000| Untracked 
| 150|0x00000000a9600000, 0x00000000a9600000, 0x00000000a9700000|  0%| F|  |TAMS 0x00000000a9600000, 0x00000000a9600000| Untracked 
| 151|0x00000000a9700000, 0x00000000a9700000, 0x00000000a9800000|  0%| F|  |TAMS 0x00000000a9700000, 0x00000000a9700000| Untracked 
| 152|0x00000000a9800000, 0x00000000a9800000, 0x00000000a9900000|  0%| F|  |TAMS 0x00000000a9800000, 0x00000000a9800000| Untracked 
| 153|0x00000000a9900000, 0x00000000a9900000, 0x00000000a9a00000|  0%| F|  |TAMS 0x00000000a9900000, 0x00000000a9900000| Untracked 
| 154|0x00000000a9a00000, 0x00000000a9a00000, 0x00000000a9b00000|  0%| F|  |TAMS 0x00000000a9a00000, 0x00000000a9a00000| Untracked 
| 155|0x00000000a9b00000, 0x00000000a9b00000, 0x00000000a9c00000|  0%| F|  |TAMS 0x00000000a9b00000, 0x00000000a9b00000| Untracked 
| 156|0x00000000a9c00000, 0x00000000a9c00000, 0x00000000a9d00000|  0%| F|  |TAMS 0x00000000a9c00000, 0x00000000a9c00000| Untracked 
| 157|0x00000000a9d00000, 0x00000000a9d00000, 0x00000000a9e00000|  0%| F|  |TAMS 0x00000000a9d00000, 0x00000000a9d00000| Untracked 
| 158|0x00000000a9e00000, 0x00000000a9e00000, 0x00000000a9f00000|  0%| F|  |TAMS 0x00000000a9e00000, 0x00000000a9e00000| Untracked 
| 159|0x00000000a9f00000, 0x00000000a9f00000, 0x00000000aa000000|  0%| F|  |TAMS 0x00000000a9f00000, 0x00000000a9f00000| Untracked 
| 160|0x00000000aa000000, 0x00000000aa000000, 0x00000000aa100000|  0%| F|  |TAMS 0x00000000aa000000, 0x00000000aa000000| Untracked 
| 161|0x00000000aa100000, 0x00000000aa100000, 0x00000000aa200000|  0%| F|  |TAMS 0x00000000aa100000, 0x00000000aa100000| Untracked 
| 162|0x00000000aa200000, 0x00000000aa200000, 0x00000000aa300000|  0%| F|  |TAMS 0x00000000aa200000, 0x00000000aa200000| Untracked 
| 163|0x00000000aa300000, 0x00000000aa300000, 0x00000000aa400000|  0%| F|  |TAMS 0x00000000aa300000, 0x00000000aa300000| Untracked 
| 164|0x00000000aa400000, 0x00000000aa400000, 0x00000000aa500000|  0%| F|  |TAMS 0x00000000aa400000, 0x00000000aa400000| Untracked 
| 165|0x00000000aa500000, 0x00000000aa500000, 0x00000000aa600000|  0%| F|  |TAMS 0x00000000aa500000, 0x00000000aa500000| Untracked 
| 166|0x00000000aa600000, 0x00000000aa600000, 0x00000000aa700000|  0%| F|  |TAMS 0x00000000aa600000, 0x00000000aa600000| Untracked 
| 167|0x00000000aa700000, 0x00000000aa700000, 0x00000000aa800000|  0%| F|  |TAMS 0x00000000aa700000, 0x00000000aa700000| Untracked 
| 168|0x00000000aa800000, 0x00000000aa800000, 0x00000000aa900000|  0%| F|  |TAMS 0x00000000aa800000, 0x00000000aa800000| Untracked 
| 169|0x00000000aa900000, 0x00000000aa900000, 0x00000000aaa00000|  0%| F|  |TAMS 0x00000000aa900000, 0x00000000aa900000| Untracked 
| 170|0x00000000aaa00000, 0x00000000aaa00000, 0x00000000aab00000|  0%| F|  |TAMS 0x00000000aaa00000, 0x00000000aaa00000| Untracked 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| S|CS|TAMS 0x00000000aab00000, 0x00000000aab00000| Complete 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| S|CS|TAMS 0x00000000aac00000, 0x00000000aac00000| Complete 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| S|CS|TAMS 0x00000000aad00000, 0x00000000aad00000| Complete 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| S|CS|TAMS 0x00000000aae00000, 0x00000000aae00000| Complete 
| 175|0x00000000aaf00000, 0x00000000aaf00000, 0x00000000ab000000|  0%| F|  |TAMS 0x00000000aaf00000, 0x00000000aaf00000| Untracked 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| S|CS|TAMS 0x00000000ab000000, 0x00000000ab000000| Complete 
| 177|0x00000000ab100000, 0x00000000ab100000, 0x00000000ab200000|  0%| F|  |TAMS 0x00000000ab100000, 0x00000000ab100000| Untracked 
| 178|0x00000000ab200000, 0x00000000ab200000, 0x00000000ab300000|  0%| F|  |TAMS 0x00000000ab200000, 0x00000000ab200000| Untracked 
| 179|0x00000000ab300000, 0x00000000ab300000, 0x00000000ab400000|  0%| F|  |TAMS 0x00000000ab300000, 0x00000000ab300000| Untracked 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| S|CS|TAMS 0x00000000ab400000, 0x00000000ab400000| Complete 
| 181|0x00000000ab500000, 0x00000000ab500000, 0x00000000ab600000|  0%| F|  |TAMS 0x00000000ab500000, 0x00000000ab500000| Untracked 
| 182|0x00000000ab600000, 0x00000000ab600000, 0x00000000ab700000|  0%| F|  |TAMS 0x00000000ab600000, 0x00000000ab600000| Untracked 
| 183|0x00000000ab700000, 0x00000000ab700000, 0x00000000ab800000|  0%| F|  |TAMS 0x00000000ab700000, 0x00000000ab700000| Untracked 
| 184|0x00000000ab800000, 0x00000000ab800000, 0x00000000ab900000|  0%| F|  |TAMS 0x00000000ab800000, 0x00000000ab800000| Untracked 
| 185|0x00000000ab900000, 0x00000000ab900000, 0x00000000aba00000|  0%| F|  |TAMS 0x00000000ab900000, 0x00000000ab900000| Untracked 
| 186|0x00000000aba00000, 0x00000000aba00000, 0x00000000abb00000|  0%| F|  |TAMS 0x00000000aba00000, 0x00000000aba00000| Untracked 
| 187|0x00000000abb00000, 0x00000000abb00000, 0x00000000abc00000|  0%| F|  |TAMS 0x00000000abb00000, 0x00000000abb00000| Untracked 
| 188|0x00000000abc00000, 0x00000000abc00000, 0x00000000abd00000|  0%| F|  |TAMS 0x00000000abc00000, 0x00000000abc00000| Untracked 
| 189|0x00000000abd00000, 0x00000000abd00000, 0x00000000abe00000|  0%| F|  |TAMS 0x00000000abd00000, 0x00000000abd00000| Untracked 
| 190|0x00000000abe00000, 0x00000000abe00000, 0x00000000abf00000|  0%| F|  |TAMS 0x00000000abe00000, 0x00000000abe00000| Untracked 
| 191|0x00000000abf00000, 0x00000000abf00000, 0x00000000ac000000|  0%| F|  |TAMS 0x00000000abf00000, 0x00000000abf00000| Untracked 
| 192|0x00000000ac000000, 0x00000000ac000000, 0x00000000ac100000|  0%| F|  |TAMS 0x00000000ac000000, 0x00000000ac000000| Untracked 
| 193|0x00000000ac100000, 0x00000000ac100000, 0x00000000ac200000|  0%| F|  |TAMS 0x00000000ac100000, 0x00000000ac100000| Untracked 
| 194|0x00000000ac200000, 0x00000000ac200000, 0x00000000ac300000|  0%| F|  |TAMS 0x00000000ac200000, 0x00000000ac200000| Untracked 
| 195|0x00000000ac300000, 0x00000000ac300000, 0x00000000ac400000|  0%| F|  |TAMS 0x00000000ac300000, 0x00000000ac300000| Untracked 
| 196|0x00000000ac400000, 0x00000000ac400000, 0x00000000ac500000|  0%| F|  |TAMS 0x00000000ac400000, 0x00000000ac400000| Untracked 
| 197|0x00000000ac500000, 0x00000000ac500000, 0x00000000ac600000|  0%| F|  |TAMS 0x00000000ac500000, 0x00000000ac500000| Untracked 
| 198|0x00000000ac600000, 0x00000000ac600000, 0x00000000ac700000|  0%| F|  |TAMS 0x00000000ac600000, 0x00000000ac600000| Untracked 
| 199|0x00000000ac700000, 0x00000000ac700000, 0x00000000ac800000|  0%| F|  |TAMS 0x00000000ac700000, 0x00000000ac700000| Untracked 
| 200|0x00000000ac800000, 0x00000000ac800000, 0x00000000ac900000|  0%| F|  |TAMS 0x00000000ac800000, 0x00000000ac800000| Untracked 
| 201|0x00000000ac900000, 0x00000000ac98bd50, 0x00000000aca00000| 54%| E|  |TAMS 0x00000000ac900000, 0x00000000ac900000| Complete 
| 202|0x00000000aca00000, 0x00000000acb00000, 0x00000000acb00000|100%| E|CS|TAMS 0x00000000aca00000, 0x00000000aca00000| Complete 
| 203|0x00000000acb00000, 0x00000000acc00000, 0x00000000acc00000|100%| E|CS|TAMS 0x00000000acb00000, 0x00000000acb00000| Complete 
| 204|0x00000000acc00000, 0x00000000acd00000, 0x00000000acd00000|100%| E|CS|TAMS 0x00000000acc00000, 0x00000000acc00000| Complete 
| 205|0x00000000acd00000, 0x00000000ace00000, 0x00000000ace00000|100%| E|CS|TAMS 0x00000000acd00000, 0x00000000acd00000| Complete 
| 206|0x00000000ace00000, 0x00000000acf00000, 0x00000000acf00000|100%| E|CS|TAMS 0x00000000ace00000, 0x00000000ace00000| Complete 
| 207|0x00000000acf00000, 0x00000000ad000000, 0x00000000ad000000|100%| E|CS|TAMS 0x00000000acf00000, 0x00000000acf00000| Complete 
| 208|0x00000000ad000000, 0x00000000ad100000, 0x00000000ad100000|100%| E|CS|TAMS 0x00000000ad000000, 0x00000000ad000000| Complete 
| 209|0x00000000ad100000, 0x00000000ad200000, 0x00000000ad200000|100%| E|CS|TAMS 0x00000000ad100000, 0x00000000ad100000| Complete 
| 210|0x00000000ad200000, 0x00000000ad300000, 0x00000000ad300000|100%| E|CS|TAMS 0x00000000ad200000, 0x00000000ad200000| Complete 
| 211|0x00000000ad300000, 0x00000000ad400000, 0x00000000ad400000|100%| E|CS|TAMS 0x00000000ad300000, 0x00000000ad300000| Complete 
| 212|0x00000000ad400000, 0x00000000ad500000, 0x00000000ad500000|100%| E|  |TAMS 0x00000000ad400000, 0x00000000ad400000| Complete 
| 213|0x00000000ad500000, 0x00000000ad600000, 0x00000000ad600000|100%| E|CS|TAMS 0x00000000ad500000, 0x00000000ad500000| Complete 
| 214|0x00000000ad600000, 0x00000000ad700000, 0x00000000ad700000|100%| E|CS|TAMS 0x00000000ad600000, 0x00000000ad600000| Complete 
| 215|0x00000000ad700000, 0x00000000ad800000, 0x00000000ad800000|100%| E|CS|TAMS 0x00000000ad700000, 0x00000000ad700000| Complete 
| 216|0x00000000ad800000, 0x00000000ad900000, 0x00000000ad900000|100%| E|CS|TAMS 0x00000000ad800000, 0x00000000ad800000| Complete 
| 217|0x00000000ad900000, 0x00000000ada00000, 0x00000000ada00000|100%| E|CS|TAMS 0x00000000ad900000, 0x00000000ad900000| Complete 
| 218|0x00000000ada00000, 0x00000000adb00000, 0x00000000adb00000|100%| E|CS|TAMS 0x00000000ada00000, 0x00000000ada00000| Complete 
| 219|0x00000000adb00000, 0x00000000adc00000, 0x00000000adc00000|100%| E|CS|TAMS 0x00000000adb00000, 0x00000000adb00000| Complete 

Card table byte_map: [0x00000264f98d0000,0x00000264f9bd0000] _byte_map_base: 0x00000264f93d0000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000264f5af8358, (CMBitMap*) 0x00000264f5af8390
 Prev Bits: [0x00000264f9ed0000, 0x00000264fb6d0000)
 Next Bits: [0x00000264fb6d0000, 0x00000264fced0000)

Polling page: 0x00000264f52a0000

Metaspace:

Usage:
  Non-class:     97.08 MB capacity,    95.58 MB ( 98%) used,     1.19 MB (  1%) free+waste,   312.81 KB ( <1%) overhead. 
      Class:     14.47 MB capacity,    13.57 MB ( 94%) used,   788.40 KB (  5%) free+waste,   135.88 KB ( <1%) overhead. 
       Both:    111.55 MB capacity,   109.16 MB ( 98%) used,     1.96 MB (  2%) free+waste,   448.69 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       98.00 MB reserved,      97.20 MB (>99%) committed 
      Class space:        1.00 GB reserved,      14.59 MB (  1%) committed 
             Both:        1.10 GB reserved,     111.79 MB ( 10%) committed 

Chunk freelists:
   Non-Class:  3.00 KB
       Class:  0 bytes
        Both:  3.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 173.82 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=10743Kb max_used=10743Kb free=109320Kb
 bounds [0x0000026487ac0000, 0x0000026488540000, 0x000002648f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=37691Kb max_used=37691Kb free=82308Kb
 bounds [0x0000026480590000, 0x0000026482a60000, 0x0000026487ac0000]
CodeHeap 'non-nmethods': size=5696Kb used=2413Kb max_used=2459Kb free=3282Kb
 bounds [0x0000026480000000, 0x0000026480270000, 0x0000026480590000]
 total_blobs=17710 nmethods=16778 adapters=843
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 93.490 Thread 0x000002648f07e000 nmethod 17216 0x0000026482a5a290 code [0x0000026482a5a440, 0x0000026482a5a598]
Event: 93.779 Thread 0x000002648f07e000 17217       3       org.gradle.internal.snapshot.PartialDirectoryNode::withIncompleteChildren (2 bytes)
Event: 93.780 Thread 0x000002648f07e000 nmethod 17217 0x0000026482a5a610 code [0x0000026482a5a7c0, 0x0000026482a5a8b8]
Event: 93.805 Thread 0x000002648f07e000 17218       1       sun.nio.fs.WindowsException::lastError (5 bytes)
Event: 93.805 Thread 0x000002648f07e000 nmethod 17218 0x000002648853dc90 code [0x000002648853de40, 0x000002648853def8]
Event: 96.282 Thread 0x00000264ff3d3800 17219       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (560 bytes)
Event: 96.399 Thread 0x000002648f07e000 17220       3       java.util.Locale::getLanguage (8 bytes)
Event: 96.472 Thread 0x000002648f07e000 nmethod 17220 0x0000026482a5a990 code [0x0000026482a5ab40, 0x0000026482a5ac58]
Event: 97.061 Thread 0x000002648f07e000 17224       3       java.io.ObjectStreamClass::getMethodSignature (55 bytes)
Event: 97.062 Thread 0x000002648f07e000 nmethod 17224 0x0000026482a5ad10 code [0x0000026482a5af80, 0x0000026482a5b808]
Event: 97.079 Thread 0x000002648f07e000 17226   !   3       org.gradle.internal.snapshot.impl.DefaultValueSnapshotter::serialize (88 bytes)
Event: 97.082 Thread 0x000002648f07e000 nmethod 17226 0x0000026482a5bb90 code [0x0000026482a5be60, 0x0000026482a5cdb8]
Event: 97.086 Thread 0x000002648f07e000 17227       3       java.lang.Integer::formatUnsignedInt (44 bytes)
Event: 97.086 Thread 0x000002648f07e000 nmethod 17227 0x0000026482a5d410 code [0x0000026482a5d5e0, 0x0000026482a5d7f8]
Event: 97.095 Thread 0x000002648f07e000 17230       3       org.gradle.internal.snapshot.impl.AbstractManagedValueSnapshot::<init> (10 bytes)
Event: 97.095 Thread 0x000002648f07e000 nmethod 17230 0x0000026482a5d990 code [0x0000026482a5db40, 0x0000026482a5dcf8]
Event: 97.095 Thread 0x000002648f07e000 17231       3       org.gradle.internal.snapshot.impl.IsolatedManagedValue::<init> (16 bytes)
Event: 97.095 Thread 0x000002648f07e000 nmethod 17231 0x0000026482a5dd90 code [0x0000026482a5df60, 0x0000026482a5e238]
Event: 97.105 Thread 0x000002648f07e000 17232       3       java.io.ObjectStreamField::getPrimitiveSignature (98 bytes)
Event: 97.107 Thread 0x000002648f07e000 nmethod 17232 0x0000026482a5e310 code [0x0000026482a5e520, 0x0000026482a5ea08]

GC Heap History (20 events):
Event: 59.250 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 225280K, used 135080K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 4 survivors (4096K)
 Metaspace       used 103374K, capacity 105755K, committed 106024K, reserved 1140736K
  class space    used 13203K, capacity 14079K, committed 14168K, reserved 1048576K
}
Event: 59.265 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 225280K, used 128852K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 103374K, capacity 105755K, committed 106024K, reserved 1140736K
  class space    used 13203K, capacity 14079K, committed 14168K, reserved 1048576K
}
Event: 60.636 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 225280K, used 186196K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 1 survivors (1024K)
 Metaspace       used 103993K, capacity 106329K, committed 106536K, reserved 1142784K
  class space    used 13239K, capacity 14128K, committed 14168K, reserved 1048576K
}
Event: 60.644 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 225280K, used 131884K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 103993K, capacity 106329K, committed 106536K, reserved 1142784K
  class space    used 13239K, capacity 14128K, committed 14168K, reserved 1048576K
}
Event: 61.888 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 225280K, used 172844K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 45 young (46080K), 4 survivors (4096K)
 Metaspace       used 106313K, capacity 108672K, committed 108840K, reserved 1144832K
  class space    used 13417K, capacity 14323K, committed 14424K, reserved 1048576K
}
Event: 61.903 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 225280K, used 138281K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 106313K, capacity 108672K, committed 108840K, reserved 1144832K
  class space    used 13417K, capacity 14323K, committed 14424K, reserved 1048576K
}
Event: 61.977 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 225280K, used 142377K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 6 survivors (6144K)
 Metaspace       used 106800K, capacity 109090K, committed 109352K, reserved 1144832K
  class space    used 13460K, capacity 14355K, committed 14424K, reserved 1048576K
}
Event: 61.988 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 225280K, used 138738K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 106800K, capacity 109090K, committed 109352K, reserved 1144832K
  class space    used 13460K, capacity 14355K, committed 14424K, reserved 1048576K
}
Event: 63.189 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 225280K, used 176626K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 1 survivors (1024K)
 Metaspace       used 108752K, capacity 111178K, committed 111272K, reserved 1146880K
  class space    used 13618K, capacity 14516K, committed 14552K, reserved 1048576K
}
Event: 63.205 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 225280K, used 145030K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 108752K, capacity 111178K, committed 111272K, reserved 1146880K
  class space    used 13618K, capacity 14516K, committed 14552K, reserved 1048576K
}
Event: 65.890 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 225280K, used 180870K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 5 survivors (5120K)
 Metaspace       used 108956K, capacity 111370K, committed 111528K, reserved 1146880K
  class space    used 13629K, capacity 14516K, committed 14552K, reserved 1048576K
}
Event: 65.909 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 225280K, used 146895K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 108956K, capacity 111370K, committed 111528K, reserved 1146880K
  class space    used 13629K, capacity 14516K, committed 14552K, reserved 1048576K
}
Event: 68.571 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 225280K, used 182735K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 3 survivors (3072K)
 Metaspace       used 109930K, capacity 112320K, committed 112424K, reserved 1146880K
  class space    used 13725K, capacity 14619K, committed 14680K, reserved 1048576K
}
Event: 68.581 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 225280K, used 147851K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 109930K, capacity 112320K, committed 112424K, reserved 1146880K
  class space    used 13725K, capacity 14619K, committed 14680K, reserved 1048576K
}
Event: 71.337 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 225280K, used 185739K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 4 survivors (4096K)
 Metaspace       used 110616K, capacity 112949K, committed 113320K, reserved 1148928K
  class space    used 13781K, capacity 14658K, committed 14808K, reserved 1048576K
}
Event: 71.374 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 225280K, used 149109K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 110616K, capacity 112949K, committed 113320K, reserved 1148928K
  class space    used 13781K, capacity 14658K, committed 14808K, reserved 1048576K
}
Event: 80.199 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 225280K, used 186997K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 41 young (41984K), 3 survivors (3072K)
 Metaspace       used 111426K, capacity 113787K, committed 114088K, reserved 1148928K
  class space    used 13863K, capacity 14736K, committed 14808K, reserved 1048576K
}
Event: 80.225 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 225280K, used 150393K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 111426K, capacity 113787K, committed 114088K, reserved 1148928K
  class space    used 13863K, capacity 14736K, committed 14808K, reserved 1048576K
}
Event: 86.916 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 225280K, used 189305K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 43 young (44032K), 4 survivors (4096K)
 Metaspace       used 111665K, capacity 114054K, committed 114216K, reserved 1148928K
  class space    used 13888K, capacity 14814K, committed 14936K, reserved 1048576K
}
Event: 87.120 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 225280K, used 154174K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 111665K, capacity 114054K, committed 114216K, reserved 1148928K
  class space    used 13888K, capacity 14814K, committed 14936K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 80.939 Thread 0x0000026495f16000 DEOPT PACKING pc=0x0000026488479a2c sp=0x000000d674afcc90
Event: 80.940 Thread 0x0000026495f16000 DEOPT UNPACKING pc=0x000002648004a1af sp=0x000000d674afcc88 mode 2
Event: 80.940 Thread 0x0000026495f16000 Uncommon trap: trap_request=0xffffffec fr.pc=0x0000026488478054 relative=0x0000000000004214
Event: 80.940 Thread 0x0000026495f16000 Uncommon trap: reason=null_assert_or_unreached0 action=make_not_entrant pc=0x0000026488478054 method=org.gradle.internal.snapshot.impl.DefaultValueSnapshotter.processValue(Ljava/lang/Object;Lorg/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueV
Event: 80.940 Thread 0x0000026495f16000 DEOPT PACKING pc=0x0000026488478054 sp=0x000000d674afcd60
Event: 80.940 Thread 0x0000026495f16000 DEOPT UNPACKING pc=0x000002648004a1af sp=0x000000d674afcd68 mode 2
Event: 86.901 Thread 0x0000026495f16000 Uncommon trap: trap_request=0xffffffcc fr.pc=0x0000026488500954 relative=0x0000000000000314
Event: 86.901 Thread 0x0000026495f16000 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x0000026488500954 method=java.util.Arrays.copyOfRange([Ljava/lang/Object;IILjava/lang/Class;)[Ljava/lang/Object; @ 83 c2
Event: 86.901 Thread 0x0000026495f16000 DEOPT PACKING pc=0x0000026488500954 sp=0x000000d674afc1b0
Event: 86.902 Thread 0x0000026495f16000 DEOPT UNPACKING pc=0x000002648004a1af sp=0x000000d674afc130 mode 2
Event: 92.107 Thread 0x0000026492b3a000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x000002648834550c relative=0x000000000000046c
Event: 92.107 Thread 0x0000026492b3a000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002648834550c method=com.google.common.cache.LongAdder.add(J)V @ 95 c2
Event: 92.107 Thread 0x0000026492b3a000 DEOPT PACKING pc=0x000002648834550c sp=0x000000d672dfc650
Event: 92.107 Thread 0x0000026492b3a000 DEOPT UNPACKING pc=0x000002648004a1af sp=0x000000d672dfc4b8 mode 2
Event: 96.282 Thread 0x0000026495f16000 DEOPT PACKING pc=0x00000264811202f3 sp=0x000000d674afa4d0
Event: 96.282 Thread 0x0000026495f16000 DEOPT UNPACKING pc=0x000002648004a95e sp=0x000000d674af9a70 mode 0
Event: 97.100 Thread 0x0000026495f16000 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002648850b57c relative=0x00000000000063fc
Event: 97.100 Thread 0x0000026495f16000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002648850b57c method=org.gradle.internal.snapshot.impl.DefaultValueSnapshotter.processValue(Ljava/lang/Object;Lorg/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueVisitor;)Ljava/l
Event: 97.100 Thread 0x0000026495f16000 DEOPT PACKING pc=0x000002648850b57c sp=0x000000d674afa780
Event: 97.100 Thread 0x0000026495f16000 DEOPT UNPACKING pc=0x000002648004a1af sp=0x000000d674afa788 mode 2

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 78.004 Thread 0x0000026495f10800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ab974970}> (0x00000000ab974970) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 78.005 Thread 0x0000026495f10800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ab975718}> (0x00000000ab975718) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 80.975 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ad1f6a58}> (0x00000000ad1f6a58) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 80.976 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ad1f7928}> (0x00000000ad1f7928) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 81.058 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ace7ae70}: <clinit>> (0x00000000ace7ae70) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 81.059 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ace7c460}: <clinit>> (0x00000000ace7c460) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 81.059 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ace7f060}: <clinit>> (0x00000000ace7f060) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 81.064 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ace95cd0}: <clinit>> (0x00000000ace95cd0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 81.064 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ace97170}: <clinit>> (0x00000000ace97170) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 93.804 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acba3e60}> (0x00000000acba3e60) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.805 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acba4d58}> (0x00000000acba4d58) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.806 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acba51b0}> (0x00000000acba51b0) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.807 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acba6088}> (0x00000000acba6088) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.807 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acba6438}> (0x00000000acba6438) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.807 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acbc7300}> (0x00000000acbc7300) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.807 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acbc7650}> (0x00000000acbc7650) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 93.807 Thread 0x0000026495f16000 Exception <a 'sun/nio/fs/WindowsException'{0x00000000acbc8510}> (0x00000000acbc8510) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 97.060 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000acabda10}: <clinit>> (0x00000000acabda10) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 97.105 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000acaf7d28}: <clinit>> (0x00000000acaf7d28) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 97.114 Thread 0x0000026495f16000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ac90aef0}: <clinit>> (0x00000000ac90aef0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]

Events (20 events):
Event: 87.120 Executing VM operation: G1CollectForAllocation done
Event: 87.192 loading class org/gradle/internal/Try$1
Event: 87.192 loading class org/gradle/internal/Try$1 done
Event: 92.789 loading class org/gradle/internal/snapshot/FileSystemLocationSnapshot
Event: 92.789 loading class org/gradle/internal/snapshot/FileSystemLocationSnapshot done
Event: 92.793 loading class org/gradle/internal/fingerprint/impl/RelativePathFingerprintingStrategy
Event: 92.793 loading class org/gradle/internal/fingerprint/impl/RelativePathFingerprintingStrategy done
Event: 93.962 Executing VM operation: RevokeBias
Event: 93.964 Executing VM operation: RevokeBias done
Event: 93.966 Thread 0x0000026494d13000 Thread exited: 0x0000026494d13000
Event: 93.968 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$TaskAction
Event: 93.968 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$TaskAction done
Event: 94.672 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$doFullTaskAction$1
Event: 94.672 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$doFullTaskAction$1 done
Event: 94.688 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$TaskWorkActionParameters
Event: 94.688 loading class com/android/build/gradle/internal/res/LinkApplicationAndroidResourcesTask$TaskWorkActionParameters done
Event: 94.716 loading class com/android/builder/internal/aapt/AaptOptions
Event: 94.716 loading class com/android/builder/internal/aapt/AaptOptions done
Event: 97.003 Executing VM operation: RevokeBias
Event: 97.004 Executing VM operation: RevokeBias done


Dynamic libraries:
0x00007ff678cd0000 - 0x00007ff678cda000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa6c5b0000 - 0x00007ffa6c5c7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa42720000 - 0x00007ffa427bd000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa3c770000 - 0x00007ffa3c8b4000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffa7e3d0000 - 0x00007ffa7e404000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:26 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (97M free)
TotalPageFile size 32602M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 145M, peak: 446M
current process commit charge ("private bytes"): 542M, peak: 550M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
