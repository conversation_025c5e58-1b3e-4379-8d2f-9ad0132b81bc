# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
# This workflow will build a Java project with <PERSON>radle and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-gradle

name: Java CI with Gradle

on:
  push:
    branches:
    - 'master'
    - 'develop'
    - 'release/**'
  pull_request:
    types:
    - opened
    - reopened
    - synchronize

permissions: write-all

jobs:
  build_and_test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    - name: Setup Android SDK
      uses: android-actions/setup-android@v2
    # - run: bash build.sh release
    - name: Debug Build with Gradle
      if: ${{ github.base_ref == 'develop' || github.ref_name == 'develop' }}
      run: ./gradlew assembleDebug
    - name: Release Build with Gradle (Currently limited to Debug build)
      if: ${{ github.base_ref == 'master' || github.ref_name == 'master' || startsWith(github.ref_name, 'release/') }}
      # run: ./gradlew assembleRelease
      run: ./gradlew assembleDebug
    - name: Test with Gradle
      run: ./gradlew test
    - name: Publish Unit Test Results
      uses: EnricoMi/publish-unit-test-result-action@v1
      if: always()
      with:
        files: "**/test-results/**/*.xml"