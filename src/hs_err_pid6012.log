#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 999696 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/share/memory/arena.cpp:197), pid=6012, tid=72036
#
# JRE version: OpenJDK Runtime Environment (11.0.12+7) (build 11.0.12+7-b1504.28-7817840)
# Java VM: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3

Host: Intel(R) Core(TM) i3-10110U CPU @ 2.10GHz, 4 cores, 7G,  Windows 10 , 64 bit Build 22000 (10.0.22000.708)
Time: Fri Aug 19 17:52:02 2022 India Standard Time elapsed time: 151.407435 seconds (0d 0h 2m 31s)

---------------  T H R E A D  ---------------

Current thread (0x000001edd8eaa800):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=72036, stack(0x000000810b500000,0x000000810b600000)]


Current CompileTask:
C2: 151407 10461       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (560 bytes)

Stack: [0x000000810b500000,0x000000810b600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5fbcea]
V  [jvm.dll+0x731905]
V  [jvm.dll+0x732f1d]
V  [jvm.dll+0x7335d3]
V  [jvm.dll+0x247bf8]
V  [jvm.dll+0xc018c]
V  [jvm.dll+0xc06cc]
V  [jvm.dll+0x2bcc04]
V  [jvm.dll+0x5113a8]
V  [jvm.dll+0x201c5a]
V  [jvm.dll+0x1ff71f]
V  [jvm.dll+0x18868c]
V  [jvm.dll+0x20e0d7]
V  [jvm.dll+0x20c901]
V  [jvm.dll+0x6f9b7f]
V  [jvm.dll+0x6f26a5]
V  [jvm.dll+0x5fabf6]
C  [ucrtbase.dll+0x26c0c]
C  [KERNEL32.DLL+0x154e0]
C  [ntdll.dll+0x485b]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ede2669070, length=79, elements={
0x000001edc058e800, 0x000001edd8e81800, 0x000001edd8e8a000, 0x000001edd8ea3000,
0x000001edd8ea3800, 0x000001edd8ea4800, 0x000001edd8eaa800, 0x000001edd8eab800,
0x000001edd8ec2000, 0x000001edd9957000, 0x000001eddb30a800, 0x000001eddad25800,
0x000001edda77d000, 0x000001eddbad9000, 0x000001eddbade000, 0x000001eddbad8800,
0x000001eddbada000, 0x000001eddbadb000, 0x000001edda8c8000, 0x000001edda8ca800,
0x000001edda8cc000, 0x000001edda8ce800, 0x000001edda8d3800, 0x000001edde257000,
0x000001edde25a800, 0x000001edde25b000, 0x000001edde24f800, 0x000001edde250800,
0x000001edde24e000, 0x000001edde251800, 0x000001edde254800, 0x000001edde252000,
0x000001edde253000, 0x000001edde253800, 0x000001edde24f000, 0x000001edde255800,
0x000001edde25c000, 0x000001edde256000, 0x000001edde258000, 0x000001edde259800,
0x000001edda8cb800, 0x000001edda8cd000, 0x000001edda8c9000, 0x000001edda8c7000,
0x000001edda8cf800, 0x000001edda8d0000, 0x000001edda8d1000, 0x000001edda8d2000,
0x000001edda8d2800, 0x000001edda8ce000, 0x000001edda8d4800, 0x000001edda8d5000,
0x000001edda8d6000, 0x000001eddbadb800, 0x000001eddbadc800, 0x000001eddbadd800,
0x000001eddbadf000, 0x000001eddf79c000, 0x000001eddf798000, 0x000001eddf799000,
0x000001eddf79e800, 0x000001eddf79d000, 0x000001eddf79a800, 0x000001eddf79b800,
0x000001eddf79e000, 0x000001eddf79f800, 0x000001eddf79a000, 0x000001eddf7a3800,
0x000001eddf7a2000, 0x000001eddf7a2800, 0x000001eddf7a5000, 0x000001eddf7a4800,
0x000001eddf7a1000, 0x000001eddf7a6000, 0x000001eddf7a0800, 0x000001eddf7a7000,
0x000001ede2abe800, 0x000001ede2ac1000, 0x000001ede2abc800
}

Java Threads: ( => current thread )
  0x000001edc058e800 JavaThread "main" [_thread_blocked, id=71292, stack(0x000000810a900000,0x000000810aa00000)]
  0x000001edd8e81800 JavaThread "Reference Handler" daemon [_thread_blocked, id=72536, stack(0x000000810b000000,0x000000810b100000)]
  0x000001edd8e8a000 JavaThread "Finalizer" daemon [_thread_blocked, id=73068, stack(0x000000810b100000,0x000000810b200000)]
  0x000001edd8ea3000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=70164, stack(0x000000810b200000,0x000000810b300000)]
  0x000001edd8ea3800 JavaThread "Attach Listener" daemon [_thread_blocked, id=72792, stack(0x000000810b300000,0x000000810b400000)]
  0x000001edd8ea4800 JavaThread "Service Thread" daemon [_thread_blocked, id=44712, stack(0x000000810b400000,0x000000810b500000)]
=>0x000001edd8eaa800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=72036, stack(0x000000810b500000,0x000000810b600000)]
  0x000001edd8eab800 JavaThread "C1 CompilerThread0" daemon [_thread_in_vm, id=73592, stack(0x000000810b600000,0x000000810b700000)]
  0x000001edd8ec2000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=69560, stack(0x000000810b700000,0x000000810b800000)]
  0x000001edd9957000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=72976, stack(0x000000810b800000,0x000000810b900000)]
  0x000001eddb30a800 JavaThread "Daemon health stats" [_thread_blocked, id=54080, stack(0x000000810bc00000,0x000000810bd00000)]
  0x000001eddad25800 JavaThread "Incoming local TCP Connector on port 51603" [_thread_in_native, id=73376, stack(0x000000810bd00000,0x000000810be00000)]
  0x000001edda77d000 JavaThread "Daemon periodic checks" [_thread_blocked, id=50328, stack(0x000000810be00000,0x000000810bf00000)]
  0x000001eddbad9000 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=71920, stack(0x000000810c700000,0x000000810c800000)]
  0x000001eddbade000 JavaThread "File lock request listener" [_thread_in_native, id=72920, stack(0x000000810c800000,0x000000810c900000)]
  0x000001eddbad8800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileHashes)" [_thread_blocked, id=71212, stack(0x000000810c900000,0x000000810ca00000)]
  0x000001eddbada000 JavaThread "File watcher server" daemon [_thread_in_native, id=73340, stack(0x000000810cb00000,0x000000810cc00000)]
  0x000001eddbadb000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=69544, stack(0x000000810cc00000,0x000000810cd00000)]
  0x000001edda8c8000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.3.3\executionHistory)" [_thread_blocked, id=73704, stack(0x000000810d000000,0x000000810d100000)]
  0x000001edda8ca800 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.3.3\kotlin-dsl)" [_thread_blocked, id=64628, stack(0x000000810d100000,0x000000810d200000)]
  0x000001edda8cc000 JavaThread "jar transforms" [_thread_blocked, id=72404, stack(0x000000810d200000,0x000000810d300000)]
  0x000001edda8ce800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=45044, stack(0x000000810d300000,0x000000810d400000)]
  0x000001edda8d3800 JavaThread "jar transforms Thread 3" [_thread_blocked, id=50540, stack(0x000000810d600000,0x000000810d700000)]
  0x000001edde257000 JavaThread "jar transforms Thread 4" [_thread_blocked, id=72684, stack(0x000000810ff00000,0x0000008110000000)]
  0x000001edde25a800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.3.3\fileContent)" [_thread_blocked, id=72876, stack(0x0000008110000000,0x0000008110100000)]
  0x000001edde25b000 JavaThread "Memory manager" [_thread_blocked, id=66832, stack(0x0000008110100000,0x0000008110200000)]
  0x000001edde24f800 JavaThread "Daemon Thread 2" [_thread_blocked, id=73608, stack(0x000000810a600000,0x000000810a700000)]
  0x000001edde250800 JavaThread "Handler for socket connection from /127.0.0.1:51603 to /127.0.0.1:51641" [_thread_in_native, id=9268, stack(0x000000810a700000,0x000000810a800000)]
  0x000001edde24e000 JavaThread "Cancel handler" [_thread_blocked, id=70912, stack(0x000000810a800000,0x000000810a900000)]
  0x000001edde251800 JavaThread "Daemon worker Thread 2" [_thread_in_Java, id=71428, stack(0x000000810bf00000,0x000000810c000000)]
  0x000001edde254800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51603 to /127.0.0.1:51641" [_thread_blocked, id=42312, stack(0x000000810c000000,0x000000810c100000)]
  0x000001edde252000 JavaThread "Stdin handler" [_thread_blocked, id=73600, stack(0x000000810c100000,0x000000810c200000)]
  0x000001edde253000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=62084, stack(0x000000810c200000,0x000000810c300000)]
  0x000001edde253800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\fileHashes)" [_thread_blocked, id=71740, stack(0x000000810c300000,0x000000810c400000)]
  0x000001edde24f000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\checksums)" [_thread_blocked, id=71900, stack(0x000000810c400000,0x000000810c500000)]
  0x000001edde255800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.3.3\md-supplier)" [_thread_blocked, id=55588, stack(0x000000810c500000,0x000000810c600000)]
  0x000001edde25c000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.3.3\md-rule)" [_thread_blocked, id=51244, stack(0x000000810ca00000,0x000000810cb00000)]
  0x000001edde256000 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\7.3.3\dependencies-accessors)" [_thread_blocked, id=71180, stack(0x000000810cd00000,0x000000810ce00000)]
  0x000001edde258000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Documents\GitHub\sibelsdk\spacelabs-sibelPatch\src\.gradle\buildOutputCleanup)" [_thread_blocked, id=67720, stack(0x000000810ce00000,0x000000810cf00000)]
  0x000001edde259800 JavaThread "Unconstrained build operations" [_thread_blocked, id=57236, stack(0x000000810cf00000,0x000000810d000000)]
  0x000001edda8cb800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=47900, stack(0x000000810d400000,0x000000810d500000)]
  0x000001edda8cd000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=69300, stack(0x000000810d500000,0x000000810d600000)]
  0x000001edda8c9000 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=71360, stack(0x000000810d700000,0x000000810d800000)]
  0x000001edda8c7000 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=73540, stack(0x000000810d800000,0x000000810d900000)]
  0x000001edda8cf800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=52080, stack(0x000000810d900000,0x000000810da00000)]
  0x000001edda8d0000 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=61100, stack(0x000000810da00000,0x000000810db00000)]
  0x000001edda8d1000 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=63744, stack(0x000000810db00000,0x000000810dc00000)]
  0x000001edda8d2000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=72948, stack(0x000000810dc00000,0x000000810dd00000)]
  0x000001edda8d2800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=72072, stack(0x000000810dd00000,0x000000810de00000)]
  0x000001edda8ce000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=64216, stack(0x000000810de00000,0x000000810df00000)]
  0x000001edda8d4800 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=71436, stack(0x000000810df00000,0x000000810e000000)]
  0x000001edda8d5000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=72980, stack(0x000000810e000000,0x000000810e100000)]
  0x000001edda8d6000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=69156, stack(0x000000810e100000,0x000000810e200000)]
  0x000001eddbadb800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=50636, stack(0x000000810e200000,0x000000810e300000)]
  0x000001eddbadc800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=69684, stack(0x000000810e300000,0x000000810e400000)]
  0x000001eddbadd800 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=70016, stack(0x000000810e400000,0x000000810e500000)]
  0x000001eddbadf000 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=72200, stack(0x000000810e500000,0x000000810e600000)]
  0x000001eddf79c000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=69844, stack(0x000000810e600000,0x000000810e700000)]
  0x000001eddf798000 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=73148, stack(0x000000810e700000,0x000000810e800000)]
  0x000001eddf799000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=8208, stack(0x000000810e800000,0x000000810e900000)]
  0x000001eddf79e800 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=52692, stack(0x000000810e900000,0x000000810ea00000)]
  0x000001eddf79d000 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=58240, stack(0x000000810ea00000,0x000000810eb00000)]
  0x000001eddf79a800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=72132, stack(0x000000810eb00000,0x000000810ec00000)]
  0x000001eddf79b800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=71388, stack(0x000000810ec00000,0x000000810ed00000)]
  0x000001eddf79e000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=46664, stack(0x000000810ed00000,0x000000810ee00000)]
  0x000001eddf79f800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=73536, stack(0x000000810ee00000,0x000000810ef00000)]
  0x000001eddf79a000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=70324, stack(0x000000810ef00000,0x000000810f000000)]
  0x000001eddf7a3800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=73232, stack(0x000000810f000000,0x000000810f100000)]
  0x000001eddf7a2000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=73560, stack(0x000000810f100000,0x000000810f200000)]
  0x000001eddf7a2800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=73400, stack(0x000000810f200000,0x000000810f300000)]
  0x000001eddf7a5000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=73552, stack(0x000000810f300000,0x000000810f400000)]
  0x000001eddf7a4800 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=73224, stack(0x000000810f400000,0x000000810f500000)]
  0x000001eddf7a1000 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=71376, stack(0x000000810f500000,0x000000810f600000)]
  0x000001eddf7a6000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=71976, stack(0x000000810f600000,0x000000810f700000)]
  0x000001eddf7a0800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=72600, stack(0x000000810f700000,0x000000810f800000)]
  0x000001eddf7a7000 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=72744, stack(0x000000810f800000,0x000000810f900000)]
  0x000001ede2abe800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=73492, stack(0x000000810f900000,0x000000810fa00000)]
  0x000001ede2ac1000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=73060, stack(0x000000810fa00000,0x000000810fb00000)]
  0x000001ede2abc800 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=72632, stack(0x000000810fb00000,0x000000810fc00000)]

Other Threads:
  0x000001edd8e5c000 VMThread "VM Thread" [stack: 0x000000810af00000,0x000000810b000000] [id=53304]
  0x000001edd9983000 WatcherThread [stack: 0x000000810b900000,0x000000810ba00000] [id=73132]
  0x000001edc05a7000 GCTaskThread "GC Thread#0" [stack: 0x000000810aa00000,0x000000810ab00000] [id=10568]
  0x000001edda07b800 GCTaskThread "GC Thread#1" [stack: 0x000000810ba00000,0x000000810bb00000] [id=73624]
  0x000001edda1a0000 GCTaskThread "GC Thread#2" [stack: 0x000000810bb00000,0x000000810bc00000] [id=73516]
  0x000001eddb149800 GCTaskThread "GC Thread#3" [stack: 0x000000810c600000,0x000000810c700000] [id=73572]
  0x000001edc05ce000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000810ab00000,0x000000810ac00000] [id=68108]
  0x000001edc05d1000 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000810ac00000,0x000000810ad00000] [id=69604]
  0x000001edc0661800 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000810ad00000,0x000000810ae00000] [id=52196]
  0x000001edc0667800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x000000810ae00000,0x000000810af00000] [id=73432]

Threads with active compile tasks:
C2 CompilerThread0 151449 10461       4       org.gradle.internal.instantiation.generator.AbstractClassGenerator::inspectType (560 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 187392K, used 129696K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 57 young (58368K), 10 survivors (10240K)
 Metaspace       used 93395K, capacity 95565K, committed 95820K, reserved 1132544K
  class space    used 12119K, capacity 12911K, committed 12928K, reserved 1048576K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0100000, 0x00000000a0000000| Untracked 
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0200000, 0x00000000a0100000| Untracked 
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| O|  |TAMS 0x00000000a0300000, 0x00000000a0200000| Untracked 
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HS|  |TAMS 0x00000000a0400000, 0x00000000a0300000| Complete 
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0500000, 0x00000000a0400000| Complete 
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%|HC|  |TAMS 0x00000000a0600000, 0x00000000a0500000| Complete 
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0700000, 0x00000000a0600000| Untracked 
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0800000, 0x00000000a0700000| Untracked 
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| O|  |TAMS 0x00000000a0900000, 0x00000000a0800000| Untracked 
|   9|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| O|  |TAMS 0x00000000a0a00000, 0x00000000a0900000| Untracked 
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0b00000, 0x00000000a0a00000| Untracked 
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0c00000, 0x00000000a0b00000| Untracked 
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0d00000, 0x00000000a0c00000| Untracked 
|  13|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| O|  |TAMS 0x00000000a0e00000, 0x00000000a0d00000| Untracked 
|  14|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| O|  |TAMS 0x00000000a0f00000, 0x00000000a0e00000| Untracked 
|  15|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| O|  |TAMS 0x00000000a1000000, 0x00000000a0f00000| Untracked 
|  16|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| O|  |TAMS 0x00000000a1100000, 0x00000000a1000000| Untracked 
|  17|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| O|  |TAMS 0x00000000a1200000, 0x00000000a1100000| Untracked 
|  18|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| O|  |TAMS 0x00000000a1300000, 0x00000000a1200000| Untracked 
|  19|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| O|  |TAMS 0x00000000a1400000, 0x00000000a1300000| Untracked 
|  20|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| O|  |TAMS 0x00000000a1500000, 0x00000000a1400000| Untracked 
|  21|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%|HS|  |TAMS 0x00000000a1600000, 0x00000000a1500000| Complete 
|  22|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%|HS|  |TAMS 0x00000000a1700000, 0x00000000a1600000| Complete 
|  23|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%|HS|  |TAMS 0x00000000a1800000, 0x00000000a1700000| Complete 
|  24|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%|HS|  |TAMS 0x00000000a1900000, 0x00000000a1800000| Complete 
|  25|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%|HC|  |TAMS 0x00000000a1a00000, 0x00000000a1900000| Complete 
|  26|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%|HC|  |TAMS 0x00000000a1b00000, 0x00000000a1a00000| Complete 
|  27|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%|HS|  |TAMS 0x00000000a1c00000, 0x00000000a1b00000| Complete 
|  28|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%|HC|  |TAMS 0x00000000a1d00000, 0x00000000a1c00000| Complete 
|  29|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| O|  |TAMS 0x00000000a1e00000, 0x00000000a1d00000| Untracked 
|  30|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| O|  |TAMS 0x00000000a1f00000, 0x00000000a1e00000| Untracked 
|  31|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| O|  |TAMS 0x00000000a2000000, 0x00000000a1f00000| Untracked 
|  32|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| O|  |TAMS 0x00000000a2100000, 0x00000000a2000000| Untracked 
|  33|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| O|  |TAMS 0x00000000a2200000, 0x00000000a2100000| Untracked 
|  34|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| O|  |TAMS 0x00000000a2300000, 0x00000000a2200000| Untracked 
|  35|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| O|  |TAMS 0x00000000a2400000, 0x00000000a2300000| Untracked 
|  36|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| O|  |TAMS 0x00000000a2500000, 0x00000000a2400000| Untracked 
|  37|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| O|  |TAMS 0x00000000a2600000, 0x00000000a2500000| Untracked 
|  38|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| O|  |TAMS 0x00000000a2700000, 0x00000000a2600000| Untracked 
|  39|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| O|  |TAMS 0x00000000a2800000, 0x00000000a2700000| Untracked 
|  40|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| O|  |TAMS 0x00000000a2900000, 0x00000000a2800000| Untracked 
|  41|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| O|  |TAMS 0x00000000a2a00000, 0x00000000a2900000| Untracked 
|  42|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| O|  |TAMS 0x00000000a2b00000, 0x00000000a2a00000| Untracked 
|  43|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| O|  |TAMS 0x00000000a2c00000, 0x00000000a2b00000| Untracked 
|  44|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| O|  |TAMS 0x00000000a2d00000, 0x00000000a2c00000| Untracked 
|  45|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| O|  |TAMS 0x00000000a2e00000, 0x00000000a2d00000| Untracked 
|  46|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| O|  |TAMS 0x00000000a2f00000, 0x00000000a2e00000| Untracked 
|  47|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| O|  |TAMS 0x00000000a3000000, 0x00000000a2f00000| Untracked 
|  48|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| O|  |TAMS 0x00000000a3093400, 0x00000000a3000000| Untracked 
|  49|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| O|  |TAMS 0x00000000a3100000, 0x00000000a3100000| Untracked 
|  50|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| O|  |TAMS 0x00000000a3200000, 0x00000000a3200000| Untracked 
|  51|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| O|  |TAMS 0x00000000a3300000, 0x00000000a3300000| Untracked 
|  52|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| O|  |TAMS 0x00000000a3400000, 0x00000000a3400000| Untracked 
|  53|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| O|  |TAMS 0x00000000a3500000, 0x00000000a3500000| Untracked 
|  54|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| O|  |TAMS 0x00000000a3600000, 0x00000000a3600000| Untracked 
|  55|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| O|  |TAMS 0x00000000a3700000, 0x00000000a3700000| Untracked 
|  56|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| O|  |TAMS 0x00000000a3800000, 0x00000000a3800000| Untracked 
|  57|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| O|  |TAMS 0x00000000a3900000, 0x00000000a3900000| Untracked 
|  58|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| O|  |TAMS 0x00000000a3a00000, 0x00000000a3a00000| Untracked 
|  59|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| O|  |TAMS 0x00000000a3b00000, 0x00000000a3b00000| Untracked 
|  60|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| O|  |TAMS 0x00000000a3c00000, 0x00000000a3c00000| Untracked 
|  61|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| O|  |TAMS 0x00000000a3d00000, 0x00000000a3d00000| Untracked 
|  62|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| O|  |TAMS 0x00000000a3e00000, 0x00000000a3e00000| Untracked 
|  63|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| O|  |TAMS 0x00000000a3f00000, 0x00000000a3f00000| Untracked 
|  64|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| O|  |TAMS 0x00000000a4000000, 0x00000000a4000000| Untracked 
|  65|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| O|  |TAMS 0x00000000a4100000, 0x00000000a4100000| Untracked 
|  66|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| O|  |TAMS 0x00000000a4200000, 0x00000000a4200000| Untracked 
|  67|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| O|  |TAMS 0x00000000a4300000, 0x00000000a4300000| Untracked 
|  68|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| O|  |TAMS 0x00000000a4400000, 0x00000000a4400000| Untracked 
|  69|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| O|  |TAMS 0x00000000a4500000, 0x00000000a4500000| Untracked 
|  70|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| O|  |TAMS 0x00000000a4600000, 0x00000000a4600000| Untracked 
|  71|0x00000000a4700000, 0x00000000a475ae00, 0x00000000a4800000| 35%| O|  |TAMS 0x00000000a4700000, 0x00000000a4700000| Untracked 
|  72|0x00000000a4800000, 0x00000000a4800000, 0x00000000a4900000|  0%| F|  |TAMS 0x00000000a4800000, 0x00000000a4800000| Untracked 
|  73|0x00000000a4900000, 0x00000000a4900000, 0x00000000a4a00000|  0%| F|  |TAMS 0x00000000a4900000, 0x00000000a4900000| Untracked 
|  74|0x00000000a4a00000, 0x00000000a4a00000, 0x00000000a4b00000|  0%| F|  |TAMS 0x00000000a4a00000, 0x00000000a4a00000| Untracked 
|  75|0x00000000a4b00000, 0x00000000a4b00000, 0x00000000a4c00000|  0%| F|  |TAMS 0x00000000a4b00000, 0x00000000a4b00000| Untracked 
|  76|0x00000000a4c00000, 0x00000000a4c00000, 0x00000000a4d00000|  0%| F|  |TAMS 0x00000000a4c00000, 0x00000000a4c00000| Untracked 
|  77|0x00000000a4d00000, 0x00000000a4d00000, 0x00000000a4e00000|  0%| F|  |TAMS 0x00000000a4d00000, 0x00000000a4d00000| Untracked 
|  78|0x00000000a4e00000, 0x00000000a4e00000, 0x00000000a4f00000|  0%| F|  |TAMS 0x00000000a4e00000, 0x00000000a4e00000| Untracked 
|  79|0x00000000a4f00000, 0x00000000a4f00000, 0x00000000a5000000|  0%| F|  |TAMS 0x00000000a4f00000, 0x00000000a4f00000| Untracked 
|  80|0x00000000a5000000, 0x00000000a5000000, 0x00000000a5100000|  0%| F|  |TAMS 0x00000000a5000000, 0x00000000a5000000| Untracked 
|  81|0x00000000a5100000, 0x00000000a5100000, 0x00000000a5200000|  0%| F|  |TAMS 0x00000000a5100000, 0x00000000a5100000| Untracked 
|  82|0x00000000a5200000, 0x00000000a5200000, 0x00000000a5300000|  0%| F|  |TAMS 0x00000000a5200000, 0x00000000a5200000| Untracked 
|  83|0x00000000a5300000, 0x00000000a5300000, 0x00000000a5400000|  0%| F|  |TAMS 0x00000000a5300000, 0x00000000a5300000| Untracked 
|  84|0x00000000a5400000, 0x00000000a5400000, 0x00000000a5500000|  0%| F|  |TAMS 0x00000000a5400000, 0x00000000a5400000| Untracked 
|  85|0x00000000a5500000, 0x00000000a5500000, 0x00000000a5600000|  0%| F|  |TAMS 0x00000000a5500000, 0x00000000a5500000| Untracked 
|  86|0x00000000a5600000, 0x00000000a5600000, 0x00000000a5700000|  0%| F|  |TAMS 0x00000000a5600000, 0x00000000a5600000| Untracked 
|  87|0x00000000a5700000, 0x00000000a5700000, 0x00000000a5800000|  0%| F|  |TAMS 0x00000000a5700000, 0x00000000a5700000| Untracked 
|  88|0x00000000a5800000, 0x00000000a5800000, 0x00000000a5900000|  0%| F|  |TAMS 0x00000000a5800000, 0x00000000a5800000| Untracked 
|  89|0x00000000a5900000, 0x00000000a5900000, 0x00000000a5a00000|  0%| F|  |TAMS 0x00000000a5900000, 0x00000000a5900000| Untracked 
|  90|0x00000000a5a00000, 0x00000000a5a00000, 0x00000000a5b00000|  0%| F|  |TAMS 0x00000000a5a00000, 0x00000000a5a00000| Untracked 
|  91|0x00000000a5b00000, 0x00000000a5b00000, 0x00000000a5c00000|  0%| F|  |TAMS 0x00000000a5b00000, 0x00000000a5b00000| Untracked 
|  92|0x00000000a5c00000, 0x00000000a5c00000, 0x00000000a5d00000|  0%| F|  |TAMS 0x00000000a5c00000, 0x00000000a5c00000| Untracked 
|  93|0x00000000a5d00000, 0x00000000a5d00000, 0x00000000a5e00000|  0%| F|  |TAMS 0x00000000a5d00000, 0x00000000a5d00000| Untracked 
|  94|0x00000000a5e00000, 0x00000000a5e00000, 0x00000000a5f00000|  0%| F|  |TAMS 0x00000000a5e00000, 0x00000000a5e00000| Untracked 
|  95|0x00000000a5f00000, 0x00000000a5f00000, 0x00000000a6000000|  0%| F|  |TAMS 0x00000000a5f00000, 0x00000000a5f00000| Untracked 
|  96|0x00000000a6000000, 0x00000000a6000000, 0x00000000a6100000|  0%| F|  |TAMS 0x00000000a6000000, 0x00000000a6000000| Untracked 
|  97|0x00000000a6100000, 0x00000000a6100000, 0x00000000a6200000|  0%| F|  |TAMS 0x00000000a6100000, 0x00000000a6100000| Untracked 
|  98|0x00000000a6200000, 0x00000000a6200000, 0x00000000a6300000|  0%| F|  |TAMS 0x00000000a6200000, 0x00000000a6200000| Untracked 
|  99|0x00000000a6300000, 0x00000000a6300000, 0x00000000a6400000|  0%| F|  |TAMS 0x00000000a6300000, 0x00000000a6300000| Untracked 
| 100|0x00000000a6400000, 0x00000000a6400000, 0x00000000a6500000|  0%| F|  |TAMS 0x00000000a6400000, 0x00000000a6400000| Untracked 
| 101|0x00000000a6500000, 0x00000000a6500000, 0x00000000a6600000|  0%| F|  |TAMS 0x00000000a6500000, 0x00000000a6500000| Untracked 
| 102|0x00000000a6600000, 0x00000000a6600000, 0x00000000a6700000|  0%| F|  |TAMS 0x00000000a6600000, 0x00000000a6600000| Untracked 
| 103|0x00000000a6700000, 0x00000000a6700000, 0x00000000a6800000|  0%| F|  |TAMS 0x00000000a6700000, 0x00000000a6700000| Untracked 
| 104|0x00000000a6800000, 0x00000000a6800000, 0x00000000a6900000|  0%| F|  |TAMS 0x00000000a6800000, 0x00000000a6800000| Untracked 
| 105|0x00000000a6900000, 0x00000000a6900000, 0x00000000a6a00000|  0%| F|  |TAMS 0x00000000a6900000, 0x00000000a6900000| Untracked 
| 106|0x00000000a6a00000, 0x00000000a6a4d3a8, 0x00000000a6b00000| 30%| S|CS|TAMS 0x00000000a6a00000, 0x00000000a6a00000| Complete 
| 107|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| S|CS|TAMS 0x00000000a6b00000, 0x00000000a6b00000| Complete 
| 108|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| S|CS|TAMS 0x00000000a6c00000, 0x00000000a6c00000| Complete 
| 109|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| S|CS|TAMS 0x00000000a6d00000, 0x00000000a6d00000| Complete 
| 110|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| S|CS|TAMS 0x00000000a6e00000, 0x00000000a6e00000| Complete 
| 111|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| S|CS|TAMS 0x00000000a6f00000, 0x00000000a6f00000| Complete 
| 112|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| S|CS|TAMS 0x00000000a7000000, 0x00000000a7000000| Complete 
| 113|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| S|CS|TAMS 0x00000000a7100000, 0x00000000a7100000| Complete 
| 114|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| S|CS|TAMS 0x00000000a7200000, 0x00000000a7200000| Complete 
| 115|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| S|CS|TAMS 0x00000000a7300000, 0x00000000a7300000| Complete 
| 116|0x00000000a7400000, 0x00000000a7400000, 0x00000000a7500000|  0%| F|  |TAMS 0x00000000a7400000, 0x00000000a7400000| Untracked 
| 117|0x00000000a7500000, 0x00000000a7500000, 0x00000000a7600000|  0%| F|  |TAMS 0x00000000a7500000, 0x00000000a7500000| Untracked 
| 118|0x00000000a7600000, 0x00000000a7600000, 0x00000000a7700000|  0%| F|  |TAMS 0x00000000a7600000, 0x00000000a7600000| Untracked 
| 119|0x00000000a7700000, 0x00000000a7700000, 0x00000000a7800000|  0%| F|  |TAMS 0x00000000a7700000, 0x00000000a7700000| Untracked 
| 120|0x00000000a7800000, 0x00000000a7800000, 0x00000000a7900000|  0%| F|  |TAMS 0x00000000a7800000, 0x00000000a7800000| Untracked 
| 121|0x00000000a7900000, 0x00000000a7900000, 0x00000000a7a00000|  0%| F|  |TAMS 0x00000000a7900000, 0x00000000a7900000| Untracked 
| 122|0x00000000a7a00000, 0x00000000a7a00000, 0x00000000a7b00000|  0%| F|  |TAMS 0x00000000a7a00000, 0x00000000a7a00000| Untracked 
| 123|0x00000000a7b00000, 0x00000000a7b00000, 0x00000000a7c00000|  0%| F|  |TAMS 0x00000000a7b00000, 0x00000000a7b00000| Untracked 
| 124|0x00000000a7c00000, 0x00000000a7c00000, 0x00000000a7d00000|  0%| F|  |TAMS 0x00000000a7c00000, 0x00000000a7c00000| Untracked 
| 125|0x00000000a7d00000, 0x00000000a7d00000, 0x00000000a7e00000|  0%| F|  |TAMS 0x00000000a7d00000, 0x00000000a7d00000| Untracked 
| 126|0x00000000a7e00000, 0x00000000a7e00000, 0x00000000a7f00000|  0%| F|  |TAMS 0x00000000a7e00000, 0x00000000a7e00000| Untracked 
| 127|0x00000000a7f00000, 0x00000000a7f00000, 0x00000000a8000000|  0%| F|  |TAMS 0x00000000a7f00000, 0x00000000a7f00000| Untracked 
| 128|0x00000000a8000000, 0x00000000a8000000, 0x00000000a8100000|  0%| F|  |TAMS 0x00000000a8000000, 0x00000000a8000000| Untracked 
| 129|0x00000000a8100000, 0x00000000a8100000, 0x00000000a8200000|  0%| F|  |TAMS 0x00000000a8100000, 0x00000000a8100000| Untracked 
| 130|0x00000000a8200000, 0x00000000a8200000, 0x00000000a8300000|  0%| F|  |TAMS 0x00000000a8200000, 0x00000000a8200000| Untracked 
| 131|0x00000000a8300000, 0x00000000a8300000, 0x00000000a8400000|  0%| F|  |TAMS 0x00000000a8300000, 0x00000000a8300000| Untracked 
| 132|0x00000000a8400000, 0x00000000a8400000, 0x00000000a8500000|  0%| F|  |TAMS 0x00000000a8400000, 0x00000000a8400000| Untracked 
| 133|0x00000000a8500000, 0x00000000a8500000, 0x00000000a8600000|  0%| F|  |TAMS 0x00000000a8500000, 0x00000000a8500000| Untracked 
| 134|0x00000000a8600000, 0x00000000a8600000, 0x00000000a8700000|  0%| F|  |TAMS 0x00000000a8600000, 0x00000000a8600000| Untracked 
| 135|0x00000000a8700000, 0x00000000a8700000, 0x00000000a8800000|  0%| F|  |TAMS 0x00000000a8700000, 0x00000000a8700000| Untracked 
| 136|0x00000000a8800000, 0x00000000a8868e48, 0x00000000a8900000| 40%| E|  |TAMS 0x00000000a8800000, 0x00000000a8800000| Complete 
| 137|0x00000000a8900000, 0x00000000a8a00000, 0x00000000a8a00000|100%| E|CS|TAMS 0x00000000a8900000, 0x00000000a8900000| Complete 
| 138|0x00000000a8a00000, 0x00000000a8b00000, 0x00000000a8b00000|100%| E|CS|TAMS 0x00000000a8a00000, 0x00000000a8a00000| Complete 
| 139|0x00000000a8b00000, 0x00000000a8c00000, 0x00000000a8c00000|100%| E|CS|TAMS 0x00000000a8b00000, 0x00000000a8b00000| Complete 
| 140|0x00000000a8c00000, 0x00000000a8d00000, 0x00000000a8d00000|100%| E|CS|TAMS 0x00000000a8c00000, 0x00000000a8c00000| Complete 
| 141|0x00000000a8d00000, 0x00000000a8e00000, 0x00000000a8e00000|100%| E|CS|TAMS 0x00000000a8d00000, 0x00000000a8d00000| Complete 
| 142|0x00000000a8e00000, 0x00000000a8f00000, 0x00000000a8f00000|100%| E|CS|TAMS 0x00000000a8e00000, 0x00000000a8e00000| Complete 
| 143|0x00000000a8f00000, 0x00000000a9000000, 0x00000000a9000000|100%| E|CS|TAMS 0x00000000a8f00000, 0x00000000a8f00000| Complete 
| 144|0x00000000a9000000, 0x00000000a9100000, 0x00000000a9100000|100%| E|CS|TAMS 0x00000000a9000000, 0x00000000a9000000| Complete 
| 145|0x00000000a9100000, 0x00000000a9200000, 0x00000000a9200000|100%| E|CS|TAMS 0x00000000a9100000, 0x00000000a9100000| Complete 
| 146|0x00000000a9200000, 0x00000000a9300000, 0x00000000a9300000|100%| E|CS|TAMS 0x00000000a9200000, 0x00000000a9200000| Complete 
| 147|0x00000000a9300000, 0x00000000a9400000, 0x00000000a9400000|100%| E|CS|TAMS 0x00000000a9300000, 0x00000000a9300000| Complete 
| 148|0x00000000a9400000, 0x00000000a9500000, 0x00000000a9500000|100%| E|CS|TAMS 0x00000000a9400000, 0x00000000a9400000| Complete 
| 149|0x00000000a9500000, 0x00000000a9600000, 0x00000000a9600000|100%| E|CS|TAMS 0x00000000a9500000, 0x00000000a9500000| Complete 
| 150|0x00000000a9600000, 0x00000000a9700000, 0x00000000a9700000|100%| E|CS|TAMS 0x00000000a9600000, 0x00000000a9600000| Complete 
| 151|0x00000000a9700000, 0x00000000a9800000, 0x00000000a9800000|100%| E|CS|TAMS 0x00000000a9700000, 0x00000000a9700000| Complete 
| 152|0x00000000a9800000, 0x00000000a9900000, 0x00000000a9900000|100%| E|CS|TAMS 0x00000000a9800000, 0x00000000a9800000| Complete 
| 153|0x00000000a9900000, 0x00000000a9a00000, 0x00000000a9a00000|100%| E|CS|TAMS 0x00000000a9900000, 0x00000000a9900000| Complete 
| 154|0x00000000a9a00000, 0x00000000a9b00000, 0x00000000a9b00000|100%| E|CS|TAMS 0x00000000a9a00000, 0x00000000a9a00000| Complete 
| 155|0x00000000a9b00000, 0x00000000a9c00000, 0x00000000a9c00000|100%| E|CS|TAMS 0x00000000a9b00000, 0x00000000a9b00000| Complete 
| 156|0x00000000a9c00000, 0x00000000a9d00000, 0x00000000a9d00000|100%| E|CS|TAMS 0x00000000a9c00000, 0x00000000a9c00000| Complete 
| 157|0x00000000a9d00000, 0x00000000a9e00000, 0x00000000a9e00000|100%| E|CS|TAMS 0x00000000a9d00000, 0x00000000a9d00000| Complete 
| 158|0x00000000a9e00000, 0x00000000a9f00000, 0x00000000a9f00000|100%| E|CS|TAMS 0x00000000a9e00000, 0x00000000a9e00000| Complete 
| 159|0x00000000a9f00000, 0x00000000aa000000, 0x00000000aa000000|100%| E|CS|TAMS 0x00000000a9f00000, 0x00000000a9f00000| Complete 
| 160|0x00000000aa000000, 0x00000000aa100000, 0x00000000aa100000|100%| E|CS|TAMS 0x00000000aa000000, 0x00000000aa000000| Complete 
| 161|0x00000000aa100000, 0x00000000aa200000, 0x00000000aa200000|100%| E|CS|TAMS 0x00000000aa100000, 0x00000000aa100000| Complete 
| 162|0x00000000aa200000, 0x00000000aa300000, 0x00000000aa300000|100%| E|CS|TAMS 0x00000000aa200000, 0x00000000aa200000| Complete 
| 163|0x00000000aa300000, 0x00000000aa400000, 0x00000000aa400000|100%| E|CS|TAMS 0x00000000aa300000, 0x00000000aa300000| Complete 
| 164|0x00000000aa400000, 0x00000000aa500000, 0x00000000aa500000|100%| E|CS|TAMS 0x00000000aa400000, 0x00000000aa400000| Complete 
| 165|0x00000000aa500000, 0x00000000aa600000, 0x00000000aa600000|100%| E|CS|TAMS 0x00000000aa500000, 0x00000000aa500000| Complete 
| 166|0x00000000aa600000, 0x00000000aa700000, 0x00000000aa700000|100%| E|CS|TAMS 0x00000000aa600000, 0x00000000aa600000| Complete 
| 167|0x00000000aa700000, 0x00000000aa800000, 0x00000000aa800000|100%| E|CS|TAMS 0x00000000aa700000, 0x00000000aa700000| Complete 
| 168|0x00000000aa800000, 0x00000000aa900000, 0x00000000aa900000|100%| E|CS|TAMS 0x00000000aa800000, 0x00000000aa800000| Complete 
| 169|0x00000000aa900000, 0x00000000aaa00000, 0x00000000aaa00000|100%| E|CS|TAMS 0x00000000aa900000, 0x00000000aa900000| Complete 
| 170|0x00000000aaa00000, 0x00000000aab00000, 0x00000000aab00000|100%| E|CS|TAMS 0x00000000aaa00000, 0x00000000aaa00000| Complete 
| 171|0x00000000aab00000, 0x00000000aac00000, 0x00000000aac00000|100%| E|CS|TAMS 0x00000000aab00000, 0x00000000aab00000| Complete 
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| E|CS|TAMS 0x00000000aac00000, 0x00000000aac00000| Complete 
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| E|CS|TAMS 0x00000000aad00000, 0x00000000aad00000| Complete 
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| E|CS|TAMS 0x00000000aae00000, 0x00000000aae00000| Complete 
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| E|CS|TAMS 0x00000000aaf00000, 0x00000000aaf00000| Complete 
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| E|CS|TAMS 0x00000000ab000000, 0x00000000ab000000| Complete 
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| E|CS|TAMS 0x00000000ab100000, 0x00000000ab100000| Complete 
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| E|CS|TAMS 0x00000000ab200000, 0x00000000ab200000| Complete 
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| E|CS|TAMS 0x00000000ab300000, 0x00000000ab300000| Complete 
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| E|CS|TAMS 0x00000000ab400000, 0x00000000ab400000| Complete 
| 181|0x00000000ab500000, 0x00000000ab600000, 0x00000000ab600000|100%| E|CS|TAMS 0x00000000ab500000, 0x00000000ab500000| Complete 
| 182|0x00000000ab600000, 0x00000000ab700000, 0x00000000ab700000|100%| E|CS|TAMS 0x00000000ab600000, 0x00000000ab600000| Complete 

Card table byte_map: [0x000001edd33a0000,0x000001edd36a0000] _byte_map_base: 0x000001edd2ea0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001edc05c7238, (CMBitMap*) 0x000001edc05c7270
 Prev Bits: [0x000001edd39a0000, 0x000001edd51a0000)
 Next Bits: [0x000001edd51a0000, 0x000001edd69a0000)

Polling page: 0x000001edbfd70000

Metaspace:

Usage:
  Non-class:     80.72 MB capacity,    79.39 MB ( 98%) used,     1.09 MB (  1%) free+waste,   251.13 KB ( <1%) overhead. 
      Class:     12.64 MB capacity,    11.84 MB ( 94%) used,   708.35 KB (  5%) free+waste,   113.38 KB ( <1%) overhead. 
       Both:     93.36 MB capacity,    91.22 MB ( 98%) used,     1.78 MB (  2%) free+waste,   364.50 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       82.00 MB reserved,      80.95 MB ( 99%) committed 
      Class space:        1.00 GB reserved,      12.75 MB (  1%) committed 
             Both:        1.08 GB reserved,      93.70 MB (  8%) committed 

Chunk freelists:
   Non-Class:  46.00 KB
       Class:  17.00 KB
        Both:  63.00 KB

MaxMetaspaceSize: 17179869184.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 20.80 MB
Current GC threshold: 108.46 MB
CDS: off

CodeHeap 'non-profiled nmethods': size=120064Kb used=5229Kb max_used=5229Kb free=114835Kb
 bounds [0x000001edcbb60000, 0x000001edcc080000, 0x000001edd30a0000]
CodeHeap 'profiled nmethods': size=120000Kb used=22407Kb max_used=22407Kb free=97592Kb
 bounds [0x000001edc4630000, 0x000001edc5c20000, 0x000001edcbb60000]
CodeHeap 'non-nmethods': size=5696Kb used=2381Kb max_used=2430Kb free=3314Kb
 bounds [0x000001edc40a0000, 0x000001edc4310000, 0x000001edc4630000]
 total_blobs=11302 nmethods=10405 adapters=807
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 151.394 Thread 0x000001edd8eab800 10502       1       org.gradle.api.tasks.util.PatternSet::isCaseSensitive (5 bytes)
Event: 151.394 Thread 0x000001edd8eab800 nmethod 10502 0x000001edcc078d10 code [0x000001edcc078ec0, 0x000001edcc078f78]
Event: 151.395 Thread 0x000001edd8eab800 10503       2       java.util.HashMap$Values::size (8 bytes)
Event: 151.395 Thread 0x000001edd8eab800 nmethod 10503 0x000001edc5c06890 code [0x000001edc5c06a40, 0x000001edc5c06b38]
Event: 151.395 Thread 0x000001edd8eab800 10504       2       org.gradle.api.internal.tasks.properties.DefaultTypeMetadataStore::determinePropertyType (48 bytes)
Event: 151.395 Thread 0x000001edd8eab800 nmethod 10504 0x000001edc5c06c10 code [0x000001edc5c06e00, 0x000001edc5c07028]
Event: 151.396 Thread 0x000001edd8eab800 10505       1       com.google.common.collect.ImmutableSortedSet::comparator (5 bytes)
Event: 151.396 Thread 0x000001edd8eab800 nmethod 10505 0x000001edcc079010 code [0x000001edcc0791c0, 0x000001edcc079278]
Event: 151.397 Thread 0x000001edd8eab800 10506       2       org.gradle.api.internal.tasks.properties.DefaultTypeMetadataStore$DefaultPropertyMetadata::<init> (15 bytes)
Event: 151.397 Thread 0x000001edd8eab800 nmethod 10506 0x000001edc5c07190 code [0x000001edc5c07340, 0x000001edc5c074d8]
Event: 151.397 Thread 0x000001edd8eab800 10507       2       com.google.common.collect.ImmutableSortedSet$Builder::sortAndDedup (165 bytes)
Event: 151.398 Thread 0x000001edd8eab800 nmethod 10507 0x000001edc5c07590 code [0x000001edc5c07820, 0x000001edc5c080b8]
Event: 151.398 Thread 0x000001edd8eab800 10508       2       com.google.common.base.Suppliers$NonSerializableMemoizingSupplier::<init> (16 bytes)
Event: 151.398 Thread 0x000001edd8eab800 nmethod 10508 0x000001edc5c08610 code [0x000001edc5c087e0, 0x000001edc5c08a48]
Event: 151.398 Thread 0x000001edd8eab800 10509       2       org.gradle.api.internal.DefaultNamedDomainObjectCollection$AbstractDomainObjectCreatingProvider::calculateOwnValue (71 bytes)
Event: 151.399 Thread 0x000001edd8eab800 nmethod 10509 0x000001edc5c08b90 code [0x000001edc5c08dc0, 0x000001edc5c093a8]
Event: 151.399 Thread 0x000001edd8eab800 10510   !   2       jdk.internal.reflect.GeneratedConstructorAccessor40::newInstance (53 bytes)
Event: 151.400 Thread 0x000001edd8eab800 nmethod 10510 0x000001edc5c09690 code [0x000001edc5c098c0, 0x000001edc5c09e28]
Event: 151.400 Thread 0x000001edd8eab800 10511   !   2       com.sun.proxy.$Proxy56::annotationType (29 bytes)
Event: 151.400 Thread 0x000001edd8eab800 nmethod 10511 0x000001edc5c0a210 code [0x000001edc5c0a3e0, 0x000001edc5c0a638]

GC Heap History (20 events):
Event: 12.844 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 155648K, used 120340K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 91 young (93184K), 9 survivors (9216K)
 Metaspace       used 52250K, capacity 53511K, committed 53580K, reserved 1095680K
  class space    used 6765K, capacity 7274K, committed 7296K, reserved 1048576K
}
Event: 12.870 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 155648K, used 48761K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 52250K, capacity 53511K, committed 53580K, reserved 1095680K
  class space    used 6765K, capacity 7274K, committed 7296K, reserved 1048576K
}
Event: 14.267 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 155648K, used 114297K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 76 young (77824K), 12 survivors (12288K)
 Metaspace       used 57490K, capacity 58923K, committed 59084K, reserved 1099776K
  class space    used 7538K, capacity 8116K, committed 8192K, reserved 1048576K
}
Event: 14.283 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 155648K, used 52689K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 57490K, capacity 58923K, committed 59084K, reserved 1099776K
  class space    used 7538K, capacity 8116K, committed 8192K, reserved 1048576K
}
Event: 14.372 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 155648K, used 70097K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 3 survivors (3072K)
 Metaspace       used 58790K, capacity 60171K, committed 60236K, reserved 1101824K
  class space    used 7714K, capacity 8276K, committed 8320K, reserved 1048576K
}
Event: 14.378 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 155648K, used 52371K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 58790K, capacity 60171K, committed 60236K, reserved 1101824K
  class space    used 7714K, capacity 8276K, committed 8320K, reserved 1048576K
}
Event: 15.513 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 155648K, used 117907K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 65 young (66560K), 3 survivors (3072K)
 Metaspace       used 64442K, capacity 65947K, committed 65996K, reserved 1105920K
  class space    used 8355K, capacity 8940K, committed 8960K, reserved 1048576K
}
Event: 15.528 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 155648K, used 55543K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 64442K, capacity 65947K, committed 65996K, reserved 1105920K
  class space    used 8355K, capacity 8940K, committed 8960K, reserved 1048576K
}
Event: 17.289 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 155648K, used 118007K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 68 young (69632K), 6 survivors (6144K)
 Metaspace       used 71833K, capacity 73435K, committed 73548K, reserved 1114112K
  class space    used 9202K, capacity 9833K, committed 9856K, reserved 1048576K
}
Event: 17.306 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 155648K, used 60023K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 71833K, capacity 73435K, committed 73548K, reserved 1114112K
  class space    used 9202K, capacity 9833K, committed 9856K, reserved 1048576K
}
Event: 17.971 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 155648K, used 122487K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 68 young (69632K), 8 survivors (8192K)
 Metaspace       used 75101K, capacity 76860K, committed 77132K, reserved 1116160K
  class space    used 9648K, capacity 10286K, committed 10368K, reserved 1048576K
}
Event: 17.987 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 155648K, used 63925K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 75101K, capacity 76860K, committed 77132K, reserved 1116160K
  class space    used 9648K, capacity 10286K, committed 10368K, reserved 1048576K
}
Event: 18.518 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 155648K, used 122293K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 66 young (67584K), 9 survivors (9216K)
 Metaspace       used 77250K, capacity 79044K, committed 79180K, reserved 1118208K
  class space    used 9920K, capacity 10584K, committed 10624K, reserved 1048576K
}
Event: 18.535 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 155648K, used 68907K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 77250K, capacity 79044K, committed 79180K, reserved 1118208K
  class space    used 9920K, capacity 10584K, committed 10624K, reserved 1048576K
}
Event: 19.362 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 155648K, used 123179K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 62 young (63488K), 9 survivors (9216K)
 Metaspace       used 84151K, capacity 85973K, committed 86220K, reserved 1124352K
  class space    used 10994K, capacity 11689K, committed 11776K, reserved 1048576K
}
Event: 19.382 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 155648K, used 73121K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 84151K, capacity 85973K, committed 86220K, reserved 1124352K
  class space    used 10994K, capacity 11689K, committed 11776K, reserved 1048576K
}
Event: 20.082 GC heap before
{Heap before GC invocations=22 (full 0):
 garbage-first heap   total 155648K, used 125345K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 56 young (57344K), 5 survivors (5120K)
 Metaspace       used 87671K, capacity 89608K, committed 89804K, reserved 1126400K
  class space    used 11437K, capacity 12195K, committed 12288K, reserved 1048576K
}
Event: 20.095 GC heap after
{Heap after GC invocations=23 (full 0):
 garbage-first heap   total 187392K, used 76800K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 87671K, capacity 89608K, committed 89804K, reserved 1126400K
  class space    used 11437K, capacity 12195K, committed 12288K, reserved 1048576K
}
Event: 149.440 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 187392K, used 144384K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 74 young (75776K), 7 survivors (7168K)
 Metaspace       used 89729K, capacity 91807K, committed 91980K, reserved 1128448K
  class space    used 11619K, capacity 12365K, committed 12416K, reserved 1048576K
}
Event: 149.525 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 187392K, used 82592K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 89729K, capacity 91807K, committed 91980K, reserved 1128448K
  class space    used 11619K, capacity 12365K, committed 12416K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 151.181 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc588e3b7 sp=0x000000810bff8a50
Event: 151.181 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff7f10 mode 0
Event: 151.182 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc588e3b7 sp=0x000000810bff8a50
Event: 151.182 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff7f10 mode 0
Event: 151.182 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc588e3b7 sp=0x000000810bff8a50
Event: 151.182 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff7f10 mode 0
Event: 151.182 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc588e3b7 sp=0x000000810bff8a50
Event: 151.182 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff7f10 mode 0
Event: 151.183 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc588e3b7 sp=0x000000810bff8a50
Event: 151.183 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff7f10 mode 0
Event: 151.183 Thread 0x000001edde251800 Uncommon trap: trap_request=0xffffff4d fr.pc=0x000001edcbe9eb44 relative=0x00000000000000a4
Event: 151.183 Thread 0x000001edde251800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001edcbe9eb44 method=java.lang.CharacterDataLatin1.digit(II)I @ 12 c2
Event: 151.183 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edcbe9eb44 sp=0x000000810bff8db0
Event: 151.183 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea1af sp=0x000000810bff8d28 mode 2
Event: 151.260 Thread 0x000001edde251800 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x000001edcc033d28 relative=0x0000000000001408
Event: 151.260 Thread 0x000001edde251800 Uncommon trap: reason=null_check action=make_not_entrant pc=0x000001edcc033d28 method=org.gradle.cache.internal.DefaultCrossBuildInMemoryCacheFactory$DefaultCrossBuildInMemoryCache.maybeGetRetainedValue(Ljava/lang/Object;)Ljava/lang/Object; @ 10 c2
Event: 151.260 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edcc033d28 sp=0x000000810bffa5e0
Event: 151.260 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea1af sp=0x000000810bffa568 mode 2
Event: 151.285 Thread 0x000001edde251800 DEOPT PACKING pc=0x000001edc5188b4d sp=0x000000810bff9580
Event: 151.285 Thread 0x000001edde251800 DEOPT UNPACKING pc=0x000001edc40ea95e sp=0x000000810bff8b20 mode 0

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 20.812 Thread 0x000001edda77e000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aaae16c8}: <clinit>> (0x00000000aaae16c8) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 20.818 Thread 0x000001edda77e000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa9067d0}: <clinit>> (0x00000000aa9067d0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 20.828 Thread 0x000001edda77e000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa91eb10}: <clinit>> (0x00000000aa91eb10) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 20.845 Thread 0x000001edda77e000 Exception <a 'java/lang/NoSuchMethodError'{0x00000000aa936ab0}: <clinit>> (0x00000000aa936ab0) thrown at [./src/hotspot/share/prims/jni.cpp, line 1365]
Event: 23.495 Thread 0x000001edda77d000 Implicit null exception at 0x000001edcbb935f7 to 0x000001edcbb93af0
Event: 23.495 Thread 0x000001edda77d000 Implicit null exception at 0x000001edcbeef974 to 0x000001edcbeeff90
Event: 146.758 Thread 0x000001edde251800 Implicit null exception at 0x000001edcbf7a226 to 0x000001edcbf7cc70
Event: 148.746 Thread 0x000001edde251800 Implicit null exception at 0x000001edcbff06e1 to 0x000001edcbff11e0
Event: 148.918 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a7bdece8}> (0x00000000a7bdece8) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 149.703 Thread 0x000001edde251800 Implicit null exception at 0x000001edcc026cc6 to 0x000001edcc0273e4
Event: 149.807 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ab0edf58}> (0x00000000ab0edf58) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 150.253 Thread 0x000001edde251800 Exception <a 'java/lang/NullPointerException'{0x00000000aa696e78}> (0x00000000aa696e78) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 1440]
Event: 150.653 Thread 0x000001edde251800 Implicit null exception at 0x000001edcbed86ef to 0x000001edcbeda758
Event: 151.113 Thread 0x000001edde251800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a944da98}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000a944da98) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]
Event: 151.167 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a9353948}> (0x00000000a9353948) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 151.168 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a9354cd8}> (0x00000000a9354cd8) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 151.168 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a93560a0}> (0x00000000a93560a0) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 151.168 Thread 0x000001edde251800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a9357468}> (0x00000000a9357468) thrown at [./src/hotspot/share/prims/jni.cpp, line 615]
Event: 151.260 Thread 0x000001edde251800 Implicit null exception at 0x000001edcc032a60 to 0x000001edcc033d1c
Event: 151.345 Thread 0x000001edde251800 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a8d07ee8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, int, int, java.lang.Object)'> (0x00000000a8d07ee8) thrown at [./src/hotspot/share/interpreter/linkResolver.cpp, line 773]

Events (20 events):
Event: 151.361 loading class kotlin/text/StringsKt__IndentKt$prependIndent$1
Event: 151.361 loading class kotlin/text/StringsKt__IndentKt$prependIndent$1 done
Event: 151.362 loading class kotlin/text/DelimitedRangesSequence$iterator$1
Event: 151.362 loading class kotlin/text/DelimitedRangesSequence$iterator$1 done
Event: 151.366 loading class org/jetbrains/kotlin/gradle/plugin/android/AndroidGradleWrapper
Event: 151.366 loading class org/jetbrains/kotlin/gradle/plugin/android/AndroidGradleWrapper done
Event: 151.367 loading class org/jetbrains/kotlin/gradle/plugin/android/AndroidGradleWrapperKt
Event: 151.367 loading class org/jetbrains/kotlin/gradle/plugin/android/AndroidGradleWrapperKt done
Event: 151.381 loading class org/jetbrains/kotlin/gradle/tasks/CompilerPluginOptions
Event: 151.381 loading class org/jetbrains/kotlin/gradle/tasks/CompilerPluginOptions done
Event: 151.383 loading class org/jetbrains/kotlin/gradle/plugin/KaptJavacOptionsDelegate
Event: 151.383 loading class org/jetbrains/kotlin/gradle/plugin/KaptJavacOptionsDelegate done
Event: 151.383 loading class org/jetbrains/kotlin/gradle/dsl/KaptJavacOption
Event: 151.383 loading class org/jetbrains/kotlin/gradle/dsl/KaptJavacOption done
Event: 151.385 loading class org/jetbrains/kotlin/gradle/internal/SubpluginUtilsKt
Event: 151.385 loading class org/jetbrains/kotlin/gradle/internal/SubpluginUtilsKt done
Event: 151.387 loading class org/jetbrains/kotlin/gradle/internal/SubpluginUtilsKt$wrapPluginOptions$encodedOptions$1
Event: 151.387 loading class org/jetbrains/kotlin/gradle/internal/SubpluginUtilsKt$wrapPluginOptions$encodedOptions$1 done
Event: 151.393 loading class org/gradle/api/artifacts/ModuleDependency
Event: 151.393 loading class org/gradle/api/artifacts/ModuleDependency done


Dynamic libraries:
0x00007ff745f00000 - 0x00007ff745f0a000 	C:\Program Files\Android\Android Studio\jre\bin\java.exe
0x00007ffa83800000 - 0x00007ffa83a09000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa82f50000 - 0x00007ffa8300d000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa80ce0000 - 0x00007ffa8105d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa81120000 - 0x00007ffa81231000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa6eaa0000 - 0x00007ffa6eab9000 	C:\Program Files\Android\Android Studio\jre\bin\jli.dll
0x00007ffa67ec0000 - 0x00007ffa67ed7000 	C:\Program Files\Android\Android Studio\jre\bin\VCRUNTIME140.dll
0x00007ffa81740000 - 0x00007ffa818ed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa812e0000 - 0x00007ffa81306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa818f0000 - 0x00007ffa81919000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa81400000 - 0x00007ffa81518000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa727d0000 - 0x00007ffa72a75000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffa81690000 - 0x00007ffa81733000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa81240000 - 0x00007ffa812dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa83270000 - 0x00007ffa832a1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa45770000 - 0x00007ffa4580d000 	C:\Program Files\Android\Android Studio\jre\bin\msvcp140.dll
0x00007ff9fd7b0000 - 0x00007ff9fe295000 	C:\Program Files\Android\Android Studio\jre\bin\server\jvm.dll
0x00007ffa82aa0000 - 0x00007ffa82b4e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa83490000 - 0x00007ffa8352e000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa83360000 - 0x00007ffa83480000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa83480000 - 0x00007ffa83488000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa7c210000 - 0x00007ffa7c21a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa7c370000 - 0x00007ffa7c3a3000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa52a90000 - 0x00007ffa52a99000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffa82a20000 - 0x00007ffa82a8f000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa7fde0000 - 0x00007ffa7fdf8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa6c4f0000 - 0x00007ffa6c501000 	C:\Program Files\Android\Android Studio\jre\bin\verify.dll
0x00007ffa7e820000 - 0x00007ffa7ea41000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa7b770000 - 0x00007ffa7b7a1000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa81310000 - 0x00007ffa8138f000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa6bcc0000 - 0x00007ffa6bce9000 	C:\Program Files\Android\Android Studio\jre\bin\java.dll
0x00007ffa7b850000 - 0x00007ffa7b85b000 	C:\Program Files\Android\Android Studio\jre\bin\jimage.dll
0x00007ffa6c430000 - 0x00007ffa6c448000 	C:\Program Files\Android\Android Studio\jre\bin\zip.dll
0x00007ffa81f80000 - 0x00007ffa82738000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa7ee50000 - 0x00007ffa7f6b8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa82bd0000 - 0x00007ffa82f49000 	C:\WINDOWS\System32\combase.dll
0x00007ffa7ece0000 - 0x00007ffa7ee46000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa81920000 - 0x00007ffa81a0a000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa836e0000 - 0x00007ffa8373d000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa80c10000 - 0x00007ffa80c31000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa6bca0000 - 0x00007ffa6bcba000 	C:\Program Files\Android\Android Studio\jre\bin\net.dll
0x00007ffa7c240000 - 0x00007ffa7c34c000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa80210000 - 0x00007ffa80277000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa6bc80000 - 0x00007ffa6bc94000 	C:\Program Files\Android\Android Studio\jre\bin\nio.dll
0x00007ffa60750000 - 0x00007ffa60777000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa420f0000 - 0x00007ffa42234000 	C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64\native-platform-file-events.dll
0x00007ffa79f00000 - 0x00007ffa79f0a000 	C:\Program Files\Android\Android Studio\jre\bin\management.dll
0x00007ffa79cd0000 - 0x00007ffa79cdd000 	C:\Program Files\Android\Android Studio\jre\bin\management_ext.dll
0x00007ffa80450000 - 0x00007ffa80468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa7fd40000 - 0x00007ffa7fd75000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa80300000 - 0x00007ffa80329000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa805d0000 - 0x00007ffa805f7000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa80470000 - 0x00007ffa8047c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa7f950000 - 0x00007ffa7f97d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa82a90000 - 0x00007ffa82a99000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa7c220000 - 0x00007ffa7c239000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa7c6b0000 - 0x00007ffa7c6ce000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa7f980000 - 0x00007ffa7fa68000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jre\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio\jre\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\5664cfc778a61ccfe75a443a1ab52a65af34e5dc3c78e0209fed803814484fcb\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx1536m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.3.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.3.3-all\4295vidhdd9hd3gbjyw1xqxpo\gradle-7.3.3\lib\gradle-launcher-7.3.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 132120576                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5830732                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122913754                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122913754                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-********
PATH=C:\Python310\Scripts\;C:\Python310\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-********\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=prajo
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 12, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 22000 (10.0.22000.708)
OS uptime: 9 days 1:14 hours

CPU:total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 12 microcode 0xec, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 8026M (321M free)
TotalPageFile size 32602M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 280M, peak: 390M
current process commit charge ("private bytes"): 458M, peak: 462M

vm_info: OpenJDK 64-Bit Server VM (11.0.12+7-b1504.28-7817840) for windows-amd64 JRE (11.0.12+7-b1504.28-7817840), built on Oct 13 2021 22:12:33 by "builder" with MS VC++ 14.0 (VS2015)

END.
